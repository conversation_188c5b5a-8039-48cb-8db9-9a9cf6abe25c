#!/bin/bash

# 获取当前打包路径（兼容Vite默认配置）
PROJECT_ROOT=$(pwd)
DIST_PATH="$PROJECT_ROOT/dist"

# 服务器列表配置
declare -A SERVERS=(
    [61]="************"
    [62]="************"
)

# 构建阶段
echo "▶️ 开始构建项目..."
pnpm run build:prod || { echo "❌ 构建失败"; exit 1; }

# 验证dist目录
if [ ! -d "$DIST_PATH" ]; then
    echo "❌ 错误：未找到dist目录，请检查构建输出路径"
    exit 1
fi

# 部署函数
deploy_server() {
    local server_ip=$1
    echo "🔁 正在部署到服务器 $server_ip..."

    # 清理旧文件
    echo "🧹 清理旧部署文件..."
    ssh wangkaidong@$server_ip "rm -rf /data/server/www/3dgs-viewer-h5/dist" && echo "✅ 旧文件清理完成"

    # 增量传输（使用rsync优化大文件传输）
    echo "🚚 传输新文件..."
    rsync -az --progress -e ssh $DIST_PATH/ wangkaidong@$server_ip:/data/server/www/3dgs-viewer-h5/dist/ && echo "✅ 文件传输完成"
}

# 批量部署
for server_id in "${!SERVERS[@]}"; do
    deploy_server ${SERVERS[$server_id]}
done

echo "🎉 所有服务器部署完成！"