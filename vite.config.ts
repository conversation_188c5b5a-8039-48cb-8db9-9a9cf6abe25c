import { defineConfig } from 'vite';
import { resolve } from 'path';
import postcss from '@vituum/vite-plugin-postcss';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import glsl from 'vite-plugin-glsl';

export default defineConfig({
    plugins: [
        postcss(),
        createSvgIconsPlugin({
            iconDirs: [`${resolve(__dirname, 'src/reall3d/assets/icons')}`],
            symbolId: 'svgicon-[name]',
        }),
        glsl({ include: ['**/*.glsl'] }),
    ],
    server: {
        port: 3100,
        open: true,
        host: '0.0.0.0',
    },
    preview: {
        port: 4100,
    },
    base: './',
    publicDir: 'public',

    esbuild: {
        pure: ['console.log', 'console.debug'],
    },

    build: {
        chunkSizeWarningLimit: 2048,
        sourcemap: false,
        rollupOptions: {
            output: {
                chunkFileNames: 'assets/chunk-[hash].js',
                entryFileNames: 'assets/entry-[hash].js',
                assetFileNames: 'assets/asset-[hash].[ext]',
            },
        },
    },
});
