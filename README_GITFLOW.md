# Git 双远程仓库工作流程

本文档描述了如何同时使用 GitHub 和 GitLab 两个远程仓库的工作流程，以实现既可以从原有 GitHub 仓库更新变更，又可以开发自己的内容并提交到 GitLab 仓库。

## 初始设置

已完成的设置：

-   GitHub 仓库作为 `origin`远程仓库
-   GitLab 仓库作为 `gitlab`远程仓库

查看当前远程仓库配置：

```bash
git remote -v
```

输出应类似于：

```
gitlab  https://gitlab.bdnrc.org.cn/bdnrc-platform/bdnrc-platform-fe/3dgs-viewer.git (fetch)
gitlab  https://gitlab.bdnrc.org.cn/bdnrc-platform/bdnrc-platform-fe/3dgs-viewer.git (push)
origin  https://github.com/reall3d-com/Reall3dViewer.git (fetch)
origin  https://github.com/reall3d-com/Reall3dViewer.git (push)
```

## 日常开发流程

### 1. 从 GitHub 获取最新更新

当您想从原始 GitHub 仓库获取最新更新时：

```bash
# 获取原始仓库的最新代码
git fetch origin

# 将原始仓库的更新合并到当前分支
git merge origin/main
```

### 2. 进行您自己的开发和修改

在本地进行代码修改和开发。

### 3. 提交您的更改

```bash
# 添加修改的文件到暂存区
git add .

# 提交您的修改
git commit -m "您的提交信息"
```

### 4. 将更改推送到 GitLab

```bash
# 将您的更改推送到GitLab仓库
git push gitlab main
```

## 高级工作流程

### 创建自己的功能分支

如果您想在独立的分支上开发新功能：

```bash
# 创建并切换到新分支
git checkout -b 您的分支名

# 开发完成后，提交更改
git add .
git commit -m "您的提交信息"

# 将分支推送到GitLab
git push gitlab 您的分支名
```

### 处理原始 GitHub 仓库的重大更新

当原始仓库有重大更新时：

```bash
# 获取原始仓库的最新代码
git fetch origin

# 将原始仓库的更新合并到当前分支
git merge origin/main

# 解决可能的冲突
# 如果有冲突，git会提示您手动解决这些冲突

# 冲突解决后，提交更改
git add .
git commit -m "解决与上游仓库的冲突"

# 将更新后的代码推送到GitLab
git push gitlab main
```

## 注意事项

1. 如果您对代码进行了修改，可能会与原始仓库的更新产生冲突。遇到冲突时，您需要手动解决这些冲突，然后再继续合并。
2. 定期从原始仓库获取更新可以减少冲突的发生和严重程度。
3. 在进行重大修改前，建议先创建一个新的分支，以便于管理代码和解决可能的冲突。
4. 推送到 GitLab 前，确保您的代码已经过测试，避免将有问题的代码推送到远程仓库。
