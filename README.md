# 3DGS Viewer H5

> 基于Vue3+TypeScript+Three.js的3D高斯散射(3DGS)模型查看器H5应用，支持iframe和微信webview嵌入。

## 📋 目录

- [项目简介](#项目简介)
- [功能特性](#功能特性)
- [技术栈](#技术栈)
- [目录结构](#目录结构)
- [快速开始](#快速开始)
- [使用方法](#使用方法)
- [配置说明](#配置说明)
- [开发指南](#开发指南)
- [部署说明](#部署说明)
- [贡献指南](#贡献指南)

## 🚀 项目简介

3DGS Viewer H5是一个专为移动端和嵌入式场景设计的3D高斯散射模型查看器。项目采用现代化的前端技术栈，支持多项目管理和URL参数化模型加载，特别适用于iframe嵌入和微信webview等场景。

### 支持的项目

- **village-mini** - Village Mini项目展示
- **unicity-mini** - Unicity Mini项目展示
- **unicity-web** - Unicity Web项目展示

## ✨ 功能特性

- 🎯 **多项目支持** - 通过路由区分不同项目，支持项目间独立配置
- 📱 **移动端优化** - 专为H5环境设计，支持iframe和微信webview嵌入
- 🔗 **URL参数化** - 支持通过Base64编码的URL参数传递自定义模型地址
- ⚡ **高性能渲染** - 基于Three.js和WebGL的高效3DGS渲染
- 🎨 **可定制配置** - 每个项目支持独立的背景色、水印等配置
- 📦 **TypeScript支持** - 完整的类型定义，提供更好的开发体验
- 🛠️ **现代化工具链** - ESLint、Prettier、Husky、Commitlint等开发工具集成

## 🛠️ 技术栈

- **前端框架**: Vue 3.4+ (Composition API)
- **开发语言**: TypeScript 5.0+
- **3D渲染**: Three.js (via @reall3d/reall3dviewer)
- **构建工具**: Vite 5.0+
- **路由管理**: Vue Router 4.0+
- **代码规范**: ESLint 8.4.1 + Prettier
- **包管理器**: pnpm (快速、节省空间的依赖管理)
- **Git钩子**: Husky + lint-staged + commitlint

## 📁 目录结构

```
3dgs-viewer-h5/
├── public/                          # 静态资源
│   └── favicon.ico
├── src/                             # 源码目录
│   ├── core/                        # 核心组件
│   │   └── ViewerContainer.vue      # 3DGS Viewer容器组件
│   ├── composables/                 # Vue3 Composition API
│   │   └── useViewer.ts            # Viewer管理逻辑
│   ├── configs/                     # 配置文件
│   │   └── projects.ts             # 项目配置管理
│   ├── types/                       # TypeScript类型定义
│   │   └── project.ts              # 项目相关接口定义
│   ├── services/                    # 服务层
│   │   └── scene.ts                # 场景相关服务
│   ├── utils/                       # 工具函数
│   │   ├── url.ts                  # URL编码解码工具
│   │   ├── http.ts                 # HTTP请求工具
│   │   ├── sceneLoader.ts          # 场景加载器
│   │   └── sceneParser.ts          # 场景解析器
│   ├── views/                       # 页面组件
│   │   └── ProjectView.vue         # 动态项目页面
│   ├── router/                      # 路由配置
│   │   └── index.ts                # Vue Router配置
│   ├── assets/                      # 资源文件
│   ├── App.vue                      # 根组件
│   └── main.ts                      # 应用入口
├── libs/                            # 本地依赖库
│   └── 3dgs-viewer/                # 3DGS查看器库
├── .husky/                          # Git钩子配置
├── package.json                     # 项目依赖配置
├── vite.config.ts                   # Vite构建配置
├── tsconfig.json                    # TypeScript配置
├── eslint.config.ts                 # ESLint配置
└── commitlint.config.js            # Commit规范配置
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0 (推荐使用 `npm install -g pnpm` 安装)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd 3dgs-viewer-h5

# 安装依赖
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

访问 `http://localhost:5173` 即可查看应用。

### 构建生产版本

```bash
pnpm build
```

## 📖 使用方法

### 基本访问

```bash
# 默认访问（自动重定向到village-mini）
http://localhost:5173/

# 访问特定项目
http://localhost:5173/project/village-mini
http://localhost:5173/project/unicity-mini
http://localhost:5173/project/unicity-web

# 兼容性路由（自动重定向）
http://localhost:5173/village-mini
http://localhost:5173/unicity-mini
http://localhost:5173/unicity-web
```

### URL参数化模型加载

```bash
# 使用自定义模型URL（需Base64编码）
http://localhost:5173/project/village-mini?model=aHR0cHM6Ly9leGFtcGxlLmNvbS9tb2RlbC5zcHg=

# 显示项目信息（开发调试用）
http://localhost:5173/project/village-mini?showInfo=true
```

### Base64编码示例

```javascript
// 编码模型URL
const modelUrl = 'https://example.com/model.spx'
const encodedUrl = btoa(modelUrl)
console.log(encodedUrl) // aHR0cHM6Ly9leGFtcGxlLmNvbS9tb2RlbC5zcHg=

// 在URL中使用
const viewerUrl = `http://localhost:5173/project/village-mini?model=${encodedUrl}`
```

## ⚙️ 配置说明

### 项目配置

在 `src/configs/projects.ts` 中配置各个项目的参数：

```typescript
export const projectConfigs: Record<string, ProjectConfig> = {
  'village-mini': {
    id: 'village-mini',
    name: 'Village Mini',
    description: 'Village Mini项目的3DGS展示页面',
    defaultModelUrl: 'https://reall3d.com/demo-models/hornedlizard.spx',
    customConfig: {
      backgroundColor: '#000000',
      showWatermark: false,
    },
  },
  // 更多项目配置...
}
```

### Viewer配置

支持的Viewer配置选项：

```typescript
interface ViewerConfig {
  useCustomControl?: boolean // 使用自定义控制器（注意：启用时会禁用自动旋转）
  disableTransitionEffectOnLoad?: boolean // 禁用过渡效果
  backgroundColor?: string // 背景颜色
  showWatermark?: boolean // 显示水印
  autoRotate?: boolean // 启用自动旋转（需要 useCustomControl: false）
  autoRotateSpeed?: number // 自动旋转速度（度/秒），默认2.0
  debugMode?: boolean // 调试模式
  // 更多配置选项...
}
```

### 自动旋转配置

要启用自动旋转功能，需要正确配置以下参数：

```typescript
{
  autoRotate: true,           // 启用自动旋转
  autoRotateSpeed: 1.5,       // 自动旋转速度（度/秒），可选，默认2.0
  useCustomControl: false,    // 必须禁用自定义控制模式
}
```

**重要说明**：
- `autoRotate` 和 `useCustomControl` 是互斥的
- 当 `useCustomControl: true` 时，会启用第一人称控制模式（WASD移动），此时自动旋转会被强制禁用
- 当 `useCustomControl: false` 时，会启用轨道控制模式，此时可以使用自动旋转功能
- `autoRotateSpeed` 控制旋转速度，单位为度/秒，数值越大旋转越快
- 用户交互（拖拽、缩放等）会暂停自动旋转，停止交互3秒后会自动恢复
- 按 `R` 键可以手动切换自动旋转开关

## 🔧 开发指南

### 添加新项目

1. 在 `src/configs/projects.ts` 中添加新项目配置
2. 在路由中添加对应的重定向规则（可选）
3. 根据需要自定义项目特定的配置

### 自定义Viewer行为

修改 `src/composables/useViewer.ts` 中的逻辑：

```typescript
// 扩展Viewer功能
const customMethod = () => {
  if (viewer.value) {
    // 自定义逻辑
  }
}
```

### 代码规范

项目使用以下代码规范工具：

```bash
# 代码检查
pnpm lint

# 代码格式化
pnpm format

# TypeScript类型检查
pnpm type-check
```

### Git提交规范

使用Conventional Commits规范：

```bash
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具变动
```

## 🚀 部署说明

### 生产构建

```bash
pnpm build
```

构建产物在 `dist/` 目录下。

### nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### iframe嵌入

```html
<!-- 基本嵌入 -->
<iframe
  src="https://your-domain.com/project/village-mini"
  width="100%"
  height="600"
  frameborder="0"
>
</iframe>

<!-- 带自定义模型的嵌入 -->
<iframe
  src="https://your-domain.com/project/village-mini?model=BASE64_ENCODED_URL"
  width="100%"
  height="600"
  frameborder="0"
>
</iframe>
```

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](../../issues)
- 发起 [Pull Request](../../pulls)

---

<p align="center">
  <strong>3DGS Viewer H5</strong> - 让3D高斯散射模型在H5环境中完美展现
</p>
