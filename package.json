{"name": "3dgs-viewer-h5", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "run-p type-check \"build-only {@}\" --", "build:prod": "run-p type-check \"build-only:prod {@}\" --", "build:dev": "run-p type-check \"build-only:dev {@}\" --", "build-only:prod": "vite build --mode production", "build-only:dev": "vite build --mode development", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write src/", "prepare": "husky", "commit": "npx git-cz", "check-all": "npm run type-check && npm run lint && npm run format"}, "dependencies": {"@reall3d/reall3dviewer": "file:libs/3dgs-viewer", "axios": "^1.10.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@types/three": "^0.178.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.7.0", "eslint": "^8.4.1", "eslint-config-prettier": "^8.10.0", "eslint-plugin-vue": "^9.22.0", "husky": "^9.1.7", "jiti": "^2.4.2", "lint-staged": "^16.1.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}