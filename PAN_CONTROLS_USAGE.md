# Pan Controls Usage Guide

## 概述

现在 Reall3dViewer 支持更精细的双指pan手势控制，包括：

1. **enablePan 配置修复** - 现在可以正确禁用/启用pan功能
2. **方向限制** - 可以限制pan只能水平或垂直移动
3. **范围限制** - 可以设置pan移动的边界范围

## 配置选项

### 基础配置

```typescript
const viewer = new Reall3dViewer({
  // 启用/禁用pan控制 (修复了之前总是为true的bug)
  enablePan: true,
  
  // Pan方向限制
  panDirection: 'both', // 'horizontal' | 'vertical' | 'both'
  
  // Pan移动范围限制
  minPan: [-10, -5, -10], // [x, y, z] 最小边界
  maxPan: [10, 5, 10],    // [x, y, z] 最大边界
});
```

### 详细说明

#### enablePan
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 控制是否启用pan手势。设置为`false`时完全禁用双指平移功能。

#### panDirection
- **类型**: `'horizontal' | 'vertical' | 'both'`
- **默认值**: `'both'`
- **说明**: 
  - `'horizontal'`: 只允许水平方向(左右)移动
  - `'vertical'`: 只允许垂直方向(上下)移动
  - `'both'`: 允许所有方向移动

#### minPan / maxPan
- **类型**: `number[]` (3个元素的数组 [x, y, z])
- **默认值**: `undefined` (无限制)
- **说明**: 设置相机目标点的移动边界。当相机目标超出这些边界时，会被自动约束到边界内。

## 使用示例

### 示例1: 只允许上下移动，范围限制在-5到5之间

```typescript
const viewer = new Reall3dViewer({
  enablePan: true,
  panDirection: 'vertical',
  minPan: [-Infinity, -5, -Infinity],
  maxPan: [Infinity, 5, Infinity],
});
```

### 示例2: 完全禁用pan功能

```typescript
const viewer = new Reall3dViewer({
  enablePan: false,
});
```

### 示例3: 限制在一个立方体区域内移动

```typescript
const viewer = new Reall3dViewer({
  enablePan: true,
  panDirection: 'both',
  minPan: [-20, -10, -20],
  maxPan: [20, 10, 20],
});
```

### 示例4: 在Vue项目中使用

```typescript
// 在 useViewer composable 中
const config: ViewerConfig = {
  enablePan: true,
  panDirection: 'horizontal', // 只允许左右移动
  minPan: [-15, -Infinity, -15],
  maxPan: [15, Infinity, 15],
  // 其他配置...
};

const { viewer, initViewer } = useViewer(config);
```

## 测试页面

我们提供了一个测试页面来验证这些功能：

1. 启动开发服务器: `npm run dev`
2. 访问: `http://localhost:5173/pan-test`
3. 在测试页面中可以：
   - 切换enablePan开关
   - 选择不同的panDirection
   - 设置minPan和maxPan边界
   - 实时应用配置并测试效果

## 技术实现

### 核心改进

1. **修复enablePan bug**: 
   - 之前在`CameraControls.updateByOptions`中总是设置为`true`
   - 现在正确使用`opts.enablePan`的值

2. **方向限制实现**:
   - 在`_applyPanConstraints`方法中计算相机的右向量和上向量
   - 根据`panDirection`设置只保留对应方向的移动分量

3. **范围限制实现**:
   - 使用Three.js的`Vector3.clamp()`方法限制目标点位置
   - 当目标点被约束时，同步调整相机位置以保持相对关系

### 兼容性

- 所有新配置选项都是可选的，不会影响现有代码
- 默认行为与之前保持一致（除了修复的enablePan bug）
- 支持动态更新配置

## 注意事项

1. **坐标系**: minPan和maxPan使用Three.js的世界坐标系
2. **性能**: 范围检查在每次update时执行，对性能影响很小
3. **移动端**: 在移动端使用双指手势进行pan操作
4. **桌面端**: 在桌面端使用鼠标中键拖拽或右键拖拽（取决于useCustomControl设置）

## 故障排除

### enablePan设置为false但仍能移动
- 检查是否在useCustomControl模式下，该模式有自己的移动控制逻辑
- 确保配置正确传递给了Reall3dViewer构造函数

### 方向限制不生效
- 确保panDirection设置正确
- 检查浏览器控制台是否有相关日志输出

### 范围限制不准确
- 检查minPan和maxPan的数组格式是否正确（必须是3个数字）
- 确保坐标值在合理范围内
