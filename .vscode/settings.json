{"prettier.configPath": ".prettier<PERSON>", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[cpp]": {"editor.defaultFormatter": "ms-vscode.cpptools"}, "[glsl]": {"editor.defaultFormatter": "raczzalan.webgl-glsl-editor"}, "webgl-glsl-editor.diagnostics": false, "C_Cpp.errorSquiggles": "disabled", "files.associations": {"bit": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "type_traits": "cpp", "limits": "cpp", "new": "cpp", "typeinfo": "cpp"}}