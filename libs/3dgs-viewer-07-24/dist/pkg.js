import {
  Vector3 as x,
  InstancedBufferGeometry as kn,
  <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON> as Ge,
  InstancedBufferAttribute as Po,
  DynamicDrawUsage as Lo,
  ShaderMaterial as Zn,
  DoubleSide as be,
  NormalBlending as Fo,
  DataTexture as Pe,
  RGBAIntegerFormat as Le,
  UnsignedIntType as Fe,
  Mesh as se,
  Vector4 as Ce,
  Vector2 as ee,
  WebGLRenderer as Ci,
  PerspectiveCamera as Mi,
  Object3D as jn,
  CylinderGeometry as xs,
  MeshBasicMaterial as ke,
  PlaneGeometry as Ro,
  Quaternion as Xt,
  Controls as Bo,
  MOUSE as Se,
  TOUCH as Me,
  Spherical as Ts,
  Ray as Uo,
  Plane as zo,
  MathUtils as xe,
  Float32BufferAttribute as ks,
  InstancedInterleavedBuffer as En,
  InterleavedBufferAttribute as Xe,
  WireframeGeometry as Oo,
  Box3 as qn,
  Sphere as ct,
  ShaderLib as Ot,
  UniformsUtils as xi,
  UniformsLib as Qt,
  Line3 as Qo,
  Matrix4 as Te,
  CircleGeometry as Re,
  Group as sn,
  BufferGeometry as Wo,
  Raycaster as Ho,
  SphereGeometry as Yo,
  Color as Jn,
  FrontSide as Vo,
  CatmullRomCurve3 as Es,
  Scene as Ti,
  AmbientLight as ki,
  FogExp2 as Ds,
  DirectionalLight as Go,
  EventDispatcher as Xo,
  Clock as No,
} from 'three'
import * as _s from '@gotoeasy/three-tile'
let v = 1
const ut = v++,
  es = v++,
  Be = v++,
  Ei = v++,
  Dn = v++,
  _n = v++,
  Di = v++,
  un = v++,
  _i = v++,
  et = v++,
  O = v++,
  U = v++,
  Ii = v++,
  In = v++,
  Ee = v++,
  je = v++,
  Qe = v++,
  wt = v++,
  Pi = v++,
  Li = v++,
  Fi = v++,
  Ko = v++,
  Ri = v++,
  Bi = v++,
  ts = v++,
  $o = v++,
  Zo = v++,
  Is = v++,
  te = v++
v++
const ce = v++,
  ht = v++,
  Ke = v++,
  Ps = v++,
  Ls = v++,
  Fs = v++,
  Ui = v++,
  jo = v++,
  Rs = v++,
  hn = v++,
  pn = v++,
  Bs = v++,
  Us = v++,
  zs = v++,
  Ne = v++,
  Os = v++,
  pt = v++,
  zi = v++,
  Ct = v++,
  Mt = v++,
  Wt = v++,
  Oi = v++,
  Pn = v++,
  qo = v++,
  ns = v++,
  Qs = v++,
  Ws = v++,
  Nt = v++,
  Kt = v++,
  $t = v++,
  Zt = v++,
  Qi = v++,
  ss = v++,
  I = v++,
  We = v++,
  ft = v++,
  z = v++,
  on = v++,
  Wi = v++,
  Jo = v++,
  ne = v++,
  Ln = v++,
  de = v++,
  Hi = v++,
  Yi = v++,
  Vi = v++,
  Gi = v++,
  Fn = v++,
  Xi = v++,
  Ue = v++,
  Ni = v++,
  Ki = v++,
  Ht = v++,
  ea = v++,
  $i = v++,
  Zi = v++
v++
const an = v++,
  fn = v++,
  gn = v++,
  mn = v++,
  Rn = v++,
  jt = v++,
  Bn = v++,
  lt = v++,
  ue = v++,
  is = v++,
  ji = v++,
  ta = v++,
  qi = v++,
  Hs = v++,
  Ys = v++,
  na = v++,
  sa = v++,
  Yt = v++,
  qt = v++,
  os = v++,
  $e = v++,
  He = v++,
  gt = v++,
  xt = v++,
  Ji = v++,
  K = v++,
  W = v++,
  ia = v++,
  Vs = v++,
  Jt = v++,
  en = v++,
  oa = v++,
  as = v++,
  rs = v++,
  De = v++,
  dt = v++,
  tt = v++,
  aa = v++,
  eo = v++,
  ra = v++
v++
v++
v++
v++
v++
const _e = v++,
  qe = v++,
  Vt = v++,
  Un = v++,
  to = v++,
  no = v++,
  zn = v++,
  Ie = v++,
  ot = v++,
  so = v++,
  cs = v++,
  Gs = v++,
  ca = v++,
  la = v++,
  ls = v++,
  io = v++,
  ds = v++,
  us = v++,
  hs = v++,
  tn = v++,
  On = v++,
  Xs = v++,
  nn = v++,
  ve = v++,
  oo = v++,
  ao = v++
v++
const at = v++,
  ro = v++,
  co = v++,
  ps = v++,
  lo = v++,
  da = v++,
  uo = v++,
  Ns = v++,
  ho = v++,
  po = v++
v++
const fo = v++,
  go = v++,
  $ = v++,
  Qn = v++,
  Wn = v++,
  Hn = v++,
  ua = v++,
  mo = v++,
  ha = v++,
  pa = v++,
  fa = v++,
  ga = v++,
  ma = v++,
  yo = v++,
  Ao = v++
v++
v++
v++
v++
v++
const Tt = v++,
  kt = v++,
  nt = v++,
  st = v++,
  Et = v++,
  Dt = v++,
  _t = v++,
  It = v++,
  Pt = v++,
  Lt = v++
class fs {
  constructor() {
    this.map = /* @__PURE__ */ new Map()
  }
  on(e, t = null, n = !1) {
    if (!e) return console.error('Invalid event key', e), null
    if (!t) return this.map.get(e)
    if (n) {
      let o = this.map.get(e)
      o
        ? typeof o == 'function'
          ? console.error('Invalid event type', 'multiFn=true', e)
          : o.push(t)
        : ((o = []), this.map.set(e, o), o.push(t))
    } else {
      let o = this.map.get(e)
      o
        ? typeof o == 'function'
          ? console.warn('Replace event', e)
          : console.error('Invalid event type', 'multiFn=false', e)
        : this.map.set(e, t)
    }
    return this.map.get(e)
  }
  fire(e, ...t) {
    const n = this.map.get(e)
    if (!n) {
      this.map.size && console.log('Undefined event:', e, '(', ...t, ')')
      return
    }
    if (typeof n == 'function') return n(...t)
    let o = []
    return n.forEach((a) => o.push(a(...t))), o
  }
  tryFire(e, ...t) {
    return this.map.get(e) ? this.fire(e, ...t) : void 0
  }
  off(e) {
    this.map.delete(e)
  }
  clear() {
    this.map.clear()
  }
}
class ya {}
class Aa {
  constructor(e, t = {}) {
    ;(this.fileSize = 0),
      (this.downloadSize = 0),
      (this.status = 0),
      (this.splatData = null),
      (this.watermarkData = null),
      (this.dataSplatCount = 0),
      (this.watermarkCount = 0),
      (this.sh12Data = []),
      (this.sh3Data = []),
      (this.sh12Count = 0),
      (this.sh3Count = 0),
      (this.rowLength = 0),
      (this.modelSplatCount = -1),
      (this.downloadSplatCount = 0),
      (this.renderSplatCount = 0),
      (this.header = null),
      (this.dataShDegree = 0),
      (this.minX = 1 / 0),
      (this.maxX = -1 / 0),
      (this.minY = 1 / 0),
      (this.maxY = -1 / 0),
      (this.minZ = 1 / 0),
      (this.maxZ = -1 / 0),
      (this.topY = 0),
      (this.currentRadius = 0),
      (this.textWatermarkVersion = 0),
      (this.lastTextWatermarkVersion = 0),
      (this.fetchLimit = 0),
      (this.opts = { ...e }),
      (this.meta = t),
      t.autoCut && (this.map = /* @__PURE__ */ new Map()),
      e.format ||
        (e.url?.endsWith('.spx')
          ? (this.opts.format = 'spx')
          : e.url?.endsWith('.splat')
            ? (this.opts.format = 'splat')
            : e.url?.endsWith('.ply')
              ? (this.opts.format = 'ply')
              : e.url?.endsWith('.spz')
                ? (this.opts.format = 'spz')
                : console.error('unknow format!')),
      (this.abortController = new AbortController())
  }
}
var R = /* @__PURE__ */ ((s) => (
  (s[(s.FetchReady = 0)] = 'FetchReady'),
  (s[(s.Fetching = 1)] = 'Fetching'),
  (s[(s.FetchDone = 2)] = 'FetchDone'),
  (s[(s.FetchAborted = 3)] = 'FetchAborted'),
  (s[(s.FetchFailed = 4)] = 'FetchFailed'),
  (s[(s.Invalid = 5)] = 'Invalid'),
  s
))(R || {})
const rn = 'v1.5.0-dev-pkg',
  pe = navigator.userAgent.includes('Mobi'),
  wa =
    'QWERTYUIOPLKJHGFDSAZXCVBNM1234567890qwertyuioplkjhgfdsazxcvbnm`~!@#$%^&*()-_=+\\|]}[{\'";::,<.>//? 	',
  Ye = 128,
  va = 32,
  Q = 32,
  Sa = 20,
  ba = 16,
  wo = 64 * 1024,
  Ca = 1024 * 1e4,
  Ma = 10240 * 1e4,
  Gt = 0.28209479177387814,
  xa = 0,
  Ta = 20,
  gs = 1,
  mt = 2,
  ms = 3,
  ka = 3141592653,
  ys =
    '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',
  As =
    '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'
async function Ea(s) {
  const e = new Uint32Array(s.buffer),
    t = new Float32Array(s.buffer),
    n = new ya()
  ;(n.Fixed = String.fromCharCode(s[0]) + String.fromCharCode(s[1]) + String.fromCharCode(s[2])),
    (n.Version = s[3]),
    (n.SplatCount = e[1]),
    (n.MinX = t[2]),
    (n.MaxX = t[3]),
    (n.MinY = t[4]),
    (n.MaxY = t[5]),
    (n.MinZ = t[6]),
    (n.MaxZ = t[7]),
    (n.MinTopY = t[8]),
    (n.MaxTopY = t[9]),
    (n.CreateDate = e[10]),
    (n.CreaterId = e[11]),
    (n.ExclusiveId = e[12]),
    (n.ShDegree = s[52]),
    (n.Flag1 = s[53]),
    (n.Flag2 = s[54]),
    (n.Flag3 = s[55]),
    (n.Reserve1 = e[14]),
    (n.Reserve2 = e[15])
  let o = ''
  for (let h = 64; h < 124; h++) o += String.fromCharCode(s[h])
  if (((n.Comment = o.trim()), (n.HashCheck = !0), n.Fixed !== 'spx' && n.Version !== 1))
    return null
  const a = n.CreaterId == 1202056903 ? As : ys,
    i = WebAssembly.compile(Uint8Array.from(atob(a), (h) => h.charCodeAt(0)).buffer),
    r = new WebAssembly.Memory({ initial: 1, maximum: 1 }),
    u = (await WebAssembly.instantiate(await i, { env: { memory: r, expf: cn } })).exports.H
  return new Uint8Array(r.buffer).set(s, 0), u(0) && (n.HashCheck = !1), n
}
async function ye(s) {
  const e = new Uint32Array(s.slice(0, 8).buffer),
    t = e[0],
    n = e[1],
    o = gs == n,
    a = mt == n,
    i = ms == n,
    r = o || a || i,
    c = !r,
    u = n == 20 || r ? As : ys,
    d = t * (r ? ba : Q),
    l = WebAssembly.compile(Uint8Array.from(atob(u), (E) => E.charCodeAt(0)).buffer),
    h = Math.floor((d + s.byteLength) / wo) + 2,
    m = new WebAssembly.Memory({ initial: h, maximum: h }),
    A = (await WebAssembly.instantiate(await l, { env: { memory: m, expf: cn } })).exports.D,
    g = new Uint8Array(m.buffer)
  return (
    g.set(s, d),
    A(0, d)
      ? { splatCount: t, blockFormat: n, success: !1 }
      : {
          splatCount: t,
          blockFormat: n,
          success: !0,
          datas: g.slice(0, d),
          isSplat: c,
          isSh: r,
          isSh1: o,
          isSh2: a,
          isSh3: i,
        }
  )
}
async function Yn(s, e) {
  const t = WebAssembly.compile(Uint8Array.from(atob(As), (u) => u.charCodeAt(0)).buffer),
    n = Math.floor((e * Q) / wo) + 2,
    o = new WebAssembly.Memory({ initial: n, maximum: n }),
    i = (await WebAssembly.instantiate(await t, { env: { memory: o, expf: cn } })).exports.s,
    r = new Uint8Array(o.buffer)
  r.set(s.slice(0, e * Q), 0)
  const c = i(0, e)
  return c ? (console.error('splat data parser failed:', c), new Uint8Array(0)) : r.slice(0, e * Q)
}
async function Da(s, e, t = !0, n = !0) {
  const o = WebAssembly.compile(Uint8Array.from(atob(ys), (l) => l.charCodeAt(0)).buffer),
    a = new WebAssembly.Memory({ initial: 1, maximum: 1 }),
    r = (await WebAssembly.instantiate(await o, { env: { memory: a, expf: cn } })).exports.w,
    c = new Uint8Array(a.buffer),
    u = new Float32Array(c.buffer),
    d = n ? -1 : 1
  return (u[0] = s), t ? (u[1] = d * e) : (u[2] = d * e), r(0, t ? 1 : 0), c.slice(0, Q)
}
function cn(s) {
  return Math.exp(s)
}
const Ve = pe ? 20480 : 51200
async function _a(s) {
  let e = 0
  try {
    s.status = R.Fetching
    const n = s.abortController.signal,
      o = s.opts.fetchReload ? 'reload' : 'default',
      a = await fetch(s.opts.url, { mode: 'cors', credentials: 'omit', cache: o, signal: n })
    if (a.status != 200) {
      console.warn(`fetch error: ${a.status}`),
        s.status === R.Fetching && (s.status = R.FetchFailed)
      return
    }
    const i = a.body.getReader(),
      r = parseInt(a.headers.get('content-length') || '0')
    ;(s.rowLength = 32), (s.fileSize = r)
    const c = (r / s.rowLength) | 0
    if (c < 1) {
      console.warn('data empty', s.opts.url), s.status === R.Fetching && (s.status = R.Invalid)
      return
    }
    ;(s.modelSplatCount = c),
      (s.downloadSplatCount = 0),
      (s.splatData = new Uint8Array(Math.min(s.modelSplatCount, s.fetchLimit) * 32)),
      (s.watermarkData = new Uint8Array(0))
    let u = new Uint8Array(32),
      d = 0
    for (;;) {
      let { done: l, value: h } = await i.read()
      if (l) break
      d + h.byteLength < s.rowLength
        ? (u.set(h, d), (d += h.byteLength), (e += h.length), (s.downloadSize = e))
        : ((d = await t(s, d, u, h)), d && u.set(h.slice(h.byteLength - d), 0)),
        s.downloadSplatCount >= s.fetchLimit && s.abortController.abort()
    }
  } catch (n) {
    n.name === 'AbortError'
      ? (console.log('Fetch Abort', s.opts.url),
        s.status === R.Fetching && (s.status = R.FetchAborted))
      : (console.error(n), s.status === R.Fetching && (s.status = R.FetchFailed))
  } finally {
    s.status === R.Fetching && (s.status = R.FetchDone)
  }
  async function t(n, o, a, i) {
    return new Promise(async (r) => {
      let c = ((o + i.byteLength) / n.rowLength) | 0,
        u = (o + i.byteLength) % n.rowLength,
        d
      o
        ? ((d = new Uint8Array(c * n.rowLength)),
          d.set(a.slice(0, o), 0),
          d.set(i.slice(0, i.byteLength - u), o))
        : (d = i.slice(0, c * n.rowLength)),
        n.downloadSplatCount + c > n.fetchLimit &&
          ((c = n.fetchLimit - n.downloadSplatCount), (u = 0))
      const l = async () => {
        if (c > Ve) {
          const h = await Yn(d, Ve)
          Ks(n, h),
            (n.downloadSplatCount += Ve),
            (e += Ve * n.rowLength),
            (n.downloadSize = e),
            (c -= Ve),
            (d = d.slice(Ve * n.rowLength)),
            setTimeout(l)
        } else {
          const h = await Yn(d, c)
          Ks(n, h), (n.downloadSplatCount += c), (e += c * n.rowLength), (n.downloadSize = e), r(u)
        }
      }
      await l()
    })
  }
}
function Ks(s, e) {
  let t = e.byteLength / Q
  const n = Math.min(s.fetchLimit, s.modelSplatCount)
  s.dataSplatCount + t > n
    ? ((t = n - s.dataSplatCount), s.splatData.set(e.slice(0, t * Q), s.dataSplatCount * Q))
    : s.splatData.set(e, s.dataSplatCount * Q)
  const o = new Float32Array(e.buffer)
  for (let i = 0, r = 0, c = 0, u = 0; i < t; i++)
    (r = o[i * 8]),
      (c = o[i * 8 + 1]),
      (u = o[i * 8 + 2]),
      (s.minX = Math.min(s.minX, r)),
      (s.maxX = Math.max(s.maxX, r)),
      (s.minY = Math.min(s.minY, c)),
      (s.maxY = Math.max(s.maxY, c)),
      (s.minZ = Math.min(s.minZ, u)),
      (s.maxZ = Math.max(s.maxZ, u))
  s.dataSplatCount += t
  const a = s.header?.MinTopY || 0
  ;(s.currentRadius = Math.sqrt(s.maxX * s.maxX + a * a + s.maxZ * s.maxZ)),
    (s.aabbCenter = new x((s.minX + s.maxX) / 2, (s.minY + s.maxY) / 2, (s.minZ + s.maxZ) / 2))
}
function ws(s) {
  let e = !1
  const t = (f, A, g) => s.on(f, A, g),
    n = (f, ...A) => s.fire(f, ...A),
    o = !n(I).renderer
  t(de, () => n(I).debugMode), t(ss, () => (e = !0))
  let a = 0
  ;(function f() {
    a++, !e && requestAnimationFrame(f)
  })(),
    t(ut, (f, A = null, g = 0) => {
      const y = () => {
        e || (g > 0 ? !(a % g) && f(a) : f(a), A && A() && requestAnimationFrame(y))
      }
      y()
    }),
    t(es, (f, A = null, g = 20) => {
      const y = () => {
        e || (f(), A && A() && setTimeout(y, g))
      }
      y()
    })
  let i = !1,
    r = 0,
    c = 0
  t(Ni, () => {
    if (((i = !0), n(I).debugMode))
      (async () => {
        const f = document.querySelector('#gsviewer #progressBarWrap')
        if (f) {
          f.style.display = 'block'
          const A = document.querySelector('#gsviewer #progressBar')
          A && (A.style.width = '0%')
        }
      })(),
        (async () => document.querySelector('#gsviewer .logo')?.classList.add('loading'))()
    else if (n(I).useCustomControl === !1)
      try {
        parent?.onProgress && parent.onProgress(1e-3, '0.001%')
      } catch (A) {
        console.debug('Cross-origin access blocked:', A.message)
      }
  }),
    t(Ht, (f) => {
      if (
        (f && (r = f),
        (i = !1),
        f !== void 0 &&
          ((async () => {
            const g = document.querySelector('#gsviewer #progressBarWrap')
            g && (g.style.display = 'none')
          })(),
          (async () => document.querySelector('#gsviewer .logo')?.classList.remove('loading'))(),
          n(I).useCustomControl === !1))
      )
        try {
          parent?.onProgress && parent.onProgress(0, '100%', 9)
        } catch (g) {
          console.debug('Cross-origin access blocked:', g.message)
        }
    }),
    t(Ki, (f) => {
      if (((i = !0), n(I).debugMode))
        (async () => {
          const A = document.querySelector('#gsviewer #progressBar')
          A && (A.style.width = `${f}%`)
        })()
      else if (n(I).useCustomControl === !1)
        try {
          parent?.onProgress && parent.onProgress(f, `${f}%`)
        } catch (g) {
          console.debug('Cross-origin access blocked:', g.message)
        }
    }),
    t(ea, () => i),
    t($i, (f) => {
      c = f
    }),
    t(Zi, () => !i && r && c >= r),
    t(We, () => {
      const A = n(et).parentElement.getBoundingClientRect()
      return { width: A.width, height: A.height, left: A.left, top: A.top }
    }),
    t(Ue, (f) => {
      let A = f.x.toFixed(3).split('.'),
        g = f.y.toFixed(3).split('.'),
        y = f.z.toFixed(3).split('.')
      return (
        (A[1] === '000' || A[1] === '00000') && (A[1] = '0'),
        (g[1] === '000' || g[1] === '00000') && (g[1] = '0'),
        (y[1] === '000' || y[1] === '00000') && (y[1] = '0'),
        `${A.join('.')}, ${g.join('.')}, ${y.join('.')}`
      )
    }),
    t($o, (f) => btoa(f)),
    t(Zo, (f) => atob(f))
  const u = 1e-3
  let d = new x(),
    l = new x(),
    h = 0
  t(Jo, () => {
    const f = n(O),
      A = f.fov,
      g = f.position.clone(),
      y = f.getWorldDirection(new x())
    return Math.abs(h - A) < u &&
      Math.abs(g.x - d.x) < u &&
      Math.abs(g.y - d.y) < u &&
      Math.abs(g.z - d.z) < u &&
      Math.abs(y.x - l.x) < u &&
      Math.abs(y.y - l.y) < u &&
      Math.abs(y.z - l.z) < u
      ? !1
      : ((h = A), (d = g), (l = y), !0)
  }),
    t(Ie, (f) => {
      if (!f) return
      const A = []
      f.traverse((g) => A.push(g)),
        A.forEach((g) => {
          g.dispose
            ? g.dispose()
            : (g.geometry?.dispose?.(),
              g.material && g.material instanceof Array
                ? g.material.forEach((y) => y?.dispose?.())
                : g.material?.dispose?.())
        }),
        f.clear()
    }),
    t(
      ce,
      async ({
        renderSplatCount: f,
        visibleSplatCount: A,
        modelSplatCount: g,
        fps: y,
        realFps: E,
        sortTime: M,
        fov: S,
        position: P,
        lookUp: T,
        lookAt: D,
        worker: F,
        scene: H,
        // updateSceneData,
        scale: G,
        cuts: p,
        shDegree: C,
      } = {}) => {
        if (!n(de)) return
        C !== void 0 && m('shDegree', `${C}`),
          f !== void 0 && m('renderSplatCount', `${f}`),
          A !== void 0 && m('visibleSplatCount', `${A}`),
          g !== void 0 && m('modelSplatCount', `${g}`),
          y !== void 0 && m('fps', y),
          E !== void 0 && m('realFps', `raw ${E}`),
          M !== void 0 && m('sort', `${M} ms`),
          !o && n(ne) ? p !== void 0 && m('cuts', `（${p} cuts）`) : m('cuts', ''),
          F && m('worker', `${F}`),
          H && m('scene', H),
          S && m('fov', S),
          P && m('position', P),
          T && m('lookUp', T),
          D && m('lookAt', D),
          D && m('viewer-version', rn)
        let b = performance.memory || { usedJSHeapSize: 0, totalJSHeapSize: 0, jsHeapSizeLimit: 0 },
          w = '',
          k = b.usedJSHeapSize / 1024 / 1024
        k > 1e3 ? (w += (k / 1024).toFixed(2) + ' G') : (w += k.toFixed(0) + ' M'), (w += ' / ')
        let _ = b.totalJSHeapSize / 1024 / 1024
        _ > 1e3 ? (w += (_ / 1024).toFixed(2) + ' G') : (w += _.toFixed(0) + ' M')
        let L = b.jsHeapSizeLimit / 1024 / 1024
        ;(w += ' / '),
          L > 1e3 ? (w += (L / 1024).toFixed(2) + ' G') : (w += L.toFixed(0) + ' M'),
          m('memory', w),
          G && m('scale', G)
      },
    )
  async function m(f, A) {
    let g = document.querySelector(`#gsviewer .debug .${f}`)
    g && (g.innerText = A)
  }
}
function Ia(s = 0) {
  const e = /* @__PURE__ */ new Date()
  return (
    e.setDate(e.getDate() - 7), s >= e.getFullYear() * 1e4 + (e.getMonth() + 1) * 100 + e.getDate()
  )
}
async function vo(s) {
  try {
    const e = new ReadableStream({
        async start(n) {
          n.enqueue(s), n.close()
        },
      }),
      t = new Response(e.pipeThrough(new DecompressionStream('gzip')))
    return new Uint8Array(await t.arrayBuffer())
  } catch (e) {
    return console.error('unGzip Failed:', e), null
  }
}
function ie(s) {
  return s < 0 ? 0 : s > 255 ? 255 : s | 0
}
function Ft(s) {
  const e = ie(Math.round(s * 128) + 128)
  return ie(Math.floor((e + 4) / 8) * 8)
}
const Pa = [xa, ka]
async function La(s) {
  try {
    s.status = R.Fetching
    const e = s.abortController.signal,
      t = s.opts.fetchReload ? 'reload' : 'default',
      n = await fetch(s.opts.url, { mode: 'cors', credentials: 'omit', cache: t, signal: e })
    if (n.status != 200) {
      console.warn(`fetch error: ${n.status}`),
        s.status === R.Fetching && (s.status = R.FetchFailed)
      return
    }
    const o = n.body.getReader(),
      a = parseInt(n.headers.get('content-length') || '0')
    if (a - Ye < Sa) {
      console.warn('data empty', s.opts.url), s.status === R.Fetching && (s.status = R.Invalid)
      return
    }
    s.fileSize = a
    let r = [],
      c = new Uint8Array(Ye),
      u = new Uint8Array(Q),
      d = 0,
      l = !1,
      h = 0,
      m = 0,
      f,
      A = !1
    for (;;) {
      let { done: g, value: y } = await o.read()
      if (g) break
      if (((s.downloadSize += y.byteLength), r)) {
        r.push(y)
        let S = 0
        for (let D = 0; D < r.length; D++) S += r[D].byteLength
        if (S < Ye) continue
        let P = 0
        for (let D = 0; D < r.length; D++)
          P + r[D].byteLength < Ye
            ? (c.set(r[D], P), (P += r[D].byteLength))
            : (c.set(r[D].slice(0, Ye - P), P), (y = new Uint8Array(r[D].slice(Ye - P))))
        const T = await Ea(c)
        if (!T) {
          s.abortController.abort(),
            s.status === R.Fetching && (s.status = R.Invalid),
            console.error('invalid spx format')
          continue
        }
        if (
          ((s.header = T),
          (s.modelSplatCount = T.SplatCount),
          (s.dataShDegree = T.ShDegree),
          (s.aabbCenter = new x(
            (T.MaxX + T.MaxX) / 2,
            (T.MinY + T.MaxY) / 2,
            (T.MinZ + T.MaxZ) / 2,
          )),
          (r = null),
          (c = null),
          !Pa.includes(T.ExclusiveId))
        ) {
          s.abortController.abort(),
            (s.status = R.Invalid),
            console.error(
              'Unrecognized format, creater id =',
              T.CreaterId,
              ', exclusive id =',
              T.ExclusiveId,
              T.Comment,
            )
          continue
        }
      }
      if (!l) {
        if (d + y.byteLength < 4) {
          u.set(y, d), (d += y.byteLength)
          continue
        }
        const S = new Uint8Array(d + y.byteLength)
        S.set(u.slice(0, d), 0), S.set(y, d), (y = S.slice(4)), (d = 0), (l = !0), (f = []), (m = 0)
        const P = new Int32Array(S.slice(0, 4).buffer)
        ;(A = P[0] < 0), (h = Math.abs(P[0]))
      }
      let E = m + y.byteLength
      if ((f.push(y), E < h)) {
        m += y.byteLength
        continue
      }
      for (; E >= h; ) {
        let S = new Uint8Array(h),
          P = 0
        for (let D = 0; D < f.length; D++)
          P + f[D].byteLength < h
            ? (S.set(f[D], P), (P += f[D].byteLength))
            : (S.set(f[D].slice(0, h - P), P), (y = new Uint8Array(f[D].slice(h - P))))
        A && (S = await vo(S))
        const T = await ye(S)
        if (!T.success) {
          console.error('spx block data parser failed. block format:', T.blockFormat),
            s.abortController.abort(),
            (s.status = R.Invalid)
          break
        }
        if (T.isSplat) (s.downloadSplatCount += T.datas.byteLength / 32), Fa(s, T.datas)
        else {
          const D = Math.min(s.fetchLimit, s.modelSplatCount)
          if (T.isSh3)
            if (s.sh3Count + T.splatCount > D) {
              const F = D - s.sh3Count
              s.sh3Data.push(T.datas.slice(0, F * 16)), (s.sh3Count += F)
            } else s.sh3Data.push(T.datas), (s.sh3Count += T.splatCount)
          else if (s.sh12Count + T.splatCount > D) {
            const F = D - s.sh12Count
            s.sh12Data.push(T.datas.slice(0, F * 16)), (s.sh12Count += F)
          } else s.sh12Data.push(T.datas), (s.sh12Count += T.splatCount)
        }
        if (y.byteLength < 4) {
          u.set(y, 0), (d = y.byteLength), (l = !1)
          break
        } else {
          const D = new Int32Array(y.slice(0, 4).buffer)
          ;(h = Math.abs(D[0])),
            (A = D[0] < 0),
            (y = y.slice(4)),
            (E = y.byteLength),
            (f = [y]),
            (m = y.byteLength)
        }
      }
      const M = s.fetchLimit
      s.header.ShDegree === 3
        ? s.downloadSplatCount >= M &&
          s.sh12Count >= M &&
          s.sh3Count >= M &&
          s.abortController.abort()
        : s.header.ShDegree
          ? s.downloadSplatCount >= M && s.sh12Count >= M && s.abortController.abort()
          : s.downloadSplatCount >= M && s.abortController.abort()
    }
  } catch (e) {
    e.name === 'AbortError'
      ? (console.log('Fetch Abort', s.opts.url),
        s.status === R.Fetching && (s.status = R.FetchAborted))
      : (console.error(e),
        s.status === R.Fetching && (s.status = R.FetchFailed),
        s.abortController.abort())
  } finally {
    s.status === R.Fetching && (s.status = R.FetchDone)
  }
}
function Fa(s, e) {
  let t = !!s.meta.autoCut,
    n = e.byteLength / 32
  const o = 4096
  if (!t) {
    const c = Math.min(s.fetchLimit, s.modelSplatCount)
    if (
      (!s.splatData && (s.splatData = new Uint8Array(c * Q)),
      !s.watermarkData && (s.watermarkData = new Uint8Array(0)),
      s.dataSplatCount + n > c && ((n = c - s.dataSplatCount), !n))
    )
      return
    const u = new Float32Array(e.buffer),
      d = new Uint32Array(e.buffer)
    for (let h = 0, m = 0, f = 0, A = 0; h < n; h++)
      if (
        ((m = u[h * 8]),
        (f = u[h * 8 + 1]),
        (A = u[h * 8 + 2]),
        (s.minX = Math.min(s.minX, m)),
        (s.maxX = Math.max(s.maxX, m)),
        (s.minY = Math.min(s.minY, f)),
        (s.maxY = Math.max(s.maxY, f)),
        (s.minZ = Math.min(s.minZ, A)),
        (s.maxZ = Math.max(s.maxZ, A)),
        d[h * 8 + 3] >> 16)
      ) {
        if (s.watermarkCount * Q === s.watermarkData.byteLength) {
          const g = new Uint8Array((s.watermarkCount + o) * Q)
          g.set(s.watermarkData, 0), (s.watermarkData = g)
        }
        s.watermarkData.set(e.slice(h * 32, h * 32 + 32), s.watermarkCount++ * Q)
      } else s.splatData.set(e.slice(h * 32, h * 32 + 32), s.dataSplatCount++ * Q)
    const l = s.header.MinTopY || 0
    s.currentRadius = Math.sqrt(s.maxX * s.maxX + l * l + s.maxZ * s.maxZ)
    return
  }
  let a = Math.min(Math.max(s.meta.autoCut, 2), 50)
  !s.watermarkData && (s.watermarkData = new Uint8Array(0))
  const i = new Float32Array(e.buffer),
    r = new Uint32Array(e.buffer)
  for (let c = 0, u = Math.floor(e.byteLength / Q), d = 0, l = 0, h = 0, m = ''; c < u; c++) {
    if (r[c * 8 + 3] >> 16) {
      if (s.watermarkCount * Q === s.watermarkData.byteLength) {
        const y = new Uint8Array((s.watermarkCount + o) * Q)
        y.set(s.watermarkData, 0), (s.watermarkData = y)
      }
      s.watermarkData.set(e.slice(c * 32, c * 32 + 32), s.watermarkCount++ * Q)
      continue
    }
    ;(d = i[c * 8]), (l = i[c * 8 + 1]), (h = i[c * 8 + 2])
    let f = Math.min(
        a - 1,
        Math.floor((Math.max(0, d - s.header.MinX) / (s.header.MaxX - s.header.MinX)) * a),
      ),
      A = Math.min(
        a - 1,
        Math.floor((Math.max(0, h - s.header.MinZ) / (s.header.MaxZ - s.header.MinZ)) * a),
      )
    m = `${f}-${A}`
    let g = s.map.get(m)
    if (!g)
      (g = {}),
        (g.minX = d),
        (g.maxX = d),
        (g.minY = l),
        (g.maxY = l),
        (g.minZ = h),
        (g.maxZ = h),
        (g.centerX = d),
        (g.centerY = l),
        (g.centerZ = h),
        (g.radius = 0),
        (g.splatData = new Uint8Array(o * Q)),
        g.splatData.set(e.slice(c * Q, c * Q + Q), 0),
        (g.splatCount = 1),
        s.map.set(m, g)
    else {
      if (g.splatData.byteLength / Q == g.splatCount) {
        const S = new Uint8Array(g.splatData.byteLength + o * Q)
        S.set(g.splatData, 0), (g.splatData = S)
      }
      ;(g.minX = Math.min(g.minX, d)),
        (g.maxX = Math.max(g.maxX, d)),
        (g.minY = Math.min(g.minY, l)),
        (g.maxY = Math.max(g.maxY, l)),
        (g.minZ = Math.min(g.minZ, h)),
        (g.maxZ = Math.max(g.maxZ, h)),
        (g.centerX = (g.maxX + g.minX) / 2),
        (g.centerY = (g.maxY + g.minY) / 2),
        (g.centerZ = (g.maxZ + g.minZ) / 2)
      const y = g.maxX - g.minX,
        E = g.maxY - g.minY,
        M = g.maxZ - g.minZ
      ;(g.radius = Math.sqrt(y * y + E * E + M * M) / 2),
        g.splatData.set(e.slice(c * Q, c * Q + Q), g.splatCount++ * Q)
    }
    s.dataSplatCount++
  }
}
const it = pe ? 20480 : 51200
async function Ra(s) {
  try {
    s.status = R.Fetching
    const o = s.abortController.signal,
      a = s.opts.fetchReload ? 'reload' : 'default',
      i = await fetch(s.opts.url, { mode: 'cors', credentials: 'omit', cache: a, signal: o })
    if (i.status != 200) {
      console.warn(`fetch error: ${i.status}`),
        s.status === R.Fetching && (s.status = R.FetchFailed)
      return
    }
    const r = i.body.getReader(),
      c = parseInt(i.headers.get('content-length') || '0')
    ;(s.fileSize = c),
      (s.downloadSize = 0),
      (s.downloadSplatCount = 0),
      (s.watermarkData = new Uint8Array(0))
    let u = new Uint8Array(256),
      d = 0,
      l = [],
      h
    for (;;) {
      let { done: m, value: f } = await r.read()
      if (m) break
      if (((s.downloadSize += f.byteLength), l)) {
        if ((l.push(f), s.downloadSize < 200)) continue
        const A = new Uint8Array(s.downloadSize)
        for (let g = 0, y = 0; g < l.length; g++) A.set(l[g], y), (y += l[g].byteLength)
        if (((h = n(A)), !h)) {
          l = [A]
          continue
        }
        ;(l = null),
          (f = A.slice(h.headerLength)),
          (s.rowLength = h.rowLength),
          (s.dataShDegree = h.shDegree),
          (s.modelSplatCount = h.vertexCount),
          (s.splatData = new Uint8Array(Math.min(s.modelSplatCount, s.fetchLimit) * 32))
      }
      d + f.byteLength < s.rowLength
        ? (u.set(f, d), (d += f.byteLength))
        : ((d = await e(h, s, d, u, f)), d && u.set(f.slice(f.byteLength - d), 0)),
        s.downloadSplatCount >= s.fetchLimit && s.abortController.abort()
    }
  } catch (o) {
    o.name === 'AbortError'
      ? (console.log('Fetch Abort', s.opts.url),
        s.status === R.Fetching && (s.status = R.FetchAborted))
      : (console.error(o), s.status === R.Fetching && (s.status = R.FetchFailed))
  } finally {
    s.status === R.Fetching && (s.status = R.FetchDone)
  }
  async function e(o, a, i, r, c) {
    return new Promise(async (u) => {
      let d = ((i + c.byteLength) / a.rowLength) | 0,
        l = (i + c.byteLength) % a.rowLength,
        h
      i
        ? ((h = new Uint8Array(d * a.rowLength)),
          h.set(r.slice(0, i), 0),
          h.set(c.slice(0, c.byteLength - l), i))
        : (h = c.slice(0, d * a.rowLength)),
        a.downloadSplatCount + d > a.fetchLimit &&
          ((d = a.fetchLimit - a.downloadSplatCount), (l = 0))
      const m = async () => {
        if (d > it) {
          const f = await t(o, h, it)
          $s(a, f),
            (a.downloadSplatCount += it),
            (d -= it),
            (h = h.slice(it * a.rowLength)),
            setTimeout(m)
        } else {
          const f = await t(o, h, d)
          $s(a, f), (a.downloadSplatCount += d), u(l)
        }
      }
      await m()
    })
  }
  async function t(o, a, i) {
    const r = new Uint8Array(i * va),
      c = new Float32Array(a.buffer),
      u = new Float32Array(r.buffer)
    for (let d = 0; d < i; d++)
      (u[d * 8 + 0] = c[(d * s.rowLength + o.offsets.x) / 4]),
        (u[d * 8 + 1] = c[(d * s.rowLength + o.offsets.y) / 4]),
        (u[d * 8 + 2] = c[(d * s.rowLength + o.offsets.z) / 4]),
        (u[d * 8 + 3] = Math.exp(c[(d * s.rowLength + o.offsets.scale_0) / 4])),
        (u[d * 8 + 4] = Math.exp(c[(d * s.rowLength + o.offsets.scale_1) / 4])),
        (u[d * 8 + 5] = Math.exp(c[(d * s.rowLength + o.offsets.scale_2) / 4])),
        (r[d * 32 + 24] = ie((0.5 + Gt * c[(d * s.rowLength + o.offsets.f_dc_0) / 4]) * 255)),
        (r[d * 32 + 25] = ie((0.5 + Gt * c[(d * s.rowLength + o.offsets.f_dc_1) / 4]) * 255)),
        (r[d * 32 + 26] = ie((0.5 + Gt * c[(d * s.rowLength + o.offsets.f_dc_2) / 4]) * 255)),
        (r[d * 32 + 27] = ie(
          (1 / (1 + Math.exp(-c[(d * s.rowLength + o.offsets.opacity) / 4]))) * 255,
        )),
        (r[d * 32 + 28] = ie(c[(d * s.rowLength + o.offsets.rot_0) / 4] * 128 + 128)),
        (r[d * 32 + 29] = ie(c[(d * s.rowLength + o.offsets.rot_1) / 4] * 128 + 128)),
        (r[d * 32 + 30] = ie(c[(d * s.rowLength + o.offsets.rot_2) / 4] * 128 + 128)),
        (r[d * 32 + 31] = ie(c[(d * s.rowLength + o.offsets.rot_3) / 4] * 128 + 128))
    if (o.shDegree == 3) {
      const l = new Uint8Array(i * 24 + 8),
        h = new Uint8Array(i * 21 + 8),
        m = new Uint32Array(2)
      ;(m[0] = i), (m[1] = mt), l.set(new Uint8Array(m.buffer), 0)
      const f = new Uint32Array(2)
      ;(f[0] = i), (f[1] = ms), h.set(new Uint8Array(f.buffer), 0)
      for (let y = 0, E = 0; y < i; y++)
        for (let M = 0; M < 8; M++)
          for (let S = 0; S < 3; S++)
            l[8 + E++] = Ft(c[(y * s.rowLength + o.offsets['f_rest_' + (M + S * 15)]) / 4])
      for (let y = 0, E = 0; y < i; y++)
        for (let M = 8; M < 15; M++)
          for (let S = 0; S < 3; S++)
            h[8 + E++] = Ft(c[(y * s.rowLength + o.offsets['f_rest_' + (M + S * 15)]) / 4])
      const A = await ye(l)
      s.sh12Data.push(A.datas)
      const g = await ye(h)
      s.sh3Data.push(g.datas)
    } else if (o.shDegree == 2) {
      const l = new Uint8Array(i * 24 + 8),
        h = new Uint32Array(2)
      ;(h[0] = i), (h[1] = mt), l.set(new Uint8Array(h.buffer), 0)
      for (let f = 0, A = 0; f < i; f++)
        for (let g = 0; g < 8; g++)
          for (let y = 0; y < 3; y++)
            l[8 + A++] = Ft(c[(f * s.rowLength + o.offsets['f_rest_' + (g + y * 8)]) / 4])
      const m = await ye(l)
      s.sh12Data.push(m.datas)
    } else if (o.shDegree == 1) {
      const l = new Uint8Array(i * 9 + 8),
        h = new Uint32Array(2)
      ;(h[0] = i), (h[1] = gs), l.set(new Uint8Array(h.buffer), 0)
      for (let f = 0, A = 0; f < i; f++)
        for (let g = 0; g < 3; g++)
          for (let y = 0; y < 3; y++)
            l[8 + A++] = Ft(c[(f * s.rowLength + o.offsets['f_rest_' + (g + y * 3)]) / 4])
      const m = await ye(l)
      s.sh12Data.push(m.datas)
    }
    return await Yn(r, i)
  }
  function n(o) {
    let a = new TextDecoder().decode(o.slice(0, 2048))
    const i = `end_header
`,
      r = a.indexOf(i)
    if (r < 0) {
      if (o.byteLength > 1024 * 2) throw new Error('Unable to read .ply file header')
      return null
    }
    if (!a.startsWith('ply') || a.indexOf('format binary_little_endian 1.0') < 0)
      throw new Error('Unknow .ply file header')
    const c = parseInt(/element vertex (\d+)\n/.exec(a)[1])
    let u = 0,
      d = {},
      l = {}
    const h = {
      double: 'getFloat64',
      int: 'getInt32',
      uint: 'getUint32',
      float: 'getFloat32',
      short: 'getInt16',
      ushort: 'getUint16',
      uchar: 'getUint8',
    }
    for (let A of a
      .slice(0, r)
      .split(
        `
`,
      )
      .filter((g) => g.startsWith('property '))) {
      const [g, y, E] = A.split(' '),
        M = h[y] || 'getInt8'
      ;(l[E] = M), (d[E] = u), (u += parseInt(M.replace(/[^\d]/g, '')) / 8)
    }
    let m = 0
    l.f_rest_44 ? (m = 3) : l.f_rest_23 ? (m = 2) : l.f_rest_8 && (m = 1)
    const f = [
      'x',
      'y',
      'z',
      'scale_0',
      'scale_1',
      'scale_2',
      'f_dc_0',
      'f_dc_1',
      'f_dc_2',
      'opacity',
      'rot_0',
      'rot_1',
      'rot_2',
      'rot_3',
    ]
    for (let A = 0; A < f.length; A++) {
      const g = f[A]
      if (!l[g]) throw new Error(`Property not found: ${g}`)
    }
    return { headerLength: r + i.length, offsets: d, rowLength: u, vertexCount: c, shDegree: m }
  }
}
function $s(s, e) {
  let t = e.byteLength / Q
  const n = Math.min(s.fetchLimit, s.modelSplatCount)
  s.dataSplatCount + t > n
    ? ((t = n - s.dataSplatCount), s.splatData.set(e.slice(0, t * Q), s.dataSplatCount * Q))
    : s.splatData.set(e, s.dataSplatCount * Q)
  const o = new Float32Array(e.buffer)
  for (let i = 0, r = 0, c = 0, u = 0; i < t; i++)
    (r = o[i * 8]),
      (c = o[i * 8 + 1]),
      (u = o[i * 8 + 2]),
      (s.minX = Math.min(s.minX, r)),
      (s.maxX = Math.max(s.maxX, r)),
      (s.minY = Math.min(s.minY, c)),
      (s.maxY = Math.max(s.maxY, c)),
      (s.minZ = Math.min(s.minZ, u)),
      (s.maxZ = Math.max(s.maxZ, u))
  s.dataSplatCount += t
  const a = 0
  ;(s.currentRadius = Math.sqrt(s.maxX * s.maxX + a * a + s.maxZ * s.maxZ)),
    (s.aabbCenter = new x((s.minX + s.maxX) / 2, (s.minY + s.maxY) / 2, (s.minZ + s.maxZ) / 2))
}
const Y = pe ? 20480 : 51200,
  yn = 16
async function Ba(s) {
  try {
    s.status = R.Fetching
    const o = s.abortController.signal,
      a = s.opts.fetchReload ? 'reload' : 'default',
      i = await fetch(s.opts.url, { mode: 'cors', credentials: 'omit', cache: a, signal: o })
    if (i.status != 200) {
      console.warn(`fetch error: ${i.status}`),
        s.status === R.Fetching && (s.status = R.FetchFailed)
      return
    }
    const r = i.body.getReader(),
      c = parseInt(i.headers.get('content-length') || '0')
    ;(s.fileSize = c),
      (s.downloadSize = 0),
      (s.downloadSplatCount = 0),
      (s.watermarkData = new Uint8Array(0))
    const u = new Uint8Array(c)
    for (;;) {
      let { done: h, value: m } = await r.read()
      if (h) break
      u.set(m, s.downloadSize), (s.downloadSize += m.length)
    }
    const d = await vo(u)
    if (!d || d.length < 16) {
      console.error('Invalid spz format'), (s.status = R.Invalid)
      return
    }
    const l = n(d)
    ;(s.modelSplatCount = l.numPoints),
      (s.dataShDegree = l.shDegree),
      (s.splatData = new Uint8Array(Math.min(s.modelSplatCount, s.fetchLimit) * 32)),
      await e(l, s, d)
  } catch (o) {
    o.name === 'AbortError'
      ? (console.log('Fetch Abort', s.opts.url),
        s.status === R.Fetching && (s.status = R.FetchAborted))
      : (console.error(o), s.status === R.Fetching && (s.status = R.FetchFailed))
  } finally {
    s.status === R.Fetching && (s.status = R.FetchDone)
  }
  async function e(o, a, i) {
    const r = o.numPoints * 9,
      c = o.numPoints,
      u = o.numPoints * 3,
      d = o.numPoints * 3,
      l = o.numPoints * 3,
      h = yn,
      m = h + r,
      f = m + c,
      A = f + u,
      g = A + d,
      y = g + l,
      E = Math.min(o.numPoints, a.fetchLimit),
      M = Math.ceil(E / Y)
    for (let S = 0; S < M; S++) {
      let P = S < M - 1 ? Y : E - S * Y
      a.dataSplatCount + P > a.fetchLimit && (P = a.fetchLimit - a.dataSplatCount)
      const T = new Uint8Array(P * 20 + 8),
        D = new Uint32Array(2)
      ;(D[0] = P), (D[1] = Ta), T.set(new Uint8Array(D.buffer), 0)
      let F = 8
      for (let p = 0; p < P; p++)
        (T[F++] = i[h + (S * Y + p) * 9 + 0]),
          (T[F++] = i[h + (S * Y + p) * 9 + 1]),
          (T[F++] = i[h + (S * Y + p) * 9 + 2])
      for (let p = 0; p < P; p++)
        (T[F++] = i[h + (S * Y + p) * 9 + 3]),
          (T[F++] = i[h + (S * Y + p) * 9 + 4]),
          (T[F++] = i[h + (S * Y + p) * 9 + 5])
      for (let p = 0; p < P; p++)
        (T[F++] = i[h + (S * Y + p) * 9 + 6]),
          (T[F++] = i[h + (S * Y + p) * 9 + 7]),
          (T[F++] = i[h + (S * Y + p) * 9 + 8])
      for (let p = 0; p < P; p++) T[F++] = i[A + (S * Y + p) * 3]
      for (let p = 0; p < P; p++) T[F++] = i[A + (S * Y + p) * 3 + 1]
      for (let p = 0; p < P; p++) T[F++] = i[A + (S * Y + p) * 3 + 2]
      for (let p = 0; p < P; p++) T[F++] = An(i[f + (S * Y + p) * 3])
      for (let p = 0; p < P; p++) T[F++] = An(i[f + (S * Y + p) * 3 + 1])
      for (let p = 0; p < P; p++) T[F++] = An(i[f + (S * Y + p) * 3 + 2])
      const H = []
      for (let p = 0, C = 0, b = 0, w = 0; p < P; p++)
        (T[F++] = i[m + (S * Y + p)]),
          (C = i[g + (S * Y + p) * 3 + 0]),
          (b = i[g + (S * Y + p) * 3 + 1]),
          (w = i[g + (S * Y + p) * 3 + 2]),
          H.push(Ua(C, b, w))
      for (let p = 0; p < P; p++) T[F++] = H[p][0]
      for (let p = 0; p < P; p++) T[F++] = H[p][1]
      for (let p = 0; p < P; p++) T[F++] = H[p][2]
      for (let p = 0; p < P; p++) T[F++] = H[p][3]
      const G = await ye(T)
      if ((t(o, a, G.datas), o.shDegree === 1)) {
        const p = new Uint8Array(P * 9 + 8),
          C = new Uint32Array(2)
        ;(C[0] = P), (C[1] = gs), p.set(new Uint8Array(C.buffer), 0)
        for (let w = 0, k = 8; w < P; w++)
          p.set(i.slice(y + (S * Y + w) * 9, y + (S * Y + w) * 9 + 9), k), (k += 9)
        const b = await ye(p)
        a.sh12Data.push(b.datas)
      } else if (o.shDegree === 2) {
        const p = new Uint8Array(P * 24 + 8),
          C = new Uint32Array(2)
        ;(C[0] = P), (C[1] = mt), p.set(new Uint8Array(C.buffer), 0)
        for (let w = 0, k = 8; w < P; w++)
          p.set(i.slice(y + (S * Y + w) * 24, y + (S * Y + w) * 24 + 24), k), (k += 24)
        const b = await ye(p)
        a.sh12Data.push(b.datas)
      } else if (o.shDegree === 3) {
        const p = new Uint8Array(P * 24 + 8),
          C = new Uint32Array(2)
        ;(C[0] = P), (C[1] = mt), p.set(new Uint8Array(C.buffer), 0)
        for (let L = 0, B = 8; L < P; L++)
          p.set(i.slice(y + (S * Y + L) * 45, y + (S * Y + L) * 45 + 24), B), (B += 24)
        const b = await ye(p)
        a.sh12Data.push(b.datas)
        const w = new Uint8Array(P * 21 + 8),
          k = new Uint32Array(2)
        ;(k[0] = P), (k[1] = ms), w.set(new Uint8Array(k.buffer), 0)
        for (let L = 0, B = 8; L < P; L++)
          w.set(i.slice(y + (S * Y + L) * 45 + 24, y + (S * Y + L) * 45 + 45), B), (B += 21)
        const _ = await ye(w)
        a.sh3Data.push(_.datas)
      }
      if (a.dataSplatCount >= a.fetchLimit) break
    }
  }
  function t(o, a, i) {
    let r = i.byteLength / Q
    const c = Math.min(a.fetchLimit, o.numPoints)
    a.dataSplatCount + r > c
      ? ((r = c - a.dataSplatCount), a.splatData.set(i.slice(0, r * Q), a.dataSplatCount * Q))
      : a.splatData.set(i, a.dataSplatCount * Q)
    const u = new Float32Array(i.buffer)
    for (let l = 0, h = 0, m = 0, f = 0; l < r; l++)
      (h = u[l * 8]),
        (m = u[l * 8 + 1]),
        (f = u[l * 8 + 2]),
        (a.minX = Math.min(a.minX, h)),
        (a.maxX = Math.max(a.maxX, h)),
        (a.minY = Math.min(a.minY, m)),
        (a.maxY = Math.max(a.maxY, m)),
        (a.minZ = Math.min(a.minZ, f)),
        (a.maxZ = Math.max(a.maxZ, f))
    ;(a.dataSplatCount += r), (a.downloadSplatCount += r)
    const d = 0
    ;(a.currentRadius = Math.sqrt(a.maxX * a.maxX + d * d + a.maxZ * a.maxZ)),
      (a.aabbCenter = new x((a.minX + a.maxX) / 2, (a.minY + a.maxY) / 2, (a.minZ + a.maxZ) / 2))
  }
  function n(o) {
    const a = o.slice(0, yn),
      i = new Uint32Array(a.buffer),
      r = {}
    if (
      ((r.magic = i[0]),
      (r.version = i[1]),
      (r.numPoints = i[2]),
      (r.shDegree = a[12]),
      (r.fractionalBits = a[13]),
      (r.flags = a[14]),
      (r.reserved = a[15]),
      r.magic !== 1347635022)
    )
      throw new Error('[SPZ ERROR] header not found')
    if (r.version !== 2) throw new Error('[SPZ ERROR] ersion not supported:' + r.version)
    if (r.shDegree > 3) throw new Error('[SPZ ERROR] unsupported SH degree:' + r.shDegree)
    if (r.fractionalBits !== 12)
      throw new Error('[SPZ ERROR] unsupported FractionalBits:' + r.fractionalBits)
    let c = 0
    if (
      (r.shDegree === 1 ? (c = 9) : r.shDegree === 2 ? (c = 24) : r.shDegree === 3 && (c = 45),
      o.length !== yn + r.numPoints * (19 + c))
    )
      throw new Error('[SPZ ERROR] invalid spz data')
    return r
  }
}
function An(s) {
  const e = (s - 127.5) / 38.25
  return ie((0.5 + Gt * e) * 255)
}
function Ua(s, e, t) {
  const n = s / 127.5 - 1,
    o = e / 127.5 - 1,
    a = t / 127.5 - 1,
    i = Math.sqrt(Math.max(0, 1 - (n * n + o * o + a * a)))
  return [ie(i * 128 + 128), ie(n * 128 + 128), ie(o * 128 + 128), ie(a * 128 + 128)]
}
function za(s) {
  const e = (E, M, S) => s.on(E, M, S),
    t = (E, ...M) => s.fire(E, ...M)
  let n,
    o = Date.now() + 36e5,
    a = null,
    i,
    r = { index: 0, version: 0 },
    c = { version: 0 },
    u = !1
  const d = t(ne)
  e(uo, () => i?.aabbCenter || new x())
  let l
  const h = new Promise((E) => (l = E))
  e(ht, async () => {
    const E = t(I)
    let M = pe ? E.maxRenderCountOfMobile : E.maxRenderCountOfPc
    if (!E.bigSceneMode) {
      let S = await h
      M = Math.min(S, M) + 10240
    }
    return M
  }),
    e(at, async (E) => {
      const M = t(I)
      if (M.bigSceneMode) return 1
      let S = pe ? M.maxRenderCountOfMobile : M.maxRenderCountOfPc,
        P = await h
      if (((S = Math.min(P, S)), !i.dataShDegree)) return 1
      if (E >= 3) {
        if (i.dataShDegree < 3) return 1
      } else if (E >= 1) {
        if (i.dataShDegree < 1) return 1
      } else return 1
      const T = 1024 * 2
      return Math.ceil(S / T)
    }),
    e(ps, async () => (t(I).bigSceneMode ? 0 : (await h, i.dataShDegree))),
    e(cs, async (E, M = !0) => {
      try {
        await h
        const S = !!i.header?.Flag2
        ;(a = await t(so, E, M, S)), i && (i.textWatermarkVersion = Date.now())
      } catch {
        console.info('failed to generate watermark')
      }
    }),
    e(Rn, () => {
      if (d) return r.version <= c.version ? r.xyz : c.xyz
      if (i?.status === R.FetchDone || i?.status === R.FetchAborted) {
        if (i.activePoints && i.activePoints.length === void 0) return i.activePoints
        const E = {},
          M = r.xyz
        for (let S = 0, P = M.length / 3, T = 0, D = 0, F = 0, H = ''; S < P; S++)
          (T = M[S * 3]),
            (D = M[S * 3 + 1]),
            (F = M[S * 3 + 2]),
            (H = `${Math.floor(T / 2) * 2 + 1},${Math.floor(D / 2) * 2 + 1},${Math.floor(F / 2) * 2 + 1}`),
            (E[H] = E[H] || []).push(T, D, F)
        return (i.activePoints = E)
      }
      return r.xyz
    })
  async function m(E) {
    if (n) return
    if (i && (i.status === R.Invalid || i.status === R.FetchFailed))
      return (
        t(I).viewerEvents?.fire(te),
        t(Ht, 0) || t(ce, { renderSplatCount: 0, visibleSplatCount: 0, modelSplatCount: 0 })
      )
    if (!i || !i.downloadSize) return
    const M = i.status !== R.FetchReady && i.status !== R.Fetching
    if (M) {
      const S = Math.min(i.fetchLimit, i.downloadSplatCount)
      !i.notifyFetchStopDone && (i.notifyFetchStopDone = !0) && t(Ht, S)
    } else t(Ki, (100 * i.downloadSize) / i.fileSize)
    i.downloadSplatCount &&
      (u ||
        ((u = !0),
        setTimeout(async () => {
          await f(M), (u = !1)
        })))
  }
  async function f(E) {
    if (n) return
    const M = r,
      S = await t(ht),
      P = a
    let T = i.dataSplatCount,
      D = E ? i.watermarkCount : 0,
      F = i.meta.showWatermark && E ? (P?.byteLength || 0) / 32 : 0
    if (
      ((i.renderSplatCount = T + D + F),
      i.renderSplatCount >= S && ((i.renderSplatCount = S), (D = 0), (F = 0), T > S && (T = S)),
      t(ce, { visibleSplatCount: i.renderSplatCount, modelSplatCount: i.modelSplatCount + F }),
      Date.now() - M.textureReadyTime < (pe ? 600 : 300) ||
        (i.smallSceneUploadDone && i.lastTextWatermarkVersion == i.textWatermarkVersion))
    )
      return
    if (!M.version) {
      t(zi, (i.header?.Flag2 ? i.header.MaxTopY : i.header?.MinTopY) || 0)
      let _ = i.opts.format
      i.opts.format == 'spx' &&
        (_ = 'spx' + (i.header.ExclusiveId ? (' ' + i.header.ExclusiveId).substring(0, 6) : '')),
        t(ce, { scene: `small (${_})` })
    }
    ;(i.lastTextWatermarkVersion = i.textWatermarkVersion), (M.textureReady = !1)
    const H = 1024 * 2,
      G = Math.ceil((2 * S) / H),
      p = new Uint32Array(H * G * 4),
      C = new Float32Array(p.buffer),
      b = new Uint8Array(p.buffer)
    b.set(i.splatData.slice(0, T * 32), 0),
      D && b.set(i.watermarkData.slice(0, D * 32), T * 32),
      F && b.set(P.slice(0, F * 32), (T + D) * 32)
    const w = new Float32Array(i.renderSplatCount * 3)
    for (let _ = 0, L = 0; _ < i.renderSplatCount; _++)
      (w[_ * 3] = C[_ * 8]), (w[_ * 3 + 1] = C[_ * 8 + 1]), (w[_ * 3 + 2] = C[_ * 8 + 2])
    const k = Date.now()
    if (
      ((M.version = k),
      (M.txdata = p),
      (M.xyz = w),
      (M.renderSplatCount = i.renderSplatCount),
      (M.visibleSplatCount = i.downloadSplatCount + F),
      (M.modelSplatCount = i.downloadSplatCount + F),
      (M.watermarkCount = D + F),
      (M.minX = i.minX),
      (M.maxX = i.maxX),
      (M.minY = i.minY),
      (M.maxY = i.maxY),
      (M.minZ = i.minZ),
      (M.maxZ = i.maxZ),
      t(ao, M, i.currentRadius, i.currentRadius),
      (o = k),
      E && !i.smallSceneUploadDone)
    ) {
      ;(i.smallSceneUploadDone = !0),
        t(ro, i.sh12Data),
        t(co, i.sh3Data),
        (i.sh12Data = null),
        (i.sh3Data = null)
      const _ = t(I)
      t(ns, _.shDegree === void 0 ? 3 : _.shDegree), t(Rn)
    }
    t(ce, { renderSplatCount: i.renderSplatCount })
  }
  function A() {
    n ||
      ((n = !0),
      i?.abortController?.abort(),
      i?.map?.clear(),
      (i = null),
      (a = null),
      (r = null),
      (c = null))
  }
  function g(E) {
    if (E.opts.format === 'spx') La(E)
    else if (E.opts.format === 'splat') _a(E)
    else if (E.opts.format === 'ply') Ra(E)
    else if (E.opts.format === 'spz') Ba(E)
    else return !1
    return !0
  }
  async function y(E, M) {
    if (n) return
    const S = t(I),
      P = pe ? S.maxRenderCountOfMobile : S.maxRenderCountOfPc,
      T = t(ne)
    if (((E.fetchReload = Ia(M.updateDate || 0)), (i = new Aa(E, M)), T && M.autoCut)) {
      const H = M.pcDownloadLimitSplatCount || Ma,
        G = M.mobileDownloadLimitSplatCount || Ca,
        p = pe ? G : H
      i.fetchLimit = Math.min(M.autoCut * M.autoCut * P + P, p)
    } else i.fetchLimit = P
    const D = Date.now(),
      F = () => {
        if (!i || i.status == R.Invalid || i.status == R.FetchFailed) return l(0)
        if (i.modelSplatCount >= 0) l(i.modelSplatCount), setTimeout(() => t(Xi), 5)
        else {
          if (Date.now() - D >= 3e3) return l(0)
          setTimeout(F, 10)
        }
      }
    if ((F(), !g(i))) {
      console.error('Unsupported format:', E.format), t(Ht, 0)
      return
    }
    t(Ni)
  }
  e(Hi, async (E, M) => await y(E, M)),
    e(Yi, (E = 1e4) => Date.now() - o < E),
    e(Vi, () => A()),
    t(
      ut,
      async () => await m(),
      () => !n,
    )
}
function Oa() {
  return `
        precision highp float;

        uniform highp usampler2D splatTexture0, splatTexture1, splatShTexture12, splatShTexture3;
        uniform vec2 focal, viewport;
        uniform int usingIndex, pointMode, bigSceneMode, showWaterMark, debugEffect, shDegree;
        uniform float topY, currentVisibleRadius, currentLightRadius, performanceNow;
        uniform vec4 markPoint, waterMarkColor;

        attribute uint splatIndex;

        varying vec4 vColor;
        varying vec3 vPosition;

        const float FactorSH = 0.0625;
        const uint MaskSH = 0x1Fu;
        const float SH_C1 = 0.4886025119029199;
        const float[5] SH_C2 = float[](1.0925484305920792f, -1.0925484305920792f, 0.31539156525252005f, -1.0925484305920792f, 0.5462742152960396f);
        const float[7] SH_C3 = float[](-0.5900435899266435f, 2.890611442640554f, -0.4570457994644658f, 0.3731763325901154f, -0.4570457994644658f, 1.445305721320277f, -0.5900435899266435f);

        vec3[15] readShDatas() {
            int shCnt = 0;
            float[45] fSHs;
            uvec4 rgb12 = texelFetch(splatShTexture12, ivec2((splatIndex & 0x7ffu), (splatIndex >> 11)), 0);
            if ( rgb12.a > 0u ) {
                shCnt = 3;
                fSHs[0] = float((rgb12.r >> 27) & MaskSH) * FactorSH - 1.0;
                fSHs[1] = float((rgb12.r >> 22) & MaskSH) * FactorSH - 1.0;
                fSHs[2] = float((rgb12.r >> 17) & MaskSH) * FactorSH - 1.0;
                fSHs[3] = float((rgb12.r >> 12) & MaskSH) * FactorSH - 1.0;
                fSHs[4] = float((rgb12.r >> 7) & MaskSH) * FactorSH - 1.0;
                fSHs[5] = float((rgb12.r >> 2) & MaskSH) * FactorSH - 1.0;
                fSHs[6] = float(( (rgb12.r << 3) | (rgb12.g >> 29) ) & MaskSH) * FactorSH - 1.0;
                fSHs[7] = float((rgb12.g >> 24) & MaskSH) * FactorSH - 1.0;
                fSHs[8] = float((rgb12.g >> 19) & MaskSH) * FactorSH - 1.0;

                if (shDegree > 1) {
                    shCnt = 8;
                    fSHs[9]  = float((rgb12.g >> 14) & MaskSH) * FactorSH - 1.0;
                    fSHs[10] = float((rgb12.g >> 9) & MaskSH) * FactorSH - 1.0;
                    fSHs[11] = float((rgb12.g >> 4) & MaskSH) * FactorSH - 1.0;
                    fSHs[12] = float(( (rgb12.g << 1) | (rgb12.b >> 31) ) & MaskSH) * FactorSH - 1.0;
                    fSHs[13] = float((rgb12.b >> 26) & MaskSH) * FactorSH - 1.0;
                    fSHs[14] = float((rgb12.b >> 21) & MaskSH) * FactorSH - 1.0;
                    fSHs[15] = float((rgb12.b >> 16) & MaskSH) * FactorSH - 1.0;
                    fSHs[16] = float((rgb12.b >> 11) & MaskSH) * FactorSH - 1.0;
                    fSHs[17] = float((rgb12.b >> 6) & MaskSH) * FactorSH - 1.0;
                    fSHs[18] = float((rgb12.b >> 1) & MaskSH) * FactorSH - 1.0;
                    fSHs[19] = float(( (rgb12.b << 4) | (rgb12.a >> 28) ) & MaskSH) * FactorSH - 1.0;
                    fSHs[20] = float(((rgb12.a >> 23) & MaskSH)) * FactorSH - 1.0;
                    fSHs[21] = float((rgb12.a >> 18) & MaskSH) * FactorSH - 1.0;
                    fSHs[22] = float((rgb12.a >> 13) & MaskSH) * FactorSH - 1.0;
                    fSHs[23] = float((rgb12.a >> 8) & MaskSH) * FactorSH - 1.0;

                    if (shDegree > 2) {
                        uvec4 rgb3 = texelFetch(splatShTexture3, ivec2(splatIndex & 0x7ffu, splatIndex >> 11), 0);
                        if ( rgb3.a > 0u ) {
                            shCnt = 15;
                            fSHs[24] = float((rgb3.r >> 27) & MaskSH) * FactorSH - 1.0;
                            fSHs[25] = float((rgb3.r >> 22) & MaskSH) * FactorSH - 1.0;
                            fSHs[26] = float((rgb3.r >> 17) & MaskSH) * FactorSH - 1.0;
                            fSHs[27] = float((rgb3.r >> 12) & MaskSH) * FactorSH - 1.0;
                            fSHs[28] = float((rgb3.r >> 7) & MaskSH) * FactorSH - 1.0;
                            fSHs[29] = float((rgb3.r >> 2) & MaskSH) * FactorSH - 1.0;
                            fSHs[30] = float(( (rgb3.r << 3) | (rgb3.g >> 29) ) & MaskSH) * FactorSH - 1.0;
                            fSHs[31] = float((rgb3.g >> 24) & MaskSH) * FactorSH - 1.0;
                            fSHs[32] = float((rgb3.g >> 19) & MaskSH) * FactorSH - 1.0;
                            fSHs[33]  = float((rgb3.g >> 14) & MaskSH) * FactorSH - 1.0;
                            fSHs[34] = float((rgb3.g >> 9) & MaskSH) * FactorSH - 1.0;
                            fSHs[35] = float((rgb3.g >> 4) & MaskSH) * FactorSH - 1.0;
                            fSHs[36] = float(( (rgb3.g << 1) | (rgb3.b >> 31) ) & MaskSH) * FactorSH - 1.0;
                            fSHs[37] = float((rgb3.b >> 26) & MaskSH) * FactorSH - 1.0;
                            fSHs[38] = float((rgb3.b >> 21) & MaskSH) * FactorSH - 1.0;
                            fSHs[39] = float((rgb3.b >> 16) & MaskSH) * FactorSH - 1.0;
                            fSHs[40] = float((rgb3.b >> 11) & MaskSH) * FactorSH - 1.0;
                            fSHs[41] = float((rgb3.b >> 6) & MaskSH) * FactorSH - 1.0;
                            fSHs[42] = float((rgb3.b >> 1) & MaskSH) * FactorSH - 1.0;
                            fSHs[43] = float(( (rgb3.b << 4) | (rgb3.a >> 28) ) & MaskSH) * FactorSH - 1.0;
                            fSHs[44] = float((rgb3.a >> 23) & MaskSH) * FactorSH - 1.0;
                        }
                    }
                }
            }

            vec3[15] sh;
            for (int i = 0; i < 15; ++i) {
                sh[i] = i < shCnt ? vec3(fSHs[i*3], fSHs[i*3 + 1], fSHs[i*3 + 2]) : vec3(0.0);
            }
            return sh;
        }

        // https://github.com/graphdeco-inria/gaussian-splatting/blob/main/utils/sh_utils.py
        vec3 evalSH(in vec3 v3Cen) {
            vec3 dir = normalize(v3Cen - cameraPosition);
            float x = dir.x;
            float y = dir.y;
            float z = dir.z;

            vec3[15] sh = readShDatas();
            vec3 result = SH_C1 * (-sh[0] * y + sh[1] * z - sh[2] * x);

            if (shDegree > 1) {
                float xx = x * x;
                float yy = y * y;
                float zz = z * z;
                float xy = x * y;
                float yz = y * z;
                float xz = x * z;

                result +=
                    sh[3] * (SH_C2[0] * xy) +
                    sh[4] * (SH_C2[1] * yz) +
                    sh[5] * (SH_C2[2] * (2.0 * zz - xx - yy)) +
                    sh[6] * (SH_C2[3] * xz) +
                    sh[7] * (SH_C2[4] * (xx - yy));

                if (shDegree > 2) {
                    result +=
                        sh[8]  * (SH_C3[0] * y * (3.0 * xx - yy)) +
                        sh[9]  * (SH_C3[1] * xy * z) +
                        sh[10] * (SH_C3[2] * y * (4.0 * zz - xx - yy)) +
                        sh[11] * (SH_C3[3] * z * (2.0 * zz - 3.0 * xx - 3.0 * yy)) +
                        sh[12] * (SH_C3[4] * x * (4.0 * zz - xx - yy)) +
                        sh[13] * (SH_C3[5] * z * (xx - yy)) +
                        sh[14] * (SH_C3[6] * x * (xx - 3.0 * yy));
                }
            }
            return result;
        }

        void main () {
            uvec4 cen, cov3d;
            if (bigSceneMode == 1) {
                if (usingIndex == 0){
                    cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
                    cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
                }else{
                    cen = texelFetch(splatTexture1, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
                    cov3d = texelFetch(splatTexture1, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
                }
            } else {
                cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
                cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
            }

            int waterMarkValue = int((cen.w & 65536u) >> 16u);
            int cenState = int(cen.w & 65535u);

            vec3 v3Cen = uintBitsToFloat(cen.xyz);

            if ( waterMarkValue == 1 && debugEffect == 1) {
                // 水印动画
                v3Cen.y += sin(performanceNow*0.002 + v3Cen.x) * 0.1;
            }

            vec4 cam = modelViewMatrix * vec4(v3Cen, 1.0);
            vec4 pos2d = projectionMatrix * cam;
            float clip = 1.2 * pos2d.w;
            if (pos2d.z < -clip || pos2d.x < -clip || pos2d.x > clip || pos2d.y < -clip || pos2d.y > clip
                || waterMarkValue == 1 && (showWaterMark == 0 || pointMode == 1) ) {
                gl_Position = vec4(0.0, 0.0, 2.0, 1.0);
                return;
            }

            float currentRadius = length(vec3(0.0, topY, 0.0) - v3Cen);
            if ( currentVisibleRadius > 0.0 && currentRadius > currentVisibleRadius ) {
                gl_Position = vec4(0.0, 0.0, 2.0, 1.0);
                return;
            }

            vec2 uh1 = unpackHalf2x16(cov3d.x), uh2 = unpackHalf2x16(cov3d.y), uh3 = unpackHalf2x16(cov3d.z);
            mat3 Vrk = mat3(uh1.x, uh1.y, uh2.x, uh1.y, uh2.y, uh3.x, uh2.x, uh3.x, uh3.y);

            float ZxZ = cam.z * cam.z;
            mat3 J_m3 = mat3(
                focal.x / cam.z, 0.0, -(focal.x * cam.x) / ZxZ,
                0.0, focal.y / cam.z, -(focal.y * cam.y) / ZxZ,
                0.0, 0.0, 0.0
            );

            mat3 T_m3 = transpose(mat3(modelViewMatrix)) * J_m3;
            mat3 cov2d = transpose(T_m3) * Vrk * T_m3;

            cov2d[0][0] += 0.3;
            cov2d[1][1] += 0.3;
            vec3 cov2Dv = vec3(cov2d[0][0], cov2d[0][1], cov2d[1][1]);
            float eigenValue1 =  0.5 * (cov2Dv.x + cov2Dv.z) + sqrt((cov2Dv.x + cov2Dv.z) * (cov2Dv.x + cov2Dv.z) / 4.0 - (cov2Dv.x * cov2Dv.z - cov2Dv.y * cov2Dv.y));
            float eigenValue2 = max( 0.5 * (cov2Dv.x + cov2Dv.z) - sqrt((cov2Dv.x + cov2Dv.z) * (cov2Dv.x + cov2Dv.z) / 4.0 - (cov2Dv.x * cov2Dv.z - cov2Dv.y * cov2Dv.y)), 0.0);
            float eigenValueOrig1 = eigenValue1;
            float eigenValueOrig2 = eigenValue2;

            int lightColorFlag = 0;
            if ( waterMarkValue == 0 ) {
                if ( pointMode == 1 ) {
                    eigenValue1 = eigenValue2 = 0.5;
                }

                if ( bigSceneMode == 0 && currentLightRadius > 0.0 ) {
                    // 仅小场景支持光圈过渡效果
                    if ( currentRadius < currentLightRadius && currentRadius > currentLightRadius * 0.9 ) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                        lightColorFlag = 1;
                    }
                    if ( currentRadius < currentLightRadius * 0.9 ){
                        if ( pointMode == 1 ){
                            eigenValue1 = eigenValueOrig1;
                            eigenValue2 = eigenValueOrig2;
                        } else {
                            eigenValue1 = eigenValue2 = 0.5;
                        }
                    }
                }
            }

            int iSelectPoint = 0;
            if (markPoint.w > 0.0 && length(vec3(markPoint.xyz) - v3Cen) < 0.000001){
                iSelectPoint = 1;
            }

            vPosition = vec3(position.xy, -1.0);
            vec2 eigenVector1 = normalize(vec2(cov2Dv.y, eigenValue1 - cov2Dv.x));
            if (iSelectPoint == 1){
                vColor = vec4(1.0, 1.0, 0.0, 1.0);
                eigenValue1 = eigenValue2 = 10.0;
                eigenVector1 = normalize(vec2(10.0, eigenValue1 - 10.0));
                vPosition.z = 1.0;
            } else if ( lightColorFlag == 1 ) {
                vColor = vec4(1.0, 1.0, 1.0, 0.2);
            } else if ( waterMarkValue == 1 ) {
                vColor = waterMarkColor;
            } else {
                vColor = vec4( float(cov3d.w & 0xFFu) / 255.0, float((cov3d.w >> 8) & 0xFFu) / 255.0, float((cov3d.w >> 16) & 0xFFu) / 255.0, float(cov3d.w >> 24) / 255.0 );
                if (shDegree > 0) {
                    vColor.rgb += evalSH(v3Cen);
                    vColor.rgb = clamp(vColor.rgb, vec3(0.), vec3(1.));
                }
            }

            vec2 eigenVector2 = vec2(eigenVector1.y, -eigenVector1.x);
            vec2 majorAxis = eigenVector1 * min(sqrt(2.0 * eigenValue1), 1024.0);
            vec2 minorAxis = eigenVector2 * min(sqrt(2.0 * eigenValue2), 1024.0);

            vec2 v2Center = vec2(pos2d) / pos2d.w;  // NDC坐标
            gl_Position = vec4(
                v2Center
                + vPosition.x * majorAxis / viewport
                + vPosition.y * minorAxis / viewport
                , 1.0, 1.0);

        }
    `
}
function Qa() {
  return `
        precision highp float;

        uniform float lightFactor;

        varying vec4 vColor;
        varying vec3 vPosition;

        void main(){
            float dtPos = -dot(vPosition.xy, vPosition.xy);
            if (dtPos < -4.0) discard;

            dtPos = vPosition.z > 0.0 ? 1.0 : exp(dtPos) * vColor.a;
            gl_FragColor = vec4(lightFactor * vColor.rgb, dtPos);
        }

    `
}
function Wa() {
  return `
        uniform vec2 viewport;
        uniform vec3 realFocusPosition;

        varying vec4 ndcPosition;
        varying vec4 ndcCenter;
        varying vec4 ndcFocusPosition;
        varying float vAngle;

        void main() {
            vec4 viewPosition = modelViewMatrix * vec4(position.xyz, 1.0);
            vec4 viewCenter = modelViewMatrix * vec4(0.0, 0.0, 0.0, 1.0);

            vec4 viewFocusPosition = modelViewMatrix * vec4(realFocusPosition, 1.0);

            ndcPosition = projectionMatrix * viewPosition;
            ndcPosition = ndcPosition * vec4(1.0 / ndcPosition.w);
            ndcCenter = projectionMatrix * viewCenter;
            ndcCenter = ndcCenter * vec4(1.0 / ndcCenter.w);

            ndcFocusPosition = projectionMatrix * viewFocusPosition;
            ndcFocusPosition = ndcFocusPosition * vec4(1.0 / ndcFocusPosition.w);


            // 计算角度
            vec2 screenPosition = vec2(ndcPosition) * viewport;
            vec2 screenCenter = vec2(ndcCenter) * viewport;
            vec2 screenVec = screenPosition - screenCenter;
            float angle = atan(screenVec.y, screenVec.x);

            // 将角度从弧度转换为度数
            vAngle = angle * (180.0 / 3.14159265) + 90.0;

            gl_Position = projectionMatrix * viewPosition;

        }
    `
}
function Ha() {
  return `
        uniform vec3 cycleColor;
        uniform vec2 viewport;
        uniform float opacity;

        varying vec4 ndcPosition;
        varying vec4 ndcCenter;
        varying vec4 ndcFocusPosition;
        varying float vAngle;

        void main() {
            vec2 screenPosition = vec2(ndcPosition) * viewport;
            vec2 screenCenter = vec2(ndcCenter) * viewport;

            vec2 screenVec = screenPosition - screenCenter;

            float projectedRadius = length(screenVec);

            float lineWidth = 0.0005 * viewport.y;
            float aaRange = 0.0025 * viewport.y;
            float radius = 0.06 * viewport.y;
            float radDiff = abs(projectedRadius - radius) - lineWidth;
            float alpha = 1.0 - clamp(radDiff / 5.0, 0.0, 1.0);

            // 将圆分成3段显示
            float segmentAngle = 120.0;
            if (mod(vAngle, segmentAngle) > segmentAngle * 0.8) {
                alpha = 0.0;
            }

            gl_FragColor = vec4(cycleColor.rgb, alpha * opacity);
        }
    `
}
const Zs = 'currentVisibleRadius',
  js = 'currentLightRadius',
  So = 'realFocusPosition',
  wn = 'splatShTexture12',
  vn = 'splatShTexture3',
  qs = 'performanceNow',
  Ya = 'waterMarkColor',
  Js = 'showWaterMark',
  Sn = 'splatTexture0',
  bn = 'splatTexture1',
  ei = 'bigSceneMode',
  ti = 'lightFactor',
  ni = 'debugEffect',
  si = 'usingIndex',
  Va = 'cycleColor',
  Ga = 'splatIndex',
  ii = 'pointMode',
  oi = 'markPoint',
  ai = 'shDegree',
  yt = 'viewport',
  Vn = 'opacity',
  ri = 'focal',
  ci = 'topY'
let V = 0
V++
const li = `$${V++}`
V++
V++
V++
const di = `$${V++}`,
  Xa = `$${V++}`,
  ui = `$${V++}`,
  Na = `$${V++}`,
  Ka = `$${V++}`,
  $a = `$${V++}`,
  Za = `$${V++}`,
  ja = `$${V++}`
V++
const qa = `$${V++}`,
  Ja = `$${V++}`
V++
const er = `$${V++}`
V++
const tr = `$${V++}`
V++
const nr = `$${V++}`,
  sr = `$${V++}`,
  ir = `$${V++}`,
  or = `$${V++}`,
  ar = `$${V++}`,
  rr = `$${V++}`,
  cr = `$${V++}`,
  lr = `$${V++}`
V++
function dr(s) {
  let e = !1
  const t = (l, h, m) => s.on(l, h, m),
    n = (l, ...h) => s.fire(l, ...h)
  let o = 0,
    a = 0
  const i = []
  let r = 0
  t(lo, () => r),
    t(Ls, async () => {
      const l = new kn()
      l.setIndex([0, 1, 2, 0, 2, 3])
      const h = new Float32Array(4 * 3),
        m = new Ge(h, 3)
      l.setAttribute('position', m),
        m.setXYZ(0, -2, -2, 0),
        m.setXYZ(1, -2, 2, 0),
        m.setXYZ(2, 2, 2, 0),
        m.setXYZ(3, 2, -2, 0),
        (m.needsUpdate = !0)
      let f = new kn().copy(l)
      const A = await n(ht)
      if (e) return
      const g = new Uint32Array(A),
        y = new Po(g, 1, !1)
      return (
        y.setUsage(Lo),
        (y.needsUpdate = !0),
        f.setAttribute(Ga, y),
        (f.instanceCount = 0),
        t(Bs, (E, M, S, P) => {
          n(zs, M),
            g.set(E, 0),
            y.clearUpdateRanges(),
            y.addUpdateRange(0, E.length),
            (y.needsUpdate = !0),
            (f.instanceCount = E.length),
            n(K),
            n(ce, { sortTime: `${S} / ${Date.now() - P}` })
        }),
        t(jo, () => f),
        t(Qs, () => {
          ;(y.array = null), f.dispose()
        }),
        f
      )
    }),
    t(Fs, async () => {
      const l = await n(ht)
      if (e) return
      const h = 1024 * 2,
        m = Math.ceil((2 * l) / h),
        f = n(I),
        A = new Zn({
          uniforms: n(Ps),
          vertexShader: Oa(),
          fragmentShader: Qa(),
          transparent: !0,
          alphaTest: 1,
          blending: Fo,
          depthTest: f.depthTest !== !1,
          // 是否启用深度测试。深度测试用于确保只有离相机更近的物体才会被渲染
          depthWrite: !1,
          // 是否将深度值写入深度缓冲区
          side: be,
        }),
        g = new Uint32Array(h * m * 4)
      let y = new Pe(g, h, m, Le, Fe)
      ;(y.internalFormat = 'RGBA32UI'), (y.needsUpdate = !0), (A.uniforms[Sn].value = y)
      const E = n(ne) ? m : 1,
        M = new Uint32Array(h * E * 4)
      let S = new Pe(M, h, E, Le, Fe)
      ;(S.internalFormat = 'RGBA32UI'), (S.needsUpdate = !0), (A.uniforms[bn].value = S)
      const P = await n(at, 1),
        T = new Uint32Array(h * P * 4)
      let D = new Pe(T, h, P, Le, Fe)
      ;(D.internalFormat = 'RGBA32UI'), (D.needsUpdate = !0), (A.uniforms[wn].value = D)
      const F = await n(at, 3),
        H = new Uint32Array(h * F * 4)
      let G = new Pe(H, h, F, Le, Fe)
      ;(G.internalFormat = 'RGBA32UI'),
        (G.needsUpdate = !0),
        (A.uniforms[vn].value = G),
        (A.needsUpdate = !0)
      let p = !1
      t(Us, (b) => {
        if (!n(ne)) {
          if (p && !b.renderSplatCount) return
          p = !b.renderSplatCount
        }
        const w = b.txdata
        b.txdata = null
        const k = new Pe(w, h, m, Le, Fe)
        ;(k.onUpdate = () => {
          ;(b.textureReady = !0), (b.textureReadyTime = Date.now()), d(b), n($i, b.renderSplatCount)
        }),
          (k.internalFormat = 'RGBA32UI'),
          (k.needsUpdate = !0),
          b.index ? ((A.uniforms[bn].value = k), (S = k)) : ((A.uniforms[Sn].value = k), (y = k)),
          (A.needsUpdate = !0),
          n(K)
      }),
        t(ro, async (b) => {
          if (n(ne) || !b || !b.length) return
          const w = new Uint32Array(h * (await n(at, 1)) * 4),
            k = new Uint8Array(w.buffer)
          for (let L = 0, B = 0; L < b.length; L++) k.set(b[L], B), (B += b[L].byteLength)
          const _ = new Pe(w, h, P, Le, Fe)
          ;(_.internalFormat = 'RGBA32UI'),
            (_.needsUpdate = !0),
            (A.uniforms[wn].value = _),
            (A.needsUpdate = !0),
            n(K)
        }),
        t(co, async (b) => {
          if (n(ne) || !b || !b.length) return
          const w = new Uint32Array(h * (await n(at, 3)) * 4),
            k = new Uint8Array(w.buffer)
          for (let L = 0, B = 0; L < b.length; L++) k.set(b[L], B), (B += b[L].byteLength)
          const _ = new Pe(w, h, P, Le, Fe)
          ;(_.internalFormat = 'RGBA32UI'),
            (_.needsUpdate = !0),
            (A.uniforms[vn].value = _),
            (A.needsUpdate = !0),
            n(K)
        }),
        t(Rs, () => A),
        t(hn, () => {
          const b = n(O),
            { width: w, height: k } = n(We),
            _ = Math.abs(b.projectionMatrix.elements[0]) * 0.5 * w,
            L = Math.abs(b.projectionMatrix.elements[5]) * 0.5 * k,
            B = n(Rs)
          B.uniforms[ri].value.set(_, L), (B.uniformsNeedUpdate = !0), n(K)
        }),
        t(pn, () => {
          const { width: b, height: w } = n(We)
          A.uniforms[yt].value.set(b, w), (A.uniformsNeedUpdate = !0), n(K)
        }),
        t(zs, (b) => {
          ;(A.uniforms[si].value = b), (A.uniformsNeedUpdate = !0), n(K)
        }),
        t(Ne, (b) => {
          const w = n(I)
          b === void 0 && (b = !w.pointcloudMode),
            (A.uniforms[ii].value = b ? 1 : 0),
            (A.uniformsNeedUpdate = !0),
            (w.pointcloudMode = b),
            n(K),
            w.viewerEvents && (w.viewerEvents.fire(I).pointcloudMode = b)
        }),
        t(Os, (b) => {
          ;(A.uniforms[ei].value = b ? 1 : 0), (A.uniformsNeedUpdate = !0)
          const w = n(I)
          ;(w.bigSceneMode = b), n(K)
        }),
        t(pt, (b) => {
          ;(A.uniforms[ti].value = b), (A.uniformsNeedUpdate = !0)
          const w = n(I)
          ;(w.lightFactor = b), n(K)
        })
      let C = !1
      return (
        t(zi, (b) => {
          n(ne) || C || ((C = !0), (A.uniforms[ci].value = b), (A.uniformsNeedUpdate = !0), n(K))
        }),
        t(Ct, (b) => {
          ;(A.uniforms[Zs].value = b), (A.uniformsNeedUpdate = !0), n(K)
        }),
        t(Mt, (b) => {
          ;(A.uniforms[js].value = b), (A.uniformsNeedUpdate = !0), n(K)
        }),
        t(Wt, (b, w, k, _) => {
          ;(A.uniforms[oi].value = [b, w, k, _ ? 1 : -1]), (A.uniformsNeedUpdate = !0), n(K)
        }),
        t(Pn, (b = !0) => {
          ;(A.uniforms[Js].value = b ? 1 : 0), (A.uniformsNeedUpdate = !0), n(K)
        }),
        t(Oi, (b) => {
          ;(A.uniforms[qs].value = b), (A.uniformsNeedUpdate = !0)
        }),
        t(qo, (b) => {
          ;(A.uniforms[ni].value = b), (A.uniformsNeedUpdate = !0)
        }),
        t(ns, async (b) => {
          if (n(ne)) return
          const w = await n(ps)
          b < 0 && (b = 0),
            b > w && (b = w),
            (r = b),
            (A.uniforms[ai].value = b),
            (A.uniformsNeedUpdate = !0),
            n(ce, { shDegree: `${b} / max ${w}` }),
            n(K)
        }),
        t(Ws, () => {
          A.dispose(), y && y.dispose(), S && S.dispose(), D && D.dispose(), G && G.dispose()
        }),
        A
      )
    }),
    t(Ui, async () => {
      const l = new se(await n(Ls), await n(Fs))
      return n(hn), n(pn), n(Os, n(ne)), n(Ne, n(Ln)), l
    })
  function c() {
    n(hn), n(pn)
  }
  window.addEventListener('resize', c),
    t(Gi, () => {
      ;(e = !0), window.removeEventListener('resize', c), n(Qs), n(Ws)
    }),
    t(Xi, async () => {
      if (n(ne)) return
      let l = 0.01,
        h = 0.01,
        m = !1,
        f = 0
      n(Ct, h),
        n(
          ut,
          () => {
            if (e || a <= h) return
            ;(h += (a - h) * l), n(Ct, h)
            let A = n(Zi)
            A && !f && (f = Date.now())
            let g = h / o
            A && (g > 0.9 || Date.now() - f > 2500)
              ? (n(Ln) && n(Fn, !0), n(Ct, 0), (m = !0))
              : A && g > 0.7
                ? (l = Math.min(l * 1.2, 0.3))
                : A && g > 0.5
                  ? (l = Math.min(l * 1.2, 0.2))
                  : g > 0.4 && (l = Math.min(l * 1.05, 0.1))
          },
          () => !e && !m,
          3,
        )
    }),
    t(Fn, (l = !1) => {
      if (n(ne)) return
      const h = n(I)
      if (h.disableTransitionEffect) {
        n(Ne, !h.pointcloudMode),
          n(Mt, 0),
          l && n(I)?.viewerEvents?.fire(qe),
          n(I)?.viewerEvents?.fire(On)
        return
      }
      for (; i.length; ) i.pop().stop = !0
      const m = o * 1e-3
      let f = {
        currentPointMode: h.pointcloudMode,
        stepRate: 15e-4,
        currentLightRadius: m,
        stop: !1,
      }
      i.push(f),
        n(
          ut,
          () => {
            e ||
              ((f.currentLightRadius += o * f.stepRate),
              n(Mt, f.currentLightRadius),
              f.currentLightRadius > o
                ? (n(Ne, !f.currentPointMode),
                  n(Mt, 0),
                  (f.stop = !0),
                  i.length === 1 && i[0] === f && i.pop(),
                  l && n(I)?.viewerEvents?.fire(qe),
                  n(I)?.viewerEvents?.fire(On))
                : f.currentLightRadius / o < 0.4
                  ? (f.stepRate = Math.min(f.stepRate * 1.02, 0.03))
                  : (f.stepRate *= 1.04))
          },
          () => !e && !f.stop,
        )
    }),
    t(Ps, () => ({
      [Sn]: { type: 't', value: null },
      [bn]: { type: 't', value: null },
      [wn]: { type: 't', value: null },
      [vn]: { type: 't', value: null },
      [ri]: { type: 'v2', value: new ee() },
      [yt]: { type: 'v2', value: new ee() },
      [si]: { type: 'int', value: 0 },
      [ii]: { type: 'int', value: 0 },
      [ni]: { type: 'int', value: 1 },
      [ei]: { type: 'int', value: 0 },
      [ai]: { type: 'int', value: 0 },
      [ti]: { type: 'float', value: 1 },
      [ci]: { type: 'float', value: 0 },
      [Zs]: { type: 'float', value: 0 },
      [js]: { type: 'float', value: 0 },
      [oi]: { type: 'v4', value: new Ce(0, 0, 0, -1) },
      [qs]: { type: 'float', value: performance.now() },
      [Ya]: { type: 'v4', value: new Ce(1, 1, 0, 0.5) },
      [Js]: { type: 'int', value: 1 },
    }))
  const u = n(_i)
  ;(u.onmessage = (l) => {
    const h = l.data
    h[li] && n(Bs, h[li], h[di], h[Za], h[$a], h[ui])
  }),
    t(ao, (l, h, m) => {
      n(ne) || ((a = h), (o = m)), n(Us, l)
    })
  function d(l) {
    const h = l.xyz.slice(0)
    u.postMessage(
      {
        [ja]: !0,
        [tr]: h,
        [lr]: l.watermarkCount,
        [di]: l.index,
        [Xa]: l.version,
        [ui]: l.renderSplatCount,
        [Na]: l.visibleSplatCount,
        [Ka]: l.modelSplatCount,
        [nr]: l.minX,
        [sr]: l.maxX,
        [ir]: l.minY,
        [or]: l.maxY,
        [ar]: l.minZ,
        [rr]: l.maxZ,
      },
      [h.buffer],
    )
  }
}
function ur(s) {
  const e = (o, ...a) => s.fire(o, ...a),
    t = (o, a, i) => s.on(o, a, i),
    n = new Set(wa.split(''))
  t(so, async (o = '', a = !0, i = !0) => {
    const r = o.trim().substring(0, 100)
    let c = await e(Ei, r),
      u = []
    for (let M = 0; M < c.length; M++) {
      let S = [],
        P = c[M]
      for (let T = 0; T < P.length; T++)
        S.push([((P[T] % 20) - 10) * 0.02, (((P[T] / 20) | 0) - 10) * 0.02])
      u.push(S)
    }
    let d = [],
      l = r.split('')
    for (let M = 0; M < l.length; M++) d[M] = n.has(l[M]) ? 0.22 : 0.4
    let h = (l.length / 2) | 0,
      m = d[h] / 2,
      f = !(l.length % 2),
      A = f ? 0 : -m
    for (let M = h - 1; M >= 0; M--) {
      A -= d[M] / 2
      for (let S of u[M]) S[0] += A
      A -= d[M] / 2
    }
    ;(m = d[h] / 2), (A = f ? 0 : m)
    for (let M = u.length - h; M < u.length; M++) {
      A += d[M] / 2
      for (let S of u[M]) S[0] += A
      A += d[M] / 2
    }
    let g = 0
    for (let M of u) g += M.length
    const y = new Uint8Array(g * Q)
    let E = 0
    for (let M of u) for (let S of M) y.set(await Da(S[0], S[1], a, i), Q * E++)
    return y
  })
}
function vs(s) {
  const e = (t, n, o) => s.on(t, n, o)
  e(Be, (t) => {
    t.url
    const n = { ...t }
    delete n.url
    const o = JSON.stringify(n, null, 2)
    console.info(o)
  }),
    e(Ei, (t = '') => {
      const n = 'https://reall3d.com/gsfont/api/getGaussianText',
        o = new FormData()
      return (
        o.append('text', t.substring(0, 100)),
        o.append('ver', rn),
        new Promise((a) => {
          fetch(n, { method: 'POST', body: o })
            .then((i) => (i.ok ? i.json() : {}))
            .then((i) => (i.success ? a(JSON.parse(i.data)) : a([])))
            .catch((i) => a([]))
        })
      )
    })
}
function hr(s) {
  let e = !1
  const t = (r, c, u) => s.on(r, c, u),
    n = (r, ...c) => s.fire(r, ...c)
  t(Qi, () => (e = !0))
  const o = /* @__PURE__ */ new Map(),
    a = /* @__PURE__ */ new Map()
  t(Nt, () => n(de) && o.set(Date.now(), 1)),
    t(Kt, () => n(de) && n(Ke, o)),
    t($t, () => n(de) && a.set(Date.now(), 1)),
    t(Zt, () => n(de) && n(Ke, a)),
    t(
      He,
      () => {
        e ||
          (n($t),
          n(de) &&
            n(ce, {
              fov: n(wt),
              position: n(Ue, n(Qe)),
              lookAt: n(Ue, n(Ee)),
              lookUp: n(Ue, n(je)),
            }))
      },
      !0,
    )
  let i = 0
  t(
    $e,
    () => {
      e || (n(Pi), n(de) && (n(Nt), !(i++ % 5) && n(ce, { fps: n(Kt), realFps: n(Zt) })))
    },
    !0,
  ),
    t(Ke, (r) => {
      let c = [],
        u = Date.now(),
        d = 0
      for (const l of r.keys()) u - l <= 1e3 ? d++ : c.push(l)
      return c.forEach((l) => r.delete(l)), Math.min(d, 30)
    }),
    window.addEventListener('beforeunload', () => n(on))
}
function pr(s) {
  const e = { ...s }
  return (
    e.bigSceneMode ?? (e.bigSceneMode = !1),
    e.pointcloudMode ?? (e.pointcloudMode = !e.bigSceneMode),
    e.lightFactor ?? (e.lightFactor = 1),
    e.name ?? (e.name = ''),
    e.showWatermark ?? (e.showWatermark = !0),
    e
  )
}
function hi(s) {
  const e = { ...s }
  return (
    (e.position = e.position ? [...e.position] : [0, -5, 15]),
    (e.lookAt = e.lookAt ? [...e.lookAt] : [0, 0, 0]),
    (e.lookUp = e.lookUp ? [...e.lookUp] : [0, -1, 0]),
    e.fov ?? (e.fov = 45),
    e.near ?? (e.near = 1e-3),
    e.far ?? (e.far = 1e3),
    e.enableDamping ?? (e.enableDamping = !0),
    e.autoRotate ?? (e.autoRotate = !0),
    e.enableZoom ?? (e.enableZoom = !0),
    e.enableRotate ?? (e.enableRotate = !0),
    e.enablePan ?? (e.enablePan = !0),
    e.enableKeyboard ?? (e.enableKeyboard = !0),
    e.bigSceneMode ?? (e.bigSceneMode = !1),
    e.pointcloudMode ?? (e.pointcloudMode = !e.bigSceneMode),
    e.lightFactor ?? (e.lightFactor = 1.1),
    e.maxRenderCountOfMobile ??
      (e.maxRenderCountOfMobile = e.bigSceneMode ? 256 * 1e4 : 384 * 10240),
    e.maxRenderCountOfPc ?? (e.maxRenderCountOfPc = e.bigSceneMode ? 320 * 1e4 : 384 * 1e4),
    e.debugMode ?? (e.debugMode = location.protocol === 'http:' || /^test\./.test(location.host)),
    e.markMode ?? (e.markMode = !1),
    e.markVisible ?? (e.markVisible = !0),
    e.meterScale ?? (e.meterScale = 1),
    e.background ?? (e.background = '#000000'),
    e
  )
}
function fr(s) {
  let e
  s.root
    ? (e =
        typeof s.root == 'string'
          ? document.querySelector(s.root) || document.querySelector('#gsviewer')
          : s.root)
    : (e = document.querySelector('#gsviewer')),
    e || ((e = document.createElement('div')), (e.id = 'gsviewer'), document.body.appendChild(e))
  let t = null
  return (
    s.renderer
      ? (t = s.renderer)
      : ((t = new Ci({
          antialias: !1,
          stencil: !0,
          logarithmicDepthBuffer: !0,
          precision: 'highp',
        })),
        t.setSize(e.clientWidth, e.clientHeight),
        t.setPixelRatio(Math.min(devicePixelRatio, 2)),
        (s.renderer = t)),
    t.domElement.classList.add('gsviewer-canvas'),
    e.appendChild(t.domElement),
    t
  )
}
function gr(s) {
  let e = s.camera
  if (!e) {
    const t = s.renderer.domElement,
      n = t.width / t.height
    let o = new x().fromArray(s.lookUp),
      a = new x().fromArray(s.lookAt),
      i = new x().fromArray(s.position)
    ;(e = new Mi(s.fov, n, s.near, s.far)),
      e.position.copy(i),
      e.up.copy(o).normalize(),
      e.lookAt(a),
      (s.camera = e)
  }
  return s.camera
}
function mr(s) {
  const { renderer: e, scene: t } = s,
    n = { renderer: e, scene: t }
  return (
    (n.viewerEvents = s.viewerEvents),
    (n.debugMode = s.debugMode),
    (n.renderer = s.renderer),
    (n.scene = s.scene),
    (n.controls = s.controls),
    (n.bigSceneMode = s.bigSceneMode),
    (n.pointcloudMode = s.pointcloudMode),
    (n.maxRenderCountOfMobile = s.maxRenderCountOfMobile),
    (n.maxRenderCountOfPc = s.maxRenderCountOfPc),
    (n.lightFactor = s.lightFactor),
    (n.shDegree = s.shDegree),
    (n.disableTransitionEffect = s.disableTransitionEffect),
    n
  )
}
function yr(s) {
  const e = (i, r, c) => s.on(i, r, c),
    t = (i, ...r) => s.fire(i, ...r),
    n =
      '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',
    o = URL.createObjectURL(new Blob([atob(n)], { type: 'text/javascript' })),
    a = new Worker(new URL(o, import.meta.url), { type: 'module' })
  e(_i, () => a),
    e(Ri, () => a.postMessage({ [er]: t(Fi) })),
    e(Bi, () => a.terminate()),
    (async () => a.postMessage({ [cr]: !0, [Ja]: await t(ht), [qa]: t(ne) }))()
}
class he extends se {
  /**
   * 构造函数
   * @param options 渲染器、场景、相机都应该传入
   */
  constructor(e) {
    super(), (this.isSplatMesh = !0), (this.disposed = !1)
    const t = new fs(),
      n = (r, c, u) => t.on(r, c, u),
      o = (r, ...c) => t.fire(r, ...c),
      a = pr(e),
      i = a.controls.object
    n(I, () => a),
      n(et, () => a.renderer.domElement),
      n(O, () => i),
      n(wt, () => i.fov),
      n(Qe, (r = !1) => (r ? i.position.clone() : i.position)),
      n(Ee, (r = !1) => (r ? a.controls.target.clone() : a.controls.target)),
      n(Fi, () =>
        i.projectionMatrix.clone().multiply(i.matrixWorldInverse).multiply(this.matrix).toArray(),
      ),
      n(Ko, () => i.projectionMatrix.clone().multiply(i.matrixWorldInverse)),
      n(da, () => i.getWorldDirection(new x()).toArray()),
      n(ft, () => a.renderer),
      n(z, () => a.scene),
      n(ne, () => a.bigSceneMode),
      n(Ln, () => a.pointcloudMode),
      n(ve, () => this),
      n(K, () => a.viewerEvents?.fire(W)),
      ws(t),
      vs(t),
      za(t),
      yr(t),
      dr(t),
      ur(t),
      (this.name = `${a.name || this.id}`),
      (this.events = t),
      (this.opts = a),
      (async () => (
        this.copy(await t.fire(Ui)),
        a.matrix && this.applyMatrix4(a.matrix),
        (this.frustumCulled = !1),
        (this.onBeforeRender = () => {
          o(Ri), o(Oi, performance.now())
        }),
        (this.onAfterRender = () => {
          o(Yi, 1e4) && o(K)
        })
      ))()
  }
  /**
   * 设定或者获取最新配置项
   * @param opts 配置项
   * @returns 最新配置项
   */
  options(e) {
    if (this.disposed) return
    const t = (o, ...a) => this.events.fire(o, ...a),
      n = this.opts
    return (
      e &&
        (e.pointcloudMode !== void 0 && t(Ne, e.pointcloudMode),
        e.lightFactor !== void 0 && t(pt, e.lightFactor),
        e.maxRenderCountOfMobile !== void 0 &&
          (n.maxRenderCountOfMobile = e.maxRenderCountOfMobile),
        e.maxRenderCountOfPc !== void 0 && (n.maxRenderCountOfPc = e.maxRenderCountOfPc),
        t(K)),
      { ...n }
    )
  }
  /**
   * 添加渲染指定高斯模型
   * @param opts 高斯模型选项
   * @param meta 元数据
   */
  async addModel(e, t) {
    this.disposed || ((this.meta = t), await this.events.fire(Hi, e, t))
  }
  fire(e, ...t) {
    if (!this.disposed) return this.events.fire(e, ...t)
  }
  /**
   * 销毁
   */
  dispose() {
    if (this.disposed) return
    this.disposed = !0
    const e = (t, ...n) => this.events.fire(t, ...n)
    e(Ie, this),
      e(z).remove(this),
      e(ss),
      e(Vi),
      e(Bi),
      e(Gi),
      this.events.clear(),
      (this.events = null),
      (this.opts = null),
      (this.onAfterRender = null)
  }
}
class Ar extends jn {
  constructor(
    e = new x(0, 0, 1),
    t = new x(0, 0, 0),
    n = 1,
    o = 0.1,
    a = 16776960,
    i = n * 0.2,
    r = i * 0.2,
  ) {
    super(), (this._axis = new x()), (this.type = 'ArrowHelper')
    const c = new xs(o, o, n, 32)
    c.translate(0, n / 2, 0)
    const u = new xs(0, r, i, 32)
    u.translate(0, n, 0), this.position.copy(t)
    const d = new ke({ color: a, toneMapped: !1 })
    ;(d.side = be),
      (this.line = new se(c, d)),
      (this.line.matrixAutoUpdate = !1),
      (this.line.ignoreIntersect = !0),
      this.add(this.line)
    const l = new ke({ color: a, toneMapped: !1 })
    ;(l.side = be),
      (this.cone = new se(u, l)),
      (this.cone.matrixAutoUpdate = !1),
      (this.cone.ignoreIntersect = !0),
      this.add(this.cone),
      this.setDirection(e),
      (this.renderOrder = 99999)
  }
  setDirection(e) {
    if (e.y > 0.99999) this.quaternion.set(0, 0, 0, 1)
    else if (e.y < -0.99999) this.quaternion.set(1, 0, 0, 0)
    else {
      this._axis.set(e.z, 0, -e.x).normalize()
      const t = Math.acos(e.y)
      this.quaternion.setFromAxisAngle(this._axis, t)
    }
  }
  setColor(e) {
    this.line.material.color.set(e), this.cone.material.color.set(e)
  }
  copy(e) {
    return super.copy(e, !1), this.line.copy(e.line), this.cone.copy(e.cone), this
  }
  dispose() {
    this.line.geometry.dispose(),
      this.line.material.dispose(),
      this.cone.geometry.dispose(),
      this.cone.material.dispose()
  }
}
function wr(s) {
  const e = (A, g, y) => s.on(A, g, y),
    t = (A, ...g) => s.fire(A, ...g),
    n = new Ro(1, 1)
  n.rotateX(-Math.PI / 2)
  const o = new ke({ color: 16777215 })
  ;(o.transparent = !0), (o.opacity = 0.6), (o.depthTest = !1), (o.depthWrite = !1), (o.side = be)
  const a = new se(n, o)
  a.ignoreIntersect = !0
  const i = new x(0, -1, 0)
  i.normalize()
  const r = new x(0, 0, 0),
    c = 0.5,
    u = 0.01,
    d = 16777062,
    l = 0.1,
    h = 0.03,
    m = new Ar(i, r, c, u, d, l, h),
    f = new jn()
  f.add(a),
    f.add(m),
    (f.renderOrder = 99999),
    (a.renderOrder = 99999),
    (f.visible = !1),
    t(z).add(f),
    e(sa, () => f),
    e(Yt, (A) => {
      t(qt, !0), (f.visible = A === void 0 ? !f.visible : A), t(W)
    }),
    e(os, () => f.visible),
    e(qt, (A = !1) => {
      if (A || f.visible) {
        const g = new Xt(),
          y = new x(0, -1, 0)
        g.setFromUnitVectors(y, t(je)), f.position.copy(t(Ee)), f.quaternion.copy(g)
      }
    })
}
const pi = { type: 'change' },
  Ss = { type: 'start' },
  bo = { type: 'end' },
  Rt = new Uo(),
  fi = new zo(),
  vr = Math.cos(70 * xe.DEG2RAD),
  N = new x(),
  re = 2 * Math.PI,
  X = {
    NONE: -1,
    ROTATE: 0,
    DOLLY: 1,
    PAN: 2,
    TOUCH_ROTATE: 3,
    TOUCH_PAN: 4,
    TOUCH_DOLLY_PAN: 5,
    TOUCH_DOLLY_ROTATE: 6,
  },
  Cn = 1e-6
class Co extends Bo {
  constructor(e, t = null) {
    super(e, t),
      (this.state = X.NONE),
      (this.enabled = !0),
      (this.target = new x()),
      (this.cursor = new x()),
      (this.minDistance = 0),
      (this.maxDistance = 1 / 0),
      (this.minZoom = 0),
      (this.maxZoom = 1 / 0),
      (this.minTargetRadius = 0),
      (this.maxTargetRadius = 1 / 0),
      (this.minPolarAngle = 0),
      (this.maxPolarAngle = Math.PI),
      (this.minAzimuthAngle = -1 / 0),
      (this.maxAzimuthAngle = 1 / 0),
      (this.enableDamping = !1),
      (this.dampingFactor = 0.05),
      (this.enableZoom = !0),
      (this.zoomSpeed = 1),
      (this.enableRotate = !0),
      (this.rotateSpeed = 1),
      (this.enablePan = !0),
      (this.panSpeed = 1),
      (this.screenSpacePanning = !0),
      (this.keyPanSpeed = 7),
      (this.zoomToCursor = !1),
      (this.autoRotate = !1),
      (this.autoRotateSpeed = 2),
      (this.keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }),
      (this.mouseButtons = { LEFT: Se.ROTATE, MIDDLE: Se.DOLLY, RIGHT: Se.PAN }),
      (this.touches = { ONE: Me.ROTATE, TWO: Me.DOLLY_PAN }),
      (this.target0 = this.target.clone()),
      (this.position0 = this.object.position.clone()),
      (this.zoom0 = this.object.zoom),
      (this._domElementKeyEvents = null),
      (this._lastPosition = new x()),
      (this._lastQuaternion = new Xt()),
      (this._lastTargetPosition = new x()),
      (this._quat = new Xt().setFromUnitVectors(e.up, new x(0, 1, 0))),
      (this._quatInverse = this._quat.clone().invert()),
      (this._spherical = new Ts()),
      (this._sphericalDelta = new Ts()),
      (this._scale = 1),
      (this._panOffset = new x()),
      (this._rotateStart = new ee()),
      (this._rotateEnd = new ee()),
      (this._rotateDelta = new ee()),
      (this._panStart = new ee()),
      (this._panEnd = new ee()),
      (this._panDelta = new ee()),
      (this._dollyStart = new ee()),
      (this._dollyEnd = new ee()),
      (this._dollyDelta = new ee()),
      (this._dollyDirection = new x()),
      (this._mouse = new ee()),
      (this._performCursorZoom = !1),
      (this._pointers = []),
      (this._pointerPositions = {}),
      (this._controlActive = !1),
      (this._onPointerMove = br.bind(this)),
      (this._onPointerDown = Sr.bind(this)),
      (this._onPointerUp = Cr.bind(this)),
      (this._onContextMenu = _r.bind(this)),
      (this._onMouseWheel = Tr.bind(this)),
      (this._onKeyDown = kr.bind(this)),
      (this._onTouchStart = Er.bind(this)),
      (this._onTouchMove = Dr.bind(this)),
      (this._onMouseDown = Mr.bind(this)),
      (this._onMouseMove = xr.bind(this)),
      (this._interceptControlDown = Ir.bind(this)),
      (this._interceptControlUp = Pr.bind(this)),
      this.domElement !== null && this.connect(),
      this.update()
  }
  connect() {
    this.domElement.addEventListener('pointerdown', this._onPointerDown),
      this.domElement.addEventListener('pointercancel', this._onPointerUp),
      this.domElement.addEventListener('contextmenu', this._onContextMenu),
      this.domElement.addEventListener('wheel', this._onMouseWheel, { passive: !1 }),
      this.domElement
        .getRootNode()
        .addEventListener('keydown', this._interceptControlDown, { passive: !0, capture: !0 }),
      (this.domElement.style.touchAction = 'none')
  }
  disconnect() {
    this.domElement.removeEventListener('pointerdown', this._onPointerDown),
      this.domElement.removeEventListener('pointermove', this._onPointerMove),
      this.domElement.removeEventListener('pointerup', this._onPointerUp),
      this.domElement.removeEventListener('pointercancel', this._onPointerUp),
      this.domElement.removeEventListener('wheel', this._onMouseWheel),
      this.domElement.removeEventListener('contextmenu', this._onContextMenu),
      this.stopListenToKeyEvents(),
      this.domElement
        .getRootNode()
        .removeEventListener('keydown', this._interceptControlDown, { capture: !0 }),
      (this.domElement.style.touchAction = 'auto')
  }
  dispose() {
    this.disconnect()
  }
  getPolarAngle() {
    return this._spherical.phi
  }
  getAzimuthalAngle() {
    return this._spherical.theta
  }
  getDistance() {
    return this.object.position.distanceTo(this.target)
  }
  listenToKeyEvents(e) {
    e.addEventListener('keydown', this._onKeyDown), (this._domElementKeyEvents = e)
  }
  stopListenToKeyEvents() {
    this._domElementKeyEvents !== null &&
      (this._domElementKeyEvents.removeEventListener('keydown', this._onKeyDown),
      (this._domElementKeyEvents = null))
  }
  saveState() {
    this.target0.copy(this.target),
      this.position0.copy(this.object.position),
      (this.zoom0 = this.object.zoom)
  }
  reset() {
    this.target.copy(this.target0),
      this.object.position.copy(this.position0),
      (this.object.zoom = this.zoom0),
      this.object.updateProjectionMatrix(),
      this.dispatchEvent(pi),
      this.update(),
      (this.state = X.NONE)
  }
  update(e = null) {
    const t = this.object.position
    N.copy(t).sub(this.target),
      N.applyQuaternion(this._quat),
      this._spherical.setFromVector3(N),
      this.autoRotate && this.state === X.NONE && this._rotateLeft(this._getAutoRotationAngle(e)),
      this.enableDamping
        ? ((this._spherical.theta += this._sphericalDelta.theta * this.dampingFactor),
          (this._spherical.phi += this._sphericalDelta.phi * this.dampingFactor))
        : ((this._spherical.theta += this._sphericalDelta.theta),
          (this._spherical.phi += this._sphericalDelta.phi))
    let n = this.minAzimuthAngle,
      o = this.maxAzimuthAngle
    isFinite(n) &&
      isFinite(o) &&
      (n < -Math.PI ? (n += re) : n > Math.PI && (n -= re),
      o < -Math.PI ? (o += re) : o > Math.PI && (o -= re),
      n <= o
        ? (this._spherical.theta = Math.max(n, Math.min(o, this._spherical.theta)))
        : (this._spherical.theta =
            this._spherical.theta > (n + o) / 2
              ? Math.max(n, this._spherical.theta)
              : Math.min(o, this._spherical.theta))),
      (this._spherical.phi = Math.max(
        this.minPolarAngle,
        Math.min(this.maxPolarAngle, this._spherical.phi),
      )),
      this._spherical.makeSafe(),
      this.enableDamping === !0
        ? this.target.addScaledVector(this._panOffset, this.dampingFactor)
        : this.target.add(this._panOffset),
      this.target.sub(this.cursor),
      this.target.clampLength(this.minTargetRadius, this.maxTargetRadius),
      this.target.add(this.cursor)
    let a = !1
    if ((this.zoomToCursor && this._performCursorZoom) || this.object.isOrthographicCamera)
      this._spherical.radius = this._clampDistance(this._spherical.radius)
    else {
      const i = this._spherical.radius
      ;(this._spherical.radius = this._clampDistance(this._spherical.radius * this._scale)),
        (a = i != this._spherical.radius)
    }
    if (
      (N.setFromSpherical(this._spherical),
      N.applyQuaternion(this._quatInverse),
      t.copy(this.target).add(N),
      this.object.lookAt(this.target),
      this.enableDamping === !0
        ? ((this._sphericalDelta.theta *= 1 - this.dampingFactor),
          (this._sphericalDelta.phi *= 1 - this.dampingFactor),
          this._panOffset.multiplyScalar(1 - this.dampingFactor))
        : (this._sphericalDelta.set(0, 0, 0), this._panOffset.set(0, 0, 0)),
      this.zoomToCursor && this._performCursorZoom)
    ) {
      let i = null
      if (this.object.isPerspectiveCamera) {
        const r = N.length()
        i = this._clampDistance(r * this._scale)
        const c = r - i
        this.object.position.addScaledVector(this._dollyDirection, c),
          this.object.updateMatrixWorld(),
          (a = !!c)
      } else if (this.object.isOrthographicCamera) {
        const r = new x(this._mouse.x, this._mouse.y, 0)
        r.unproject(this.object)
        const c = this.object.zoom
        ;(this.object.zoom = Math.max(
          this.minZoom,
          Math.min(this.maxZoom, this.object.zoom / this._scale),
        )),
          this.object.updateProjectionMatrix(),
          (a = c !== this.object.zoom)
        const u = new x(this._mouse.x, this._mouse.y, 0)
        u.unproject(this.object),
          this.object.position.sub(u).add(r),
          this.object.updateMatrixWorld(),
          (i = N.length())
      } else
        console.warn(
          'WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.',
        ),
          (this.zoomToCursor = !1)
      i !== null &&
        (this.screenSpacePanning
          ? this.target
              .set(0, 0, -1)
              .transformDirection(this.object.matrix)
              .multiplyScalar(i)
              .add(this.object.position)
          : (Rt.origin.copy(this.object.position),
            Rt.direction.set(0, 0, -1).transformDirection(this.object.matrix),
            Math.abs(this.object.up.dot(Rt.direction)) < vr
              ? this.object.lookAt(this.target)
              : (fi.setFromNormalAndCoplanarPoint(this.object.up, this.target),
                Rt.intersectPlane(fi, this.target))))
    } else if (this.object.isOrthographicCamera) {
      const i = this.object.zoom
      ;(this.object.zoom = Math.max(
        this.minZoom,
        Math.min(this.maxZoom, this.object.zoom / this._scale),
      )),
        i !== this.object.zoom && (this.object.updateProjectionMatrix(), (a = !0))
    }
    return (
      (this._scale = 1),
      (this._performCursorZoom = !1),
      a ||
      this._lastPosition.distanceToSquared(this.object.position) > Cn ||
      8 * (1 - this._lastQuaternion.dot(this.object.quaternion)) > Cn ||
      this._lastTargetPosition.distanceToSquared(this.target) > Cn
        ? (this.dispatchEvent(pi),
          this._lastPosition.copy(this.object.position),
          this._lastQuaternion.copy(this.object.quaternion),
          this._lastTargetPosition.copy(this.target),
          !0)
        : !1
    )
  }
  _getAutoRotationAngle(e) {
    return e !== null ? (re / 60) * this.autoRotateSpeed * e : (re / 60 / 60) * this.autoRotateSpeed
  }
  _getZoomScale(e) {
    const t = Math.abs(e * 0.01)
    return Math.pow(0.95, this.zoomSpeed * t)
  }
  _rotateLeft(e) {
    this._sphericalDelta.theta -= e
  }
  _rotateUp(e) {
    this._sphericalDelta.phi -= e
  }
  _panLeft(e, t) {
    N.setFromMatrixColumn(t, 0), N.multiplyScalar(-e), this._panOffset.add(N)
  }
  _panUp(e, t) {
    this.screenSpacePanning === !0
      ? N.setFromMatrixColumn(t, 1)
      : (N.setFromMatrixColumn(t, 0), N.crossVectors(this.object.up, N)),
      N.multiplyScalar(e),
      this._panOffset.add(N)
  }
  // deltaX and deltaY are in pixels; right and down are positive
  _pan(e, t) {
    const n = this.domElement
    if (this.object.isPerspectiveCamera) {
      const o = this.object.position
      N.copy(o).sub(this.target)
      let a = N.length()
      ;(a *= Math.tan(((this.object.fov / 2) * Math.PI) / 180)),
        this._panLeft((2 * e * a) / n.clientHeight, this.object.matrix),
        this._panUp((2 * t * a) / n.clientHeight, this.object.matrix)
    } else
      this.object.isOrthographicCamera
        ? (this._panLeft(
            (e * (this.object.right - this.object.left)) / this.object.zoom / n.clientWidth,
            this.object.matrix,
          ),
          this._panUp(
            (t * (this.object.top - this.object.bottom)) / this.object.zoom / n.clientHeight,
            this.object.matrix,
          ))
        : (console.warn(
            'WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.',
          ),
          (this.enablePan = !1))
  }
  _dollyOut(e) {
    this.object.isPerspectiveCamera || this.object.isOrthographicCamera
      ? (this._scale /= e)
      : (console.warn(
          'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.',
        ),
        (this.enableZoom = !1))
  }
  _dollyIn(e) {
    this.object.isPerspectiveCamera || this.object.isOrthographicCamera
      ? (this._scale *= e)
      : (console.warn(
          'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.',
        ),
        (this.enableZoom = !1))
  }
  _updateZoomParameters(e, t) {
    if (!this.zoomToCursor) return
    this._performCursorZoom = !0
    const n = this.domElement.getBoundingClientRect(),
      o = e - n.left,
      a = t - n.top,
      i = n.width,
      r = n.height
    ;(this._mouse.x = (o / i) * 2 - 1),
      (this._mouse.y = -(a / r) * 2 + 1),
      this._dollyDirection
        .set(this._mouse.x, this._mouse.y, 1)
        .unproject(this.object)
        .sub(this.object.position)
        .normalize()
  }
  _clampDistance(e) {
    return Math.max(this.minDistance, Math.min(this.maxDistance, e))
  }
  //
  // event callbacks - update the object state
  //
  _handleMouseDownRotate(e) {
    this._rotateStart.set(e.clientX, e.clientY)
  }
  _handleMouseDownDolly(e) {
    this._updateZoomParameters(e.clientX, e.clientX), this._dollyStart.set(e.clientX, e.clientY)
  }
  _handleMouseDownPan(e) {
    this._panStart.set(e.clientX, e.clientY)
  }
  _handleMouseMoveRotate(e) {
    this._rotateEnd.set(e.clientX, e.clientY),
      this._rotateDelta
        .subVectors(this._rotateEnd, this._rotateStart)
        .multiplyScalar(this.rotateSpeed)
    const t = this.domElement
    this._rotateLeft((re * this._rotateDelta.x) / t.clientHeight),
      this._rotateUp((re * this._rotateDelta.y) / t.clientHeight),
      this._rotateStart.copy(this._rotateEnd),
      this.update()
  }
  _handleMouseMoveDolly(e) {
    this._dollyEnd.set(e.clientX, e.clientY),
      this._dollyDelta.subVectors(this._dollyEnd, this._dollyStart),
      this._dollyDelta.y > 0
        ? this._dollyOut(this._getZoomScale(this._dollyDelta.y))
        : this._dollyDelta.y < 0 && this._dollyIn(this._getZoomScale(this._dollyDelta.y)),
      this._dollyStart.copy(this._dollyEnd),
      this.update()
  }
  _handleMouseMovePan(e) {
    this._panEnd.set(e.clientX, e.clientY),
      this._panDelta.subVectors(this._panEnd, this._panStart).multiplyScalar(this.panSpeed),
      this._pan(this._panDelta.x, this._panDelta.y),
      this._panStart.copy(this._panEnd),
      this.update()
  }
  _handleMouseWheel(e) {
    this._updateZoomParameters(e.clientX, e.clientY),
      e.deltaY < 0
        ? this._dollyIn(this._getZoomScale(e.deltaY))
        : e.deltaY > 0 && this._dollyOut(this._getZoomScale(e.deltaY)),
      this.update()
  }
  _handleKeyDown(e) {
    let t = !1
    switch (e.code) {
      case this.keys.UP:
        e.ctrlKey || e.metaKey || e.shiftKey
          ? this.enableRotate &&
            this._rotateUp((re * this.rotateSpeed) / this.domElement.clientHeight)
          : this.enablePan && this._pan(0, this.keyPanSpeed),
          (t = !0)
        break
      case this.keys.BOTTOM:
        e.ctrlKey || e.metaKey || e.shiftKey
          ? this.enableRotate &&
            this._rotateUp((-re * this.rotateSpeed) / this.domElement.clientHeight)
          : this.enablePan && this._pan(0, -this.keyPanSpeed),
          (t = !0)
        break
      case this.keys.LEFT:
        e.ctrlKey || e.metaKey || e.shiftKey
          ? this.enableRotate &&
            this._rotateLeft((re * this.rotateSpeed) / this.domElement.clientHeight)
          : this.enablePan && this._pan(this.keyPanSpeed, 0),
          (t = !0)
        break
      case this.keys.RIGHT:
        e.ctrlKey || e.metaKey || e.shiftKey
          ? this.enableRotate &&
            this._rotateLeft((-re * this.rotateSpeed) / this.domElement.clientHeight)
          : this.enablePan && this._pan(-this.keyPanSpeed, 0),
          (t = !0)
        break
    }
    t && (e.preventDefault(), this.update())
  }
  _handleTouchStartRotate(e) {
    if (this._pointers.length === 1) this._rotateStart.set(e.pageX, e.pageY)
    else {
      const t = this._getSecondPointerPosition(e),
        n = 0.5 * (e.pageX + t.x),
        o = 0.5 * (e.pageY + t.y)
      this._rotateStart.set(n, o)
    }
  }
  _handleTouchStartPan(e) {
    if (this._pointers.length === 1) this._panStart.set(e.pageX, e.pageY)
    else {
      const t = this._getSecondPointerPosition(e),
        n = 0.5 * (e.pageX + t.x),
        o = 0.5 * (e.pageY + t.y)
      this._panStart.set(n, o)
    }
  }
  _handleTouchStartDolly(e) {
    const t = this._getSecondPointerPosition(e),
      n = e.pageX - t.x,
      o = e.pageY - t.y,
      a = Math.sqrt(n * n + o * o)
    this._dollyStart.set(0, a)
  }
  _handleTouchStartDollyPan(e) {
    this.enableZoom && this._handleTouchStartDolly(e),
      this.enablePan && this._handleTouchStartPan(e)
  }
  _handleTouchStartDollyRotate(e) {
    this.enableZoom && this._handleTouchStartDolly(e),
      this.enableRotate && this._handleTouchStartRotate(e)
  }
  _handleTouchMoveRotate(e) {
    if (this._pointers.length == 1) this._rotateEnd.set(e.pageX, e.pageY)
    else {
      const n = this._getSecondPointerPosition(e),
        o = 0.5 * (e.pageX + n.x),
        a = 0.5 * (e.pageY + n.y)
      this._rotateEnd.set(o, a)
    }
    this._rotateDelta
      .subVectors(this._rotateEnd, this._rotateStart)
      .multiplyScalar(this.rotateSpeed)
    const t = this.domElement
    this._rotateLeft((re * this._rotateDelta.x) / t.clientHeight),
      this._rotateUp((re * this._rotateDelta.y) / t.clientHeight),
      this._rotateStart.copy(this._rotateEnd)
  }
  _handleTouchMovePan(e) {
    if (this._pointers.length === 1) this._panEnd.set(e.pageX, e.pageY)
    else {
      const t = this._getSecondPointerPosition(e),
        n = 0.5 * (e.pageX + t.x),
        o = 0.5 * (e.pageY + t.y)
      this._panEnd.set(n, o)
    }
    this._panDelta.subVectors(this._panEnd, this._panStart).multiplyScalar(this.panSpeed),
      this._pan(this._panDelta.x, this._panDelta.y),
      this._panStart.copy(this._panEnd)
  }
  _handleTouchMoveDolly(e) {
    const t = this._getSecondPointerPosition(e),
      n = e.pageX - t.x,
      o = e.pageY - t.y,
      a = Math.sqrt(n * n + o * o)
    this._dollyEnd.set(0, a),
      this._dollyDelta.set(0, Math.pow(this._dollyEnd.y / this._dollyStart.y, this.zoomSpeed)),
      this._dollyOut(this._dollyDelta.y),
      this._dollyStart.copy(this._dollyEnd)
    const i = (e.pageX + t.x) * 0.5,
      r = (e.pageY + t.y) * 0.5
    this._updateZoomParameters(i, r)
  }
  _handleTouchMoveDollyPan(e) {
    this.enableZoom && this._handleTouchMoveDolly(e), this.enablePan && this._handleTouchMovePan(e)
  }
  _handleTouchMoveDollyRotate(e) {
    this.enableZoom && this._handleTouchMoveDolly(e),
      this.enableRotate && this._handleTouchMoveRotate(e)
  }
  // pointers
  _addPointer(e) {
    this._pointers.push(e.pointerId)
  }
  _removePointer(e) {
    delete this._pointerPositions[e.pointerId]
    for (let t = 0; t < this._pointers.length; t++)
      if (this._pointers[t] == e.pointerId) {
        this._pointers.splice(t, 1)
        return
      }
  }
  _isTrackingPointer(e) {
    for (let t = 0; t < this._pointers.length; t++) if (this._pointers[t] == e.pointerId) return !0
    return !1
  }
  _trackPointer(e) {
    let t = this._pointerPositions[e.pointerId]
    t === void 0 && ((t = new ee()), (this._pointerPositions[e.pointerId] = t)),
      t.set(e.pageX, e.pageY)
  }
  _getSecondPointerPosition(e) {
    const t = e.pointerId === this._pointers[0] ? this._pointers[1] : this._pointers[0]
    return this._pointerPositions[t]
  }
  //
  _customWheelEvent(e) {
    const t = e.deltaMode,
      n = {
        clientX: e.clientX,
        clientY: e.clientY,
        deltaY: e.deltaY,
      }
    switch (t) {
      case 1:
        n.deltaY *= 16
        break
      case 2:
        n.deltaY *= 100
        break
    }
    return e.ctrlKey && !this._controlActive && (n.deltaY *= 10), n
  }
}
function Sr(s) {
  this.enabled !== !1 &&
    (this._pointers.length === 0 &&
      (this.domElement.setPointerCapture(s.pointerId),
      this.domElement.addEventListener('pointermove', this._onPointerMove),
      this.domElement.addEventListener('pointerup', this._onPointerUp)),
    !this._isTrackingPointer(s) &&
      (this._addPointer(s),
      s.pointerType === 'touch' ? this._onTouchStart(s) : this._onMouseDown(s)))
}
function br(s) {
  this.enabled !== !1 && (s.pointerType === 'touch' ? this._onTouchMove(s) : this._onMouseMove(s))
}
function Cr(s) {
  switch ((this._removePointer(s), this._pointers.length)) {
    case 0:
      this.domElement.releasePointerCapture(s.pointerId),
        this.domElement.removeEventListener('pointermove', this._onPointerMove),
        this.domElement.removeEventListener('pointerup', this._onPointerUp),
        this.dispatchEvent(bo),
        (this.state = X.NONE)
      break
    case 1:
      const e = this._pointers[0],
        t = this._pointerPositions[e]
      this._onTouchStart({ pointerId: e, pageX: t.x, pageY: t.y })
      break
  }
}
function Mr(s) {
  let e
  switch (s.button) {
    case 0:
      e = this.mouseButtons.LEFT
      break
    case 1:
      e = this.mouseButtons.MIDDLE
      break
    case 2:
      e = this.mouseButtons.RIGHT
      break
    default:
      e = -1
  }
  switch (e) {
    case Se.DOLLY:
      if (this.enableZoom === !1) return
      this._handleMouseDownDolly(s), (this.state = X.DOLLY)
      break
    case Se.ROTATE:
      if (s.ctrlKey || s.metaKey || s.shiftKey) {
        if (this.enablePan === !1) return
        this._handleMouseDownPan(s), (this.state = X.PAN)
      } else {
        if (this.enableRotate === !1) return
        this._handleMouseDownRotate(s), (this.state = X.ROTATE)
      }
      break
    case Se.PAN:
      if (s.ctrlKey || s.metaKey || s.shiftKey) {
        if (this.enableRotate === !1) return
        this._handleMouseDownRotate(s), (this.state = X.ROTATE)
      } else {
        if (this.enablePan === !1) return
        this._handleMouseDownPan(s), (this.state = X.PAN)
      }
      break
    default:
      this.state = X.NONE
  }
  this.state !== X.NONE && this.dispatchEvent(Ss)
}
function xr(s) {
  switch (this.state) {
    case X.ROTATE:
      if (this.enableRotate === !1) return
      this._handleMouseMoveRotate(s)
      break
    case X.DOLLY:
      if (this.enableZoom === !1) return
      this._handleMouseMoveDolly(s)
      break
    case X.PAN:
      if (this.enablePan === !1) return
      this._handleMouseMovePan(s)
      break
  }
}
function Tr(s) {
  this.enabled === !1 ||
    this.enableZoom === !1 ||
    this.state !== X.NONE ||
    (s.preventDefault(),
    this.dispatchEvent(Ss),
    this._handleMouseWheel(this._customWheelEvent(s)),
    this.dispatchEvent(bo))
}
function kr(s) {
  this.enabled !== !1 && this._handleKeyDown(s)
}
function Er(s) {
  switch ((this._trackPointer(s), this._pointers.length)) {
    case 1:
      switch (this.touches.ONE) {
        case Me.ROTATE:
          if (this.enableRotate === !1) return
          this._handleTouchStartRotate(s), (this.state = X.TOUCH_ROTATE)
          break
        case Me.PAN:
          if (this.enablePan === !1) return
          this._handleTouchStartPan(s), (this.state = X.TOUCH_PAN)
          break
        default:
          this.state = X.NONE
      }
      break
    case 2:
      switch (this.touches.TWO) {
        case Me.DOLLY_PAN:
          if (this.enableZoom === !1 && this.enablePan === !1) return
          this._handleTouchStartDollyPan(s), (this.state = X.TOUCH_DOLLY_PAN)
          break
        case Me.DOLLY_ROTATE:
          if (this.enableZoom === !1 && this.enableRotate === !1) return
          this._handleTouchStartDollyRotate(s), (this.state = X.TOUCH_DOLLY_ROTATE)
          break
        default:
          this.state = X.NONE
      }
      break
    default:
      this.state = X.NONE
  }
  this.state !== X.NONE && this.dispatchEvent(Ss)
}
function Dr(s) {
  switch ((this._trackPointer(s), this.state)) {
    case X.TOUCH_ROTATE:
      if (this.enableRotate === !1) return
      this._handleTouchMoveRotate(s), this.update()
      break
    case X.TOUCH_PAN:
      if (this.enablePan === !1) return
      this._handleTouchMovePan(s), this.update()
      break
    case X.TOUCH_DOLLY_PAN:
      if (this.enableZoom === !1 && this.enablePan === !1) return
      this._handleTouchMoveDollyPan(s), this.update()
      break
    case X.TOUCH_DOLLY_ROTATE:
      if (this.enableZoom === !1 && this.enableRotate === !1) return
      this._handleTouchMoveDollyRotate(s), this.update()
      break
    default:
      this.state = X.NONE
  }
}
function _r(s) {
  this.enabled !== !1 && s.preventDefault()
}
function Ir(s) {
  s.key === 'Control' &&
    ((this._controlActive = !0),
    this.domElement
      .getRootNode()
      .addEventListener('keyup', this._interceptControlUp, { passive: !0, capture: !0 }))
}
function Pr(s) {
  s.key === 'Control' &&
    ((this._controlActive = !1),
    this.domElement
      .getRootNode()
      .removeEventListener('keyup', this._interceptControlUp, { passive: !0, capture: !0 }))
}
class Lr extends Co {
  constructor(e) {
    const t = e.camera
    super(t, e.renderer.domElement)
    const n = this
    ;(n.dampingFactor = 0.1), (n.rotateSpeed = 0.4), n.updateByOptions(e)
  }
  updateByOptions(e = {}) {
    const t = this
    e.enableDamping !== void 0 && (t.enableDamping = e.enableDamping),
      e.autoRotate !== void 0 && (t.autoRotate = e.autoRotate),
      e.enableZoom !== void 0 && (t.enableZoom = e.enableZoom),
      e.enableRotate !== void 0 && (t.enableRotate = e.enableRotate),
      e.enablePan !== void 0 && (t.enablePan = e.enablePan),
      e.minDistance !== void 0 && (t.minDistance = e.minDistance),
      e.maxDistance !== void 0 && (t.maxDistance = e.maxDistance),
      e.minPolarAngle !== void 0 && (t.minPolarAngle = e.minPolarAngle),
      e.maxPolarAngle !== void 0 && (t.maxPolarAngle = e.maxPolarAngle),
      e.fov !== void 0 && (t.object.fov = e.fov),
      e.near !== void 0 && (t.object.near = e.near),
      e.far !== void 0 && (t.object.far = e.far),
      e.position && t.object.position.fromArray(e.position),
      e.lookAt && t.target.fromArray(e.lookAt),
      e.lookUp && t.object.up.fromArray(e.lookUp),
      t.updateControlMode(e),
      pe && t._dollyOut?.(0.75),
      t.updateRotateAxis(),
      t.update()
  }
  /**
   * 更新控制模式
   * @param opts 选项
   */
  updateControlMode(e) {
    const t = this,
      n = e.useCustomControl === !0
    if (
      (console.log('[updateControlMode] 当前相机位置:', t.object.position.toArray()),
      console.log('[updateControlMode] 当前目标位置:', t.target.toArray()),
      n)
    ) {
      console.log('[updateControlMode] 启用第一人称模式'),
        (t.enabled = !1),
        (t.enableRotate = !1),
        (t.enablePan = !1),
        (t.enableZoom = !1),
        (t.enableDamping = !1),
        (t.autoRotate = !1),
        (t.mouseButtons = { LEFT: -1, MIDDLE: -1, RIGHT: -1 }),
        t.domElement &&
          (t._domElementKeyEvents && window.removeEventListener('keydown', t._onKeyDown),
          t.domElement.removeEventListener('contextmenu', t._onContextMenu),
          t.domElement.removeEventListener('pointerdown', t._onPointerDown),
          t.domElement.removeEventListener('pointercancel', t._onPointerCancel),
          t.domElement.removeEventListener('wheel', t._onMouseWheel),
          window.removeEventListener('pointermove', t._onPointerMove),
          window.removeEventListener('pointerup', t._onPointerUp))
      const o = this.getDistance()
      ;(t.minDistance = o), (t.maxDistance = o)
    } else
      console.log('[updateControlMode] 禁用第一人称模式'),
        (t.enabled = !0),
        (t.enableRotate = e.enableRotate !== void 0 ? e.enableRotate : !0),
        (t.enablePan = e.enablePan !== void 0 ? e.enablePan : !0),
        (t.enableZoom = e.enableZoom !== void 0 ? e.enableZoom : !0),
        (t.enableDamping = e.enableDamping !== void 0 ? e.enableDamping : !0),
        (t.autoRotate = e.autoRotate !== void 0 ? e.autoRotate : !1),
        (t.minDistance = e.minDistance !== void 0 ? e.minDistance : 0),
        (t.maxDistance = e.maxDistance !== void 0 ? e.maxDistance : 1 / 0),
        delete t.mouseButtons
    console.log('[updateControlMode] 更新后相机位置:', t.object.position.toArray()),
      console.log('[updateControlMode] 更新后目标位置:', t.target.toArray())
  }
  /**
   * 获取相机到目标的距离
   */
  getDistance() {
    return this._spherical?.radius || 1
  }
  /**
   * 更新旋转轴
   */
  updateRotateAxis() {
    this._quat?.setFromUnitVectors?.(this.object.up, new x(0, 1, 0)),
      (this._quatInverse = this._quat?.clone?.()?.invert?.())
  }
  /**
   * 覆盖OrbitControls的update方法，在第一人称模式下防止相机位置变化
   * @returns 是否进行了更新
   */
  update() {
    return this.enabled === !1
      ? (console.log('[CameraControls.update] 在第一人称模式下跳过update'), !1)
      : super.update()
  }
}
class Fr extends Co {
  constructor(e, t) {
    super(e, t),
      (this.screenSpacePanning = !1),
      (this.mouseButtons = { LEFT: Se.PAN, MIDDLE: Se.DOLLY, RIGHT: Se.ROTATE }),
      (this.touches = { ONE: Me.PAN, TWO: Me.DOLLY_ROTATE })
  }
}
const gi = new qn(),
  Bt = new x()
class Mo extends kn {
  constructor() {
    super(), (this.isLineSegmentsGeometry = !0), (this.type = 'LineSegmentsGeometry')
    const e = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0],
      t = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2],
      n = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5]
    this.setIndex(n),
      this.setAttribute('position', new ks(e, 3)),
      this.setAttribute('uv', new ks(t, 2))
  }
  applyMatrix4(e) {
    const t = this.attributes.instanceStart,
      n = this.attributes.instanceEnd
    return (
      t !== void 0 && (t.applyMatrix4(e), n.applyMatrix4(e), (t.needsUpdate = !0)),
      this.boundingBox !== null && this.computeBoundingBox(),
      this.boundingSphere !== null && this.computeBoundingSphere(),
      this
    )
  }
  setPositions(e) {
    let t
    e instanceof Float32Array ? (t = e) : Array.isArray(e) && (t = new Float32Array(e))
    const n = new En(t, 6, 1)
    return (
      this.setAttribute('instanceStart', new Xe(n, 3, 0)),
      this.setAttribute('instanceEnd', new Xe(n, 3, 3)),
      (this.instanceCount = this.attributes.instanceStart.count),
      this.computeBoundingBox(),
      this.computeBoundingSphere(),
      this
    )
  }
  setColors(e) {
    let t
    e instanceof Float32Array ? (t = e) : Array.isArray(e) && (t = new Float32Array(e))
    const n = new En(t, 6, 1)
    return (
      this.setAttribute('instanceColorStart', new Xe(n, 3, 0)),
      this.setAttribute('instanceColorEnd', new Xe(n, 3, 3)),
      this
    )
  }
  fromWireframeGeometry(e) {
    return this.setPositions(e.attributes.position.array), this
  }
  fromEdgesGeometry(e) {
    return this.setPositions(e.attributes.position.array), this
  }
  fromMesh(e) {
    return this.fromWireframeGeometry(new Oo(e.geometry)), this
  }
  fromLineSegments(e) {
    const t = e.geometry
    return this.setPositions(t.attributes.position.array), this
  }
  computeBoundingBox() {
    this.boundingBox === null && (this.boundingBox = new qn())
    const e = this.attributes.instanceStart,
      t = this.attributes.instanceEnd
    e !== void 0 &&
      t !== void 0 &&
      (this.boundingBox.setFromBufferAttribute(e),
      gi.setFromBufferAttribute(t),
      this.boundingBox.union(gi))
  }
  computeBoundingSphere() {
    this.boundingSphere === null && (this.boundingSphere = new ct()),
      this.boundingBox === null && this.computeBoundingBox()
    const e = this.attributes.instanceStart,
      t = this.attributes.instanceEnd
    if (e !== void 0 && t !== void 0) {
      const n = this.boundingSphere.center
      this.boundingBox.getCenter(n)
      let o = 0
      for (let a = 0, i = e.count; a < i; a++)
        Bt.fromBufferAttribute(e, a),
          (o = Math.max(o, n.distanceToSquared(Bt))),
          Bt.fromBufferAttribute(t, a),
          (o = Math.max(o, n.distanceToSquared(Bt)))
      ;(this.boundingSphere.radius = Math.sqrt(o)),
        isNaN(this.boundingSphere.radius) &&
          console.error(
            'THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.',
            this,
          )
    }
  }
  toJSON() {}
  applyMatrix(e) {
    return (
      console.warn('THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().'),
      this.applyMatrix4(e)
    )
  }
}
Qt.line = {
  worldUnits: { value: 1 },
  linewidth: { value: 1 },
  resolution: { value: new ee(1, 1) },
  dashOffset: { value: 0 },
  dashScale: { value: 1 },
  dashSize: { value: 1 },
  gapSize: { value: 1 },
  // todo FIX - maybe change to totalSize
}
Ot.line = {
  uniforms: xi.merge([Qt.common, Qt.fog, Qt.line]),
  vertexShader:
    /* glsl */
    `
		#include <common>
		#include <color_pars_vertex>
		#include <fog_pars_vertex>
		#include <logdepthbuf_pars_vertex>
		#include <clipping_planes_pars_vertex>

		uniform float linewidth;
		uniform vec2 resolution;

		attribute vec3 instanceStart;
		attribute vec3 instanceEnd;

		attribute vec3 instanceColorStart;
		attribute vec3 instanceColorEnd;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#ifdef USE_DASH

			uniform float dashScale;
			attribute float instanceDistanceStart;
			attribute float instanceDistanceEnd;
			varying float vLineDistance;

		#endif

		void trimSegment( const in vec4 start, inout vec4 end ) {

			// trim end segment so it terminates between the camera plane and the near plane

			// conservative estimate of the near plane
			float a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column
			float b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column
			float nearEstimate = - 0.5 * b / a;

			float alpha = ( nearEstimate - start.z ) / ( end.z - start.z );

			end.xyz = mix( start.xyz, end.xyz, alpha );

		}

		void main() {

			#ifdef USE_COLOR

				vColor.xyz = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;

			#endif

			#ifdef USE_DASH

				vLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;
				vUv = uv;

			#endif

			float aspect = resolution.x / resolution.y;

			// camera space
			vec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );
			vec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );

			#ifdef WORLD_UNITS

				worldStart = start.xyz;
				worldEnd = end.xyz;

			#else

				vUv = uv;

			#endif

			// special case for perspective projection, and segments that terminate either in, or behind, the camera plane
			// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space
			// but we need to perform ndc-space calculations in the shader, so we must address this issue directly
			// perhaps there is a more elegant solution -- WestLangley

			bool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column

			if ( perspective ) {

				if ( start.z < 0.0 && end.z >= 0.0 ) {

					trimSegment( start, end );

				} else if ( end.z < 0.0 && start.z >= 0.0 ) {

					trimSegment( end, start );

				}

			}

			// clip space
			vec4 clipStart = projectionMatrix * start;
			vec4 clipEnd = projectionMatrix * end;

			// ndc space
			vec3 ndcStart = clipStart.xyz / clipStart.w;
			vec3 ndcEnd = clipEnd.xyz / clipEnd.w;

			// direction
			vec2 dir = ndcEnd.xy - ndcStart.xy;

			// account for clip-space aspect ratio
			dir.x *= aspect;
			dir = normalize( dir );

			#ifdef WORLD_UNITS

				vec3 worldDir = normalize( end.xyz - start.xyz );
				vec3 tmpFwd = normalize( mix( start.xyz, end.xyz, 0.5 ) );
				vec3 worldUp = normalize( cross( worldDir, tmpFwd ) );
				vec3 worldFwd = cross( worldDir, worldUp );
				worldPos = position.y < 0.5 ? start: end;

				// height offset
				float hw = linewidth * 0.5;
				worldPos.xyz += position.x < 0.0 ? hw * worldUp : - hw * worldUp;

				// don't extend the line if we're rendering dashes because we
				// won't be rendering the endcaps
				#ifndef USE_DASH

					// cap extension
					worldPos.xyz += position.y < 0.5 ? - hw * worldDir : hw * worldDir;

					// add width to the box
					worldPos.xyz += worldFwd * hw;

					// endcaps
					if ( position.y > 1.0 || position.y < 0.0 ) {

						worldPos.xyz -= worldFwd * 2.0 * hw;

					}

				#endif

				// project the worldpos
				vec4 clip = projectionMatrix * worldPos;

				// shift the depth of the projected points so the line
				// segments overlap neatly
				vec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;
				clip.z = clipPose.z * clip.w;

			#else

				vec2 offset = vec2( dir.y, - dir.x );
				// undo aspect ratio adjustment
				dir.x /= aspect;
				offset.x /= aspect;

				// sign flip
				if ( position.x < 0.0 ) offset *= - 1.0;

				// endcaps
				if ( position.y < 0.0 ) {

					offset += - dir;

				} else if ( position.y > 1.0 ) {

					offset += dir;

				}

				// adjust for linewidth
				offset *= linewidth;

				// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...
				offset /= resolution.y;

				// select end
				vec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;

				// back to clip space
				offset *= clip.w;

				clip.xy += offset;

			#endif

			gl_Position = clip;

			vec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation

			#include <logdepthbuf_vertex>
			#include <clipping_planes_vertex>
			#include <fog_vertex>

		}
		`,
  fragmentShader:
    /* glsl */
    `
		uniform vec3 diffuse;
		uniform float opacity;
		uniform float linewidth;

		#ifdef USE_DASH

			uniform float dashOffset;
			uniform float dashSize;
			uniform float gapSize;

		#endif

		varying float vLineDistance;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#include <common>
		#include <color_pars_fragment>
		#include <fog_pars_fragment>
		#include <logdepthbuf_pars_fragment>
		#include <clipping_planes_pars_fragment>

		vec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {

			float mua;
			float mub;

			vec3 p13 = p1 - p3;
			vec3 p43 = p4 - p3;

			vec3 p21 = p2 - p1;

			float d1343 = dot( p13, p43 );
			float d4321 = dot( p43, p21 );
			float d1321 = dot( p13, p21 );
			float d4343 = dot( p43, p43 );
			float d2121 = dot( p21, p21 );

			float denom = d2121 * d4343 - d4321 * d4321;

			float numer = d1343 * d4321 - d1321 * d4343;

			mua = numer / denom;
			mua = clamp( mua, 0.0, 1.0 );
			mub = ( d1343 + d4321 * ( mua ) ) / d4343;
			mub = clamp( mub, 0.0, 1.0 );

			return vec2( mua, mub );

		}

		void main() {

			#include <clipping_planes_fragment>

			#ifdef USE_DASH

				if ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps

				if ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX

			#endif

			float alpha = opacity;

			#ifdef WORLD_UNITS

				// Find the closest points on the view ray and the line segment
				vec3 rayEnd = normalize( worldPos.xyz ) * 1e5;
				vec3 lineDir = worldEnd - worldStart;
				vec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );

				vec3 p1 = worldStart + lineDir * params.x;
				vec3 p2 = rayEnd * params.y;
				vec3 delta = p1 - p2;
				float len = length( delta );
				float norm = len / linewidth;

				#ifndef USE_DASH

					#ifdef USE_ALPHA_TO_COVERAGE

						float dnorm = fwidth( norm );
						alpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );

					#else

						if ( norm > 0.5 ) {

							discard;

						}

					#endif

				#endif

			#else

				#ifdef USE_ALPHA_TO_COVERAGE

					// artifacts appear on some hardware if a derivative is taken within a conditional
					float a = vUv.x;
					float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
					float len2 = a * a + b * b;
					float dlen = fwidth( len2 );

					if ( abs( vUv.y ) > 1.0 ) {

						alpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );

					}

				#else

					if ( abs( vUv.y ) > 1.0 ) {

						float a = vUv.x;
						float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
						float len2 = a * a + b * b;

						if ( len2 > 1.0 ) discard;

					}

				#endif

			#endif

			vec4 diffuseColor = vec4( diffuse, alpha );

			#include <logdepthbuf_fragment>
			#include <color_fragment>

			gl_FragColor = vec4( diffuseColor.rgb, alpha );

			#include <tonemapping_fragment>
			#include <colorspace_fragment>
			#include <fog_fragment>
			#include <premultiplied_alpha_fragment>

		}
		`,
}
class Je extends Zn {
  constructor(e) {
    super({
      type: 'LineMaterial',
      uniforms: xi.clone(Ot.line.uniforms),
      vertexShader: Ot.line.vertexShader,
      fragmentShader: Ot.line.fragmentShader,
      clipping: !0,
      // required for clipping support
    }),
      (this.isLineMaterial = !0),
      this.setValues(e)
  }
  get color() {
    return this.uniforms.diffuse.value
  }
  set color(e) {
    this.uniforms.diffuse.value = e
  }
  get worldUnits() {
    return 'WORLD_UNITS' in this.defines
  }
  set worldUnits(e) {
    e === !0 ? (this.defines.WORLD_UNITS = '') : delete this.defines.WORLD_UNITS
  }
  get linewidth() {
    return this.uniforms.linewidth.value
  }
  set linewidth(e) {
    this.uniforms.linewidth && (this.uniforms.linewidth.value = e)
  }
  get dashed() {
    return 'USE_DASH' in this.defines
  }
  set dashed(e) {
    ;(e === !0) !== this.dashed && (this.needsUpdate = !0),
      e === !0 ? (this.defines.USE_DASH = '') : delete this.defines.USE_DASH
  }
  get dashScale() {
    return this.uniforms.dashScale.value
  }
  set dashScale(e) {
    this.uniforms.dashScale.value = e
  }
  get dashSize() {
    return this.uniforms.dashSize.value
  }
  set dashSize(e) {
    this.uniforms.dashSize.value = e
  }
  get dashOffset() {
    return this.uniforms.dashOffset.value
  }
  set dashOffset(e) {
    this.uniforms.dashOffset.value = e
  }
  get gapSize() {
    return this.uniforms.gapSize.value
  }
  set gapSize(e) {
    this.uniforms.gapSize.value = e
  }
  get opacity() {
    return this.uniforms.opacity.value
  }
  set opacity(e) {
    this.uniforms && (this.uniforms.opacity.value = e)
  }
  get resolution() {
    return this.uniforms.resolution.value
  }
  set resolution(e) {
    this.uniforms.resolution.value.copy(e)
  }
  get alphaToCoverage() {
    return 'USE_ALPHA_TO_COVERAGE' in this.defines
  }
  set alphaToCoverage(e) {
    this.defines &&
      ((e === !0) !== this.alphaToCoverage && (this.needsUpdate = !0),
      e === !0
        ? (this.defines.USE_ALPHA_TO_COVERAGE = '')
        : delete this.defines.USE_ALPHA_TO_COVERAGE)
  }
}
const Mn = new Ce(),
  mi = new x(),
  yi = new x(),
  j = new Ce(),
  q = new Ce(),
  fe = new Ce(),
  xn = new x(),
  Tn = new Te(),
  J = new Qo(),
  Ai = new x(),
  Ut = new qn(),
  zt = new ct(),
  ge = new Ce()
let Ae, ze
function wi(s, e, t) {
  return (
    ge.set(0, 0, -e, 1).applyMatrix4(s.projectionMatrix),
    ge.multiplyScalar(1 / ge.w),
    (ge.x = ze / t.width),
    (ge.y = ze / t.height),
    ge.applyMatrix4(s.projectionMatrixInverse),
    ge.multiplyScalar(1 / ge.w),
    Math.abs(Math.max(ge.x, ge.y))
  )
}
function Rr(s, e) {
  const t = s.matrixWorld,
    n = s.geometry,
    o = n.attributes.instanceStart,
    a = n.attributes.instanceEnd,
    i = Math.min(n.instanceCount, o.count)
  for (let r = 0, c = i; r < c; r++) {
    J.start.fromBufferAttribute(o, r), J.end.fromBufferAttribute(a, r), J.applyMatrix4(t)
    const u = new x(),
      d = new x()
    Ae.distanceSqToSegment(J.start, J.end, d, u),
      d.distanceTo(u) < ze * 0.5 &&
        e.push({
          point: d,
          pointOnLine: u,
          distance: Ae.origin.distanceTo(d),
          object: s,
          face: null,
          faceIndex: r,
          uv: null,
          uv1: null,
        })
  }
}
function Br(s, e, t) {
  const n = e.projectionMatrix,
    a = s.material.resolution,
    i = s.matrixWorld,
    r = s.geometry,
    c = r.attributes.instanceStart,
    u = r.attributes.instanceEnd,
    d = Math.min(r.instanceCount, c.count),
    l = -e.near
  Ae.at(1, fe),
    (fe.w = 1),
    fe.applyMatrix4(e.matrixWorldInverse),
    fe.applyMatrix4(n),
    fe.multiplyScalar(1 / fe.w),
    (fe.x *= a.x / 2),
    (fe.y *= a.y / 2),
    (fe.z = 0),
    xn.copy(fe),
    Tn.multiplyMatrices(e.matrixWorldInverse, i)
  for (let h = 0, m = d; h < m; h++) {
    if (
      (j.fromBufferAttribute(c, h),
      q.fromBufferAttribute(u, h),
      (j.w = 1),
      (q.w = 1),
      j.applyMatrix4(Tn),
      q.applyMatrix4(Tn),
      j.z > l && q.z > l)
    )
      continue
    if (j.z > l) {
      const M = j.z - q.z,
        S = (j.z - l) / M
      j.lerp(q, S)
    } else if (q.z > l) {
      const M = q.z - j.z,
        S = (q.z - l) / M
      q.lerp(j, S)
    }
    j.applyMatrix4(n),
      q.applyMatrix4(n),
      j.multiplyScalar(1 / j.w),
      q.multiplyScalar(1 / q.w),
      (j.x *= a.x / 2),
      (j.y *= a.y / 2),
      (q.x *= a.x / 2),
      (q.y *= a.y / 2),
      J.start.copy(j),
      (J.start.z = 0),
      J.end.copy(q),
      (J.end.z = 0)
    const A = J.closestPointToPointParameter(xn, !0)
    J.at(A, Ai)
    const g = xe.lerp(j.z, q.z, A),
      y = g >= -1 && g <= 1,
      E = xn.distanceTo(Ai) < ze * 0.5
    if (y && E) {
      J.start.fromBufferAttribute(c, h),
        J.end.fromBufferAttribute(u, h),
        J.start.applyMatrix4(i),
        J.end.applyMatrix4(i)
      const M = new x(),
        S = new x()
      Ae.distanceSqToSegment(J.start, J.end, S, M),
        t.push({
          point: S,
          pointOnLine: M,
          distance: Ae.origin.distanceTo(S),
          object: s,
          face: null,
          faceIndex: h,
          uv: null,
          uv1: null,
        })
    }
  }
}
class Ur extends se {
  constructor(e = new Mo(), t = new Je({ color: Math.random() * 16777215 })) {
    super(e, t), (this.isLineSegments2 = !0), (this.type = 'LineSegments2')
  }
  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...
  computeLineDistances() {
    const e = this.geometry,
      t = e.attributes.instanceStart,
      n = e.attributes.instanceEnd,
      o = new Float32Array(2 * t.count)
    for (let i = 0, r = 0, c = t.count; i < c; i++, r += 2)
      mi.fromBufferAttribute(t, i),
        yi.fromBufferAttribute(n, i),
        (o[r] = r === 0 ? 0 : o[r - 1]),
        (o[r + 1] = o[r] + mi.distanceTo(yi))
    const a = new En(o, 2, 1)
    return (
      e.setAttribute('instanceDistanceStart', new Xe(a, 1, 0)),
      e.setAttribute('instanceDistanceEnd', new Xe(a, 1, 1)),
      this
    )
  }
  raycast(e, t) {
    const n = this.material.worldUnits,
      o = e.camera
    o === null &&
      !n &&
      console.error(
        'LineSegments2: "Raycaster.camera" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.',
      )
    const a = (e.params.Line2 !== void 0 && e.params.Line2.threshold) || 0
    Ae = e.ray
    const i = this.matrixWorld,
      r = this.geometry,
      c = this.material
    ;(ze = c.linewidth + a),
      r.boundingSphere === null && r.computeBoundingSphere(),
      zt.copy(r.boundingSphere).applyMatrix4(i)
    let u
    if (n) u = ze * 0.5
    else {
      const l = Math.max(o.near, zt.distanceToPoint(Ae.origin))
      u = wi(o, l, c.resolution)
    }
    if (((zt.radius += u), Ae.intersectsSphere(zt) === !1)) return
    r.boundingBox === null && r.computeBoundingBox(), Ut.copy(r.boundingBox).applyMatrix4(i)
    let d
    if (n) d = ze * 0.5
    else {
      const l = Math.max(o.near, Ut.distanceToPoint(Ae.origin))
      d = wi(o, l, c.resolution)
    }
    Ut.expandByScalar(d), Ae.intersectsBox(Ut) !== !1 && (n ? Rr(this, t) : Br(this, o, t))
  }
  onBeforeRender(e) {
    const t = this.material.uniforms
    t &&
      t.resolution &&
      (e.getViewport(Mn), this.material.uniforms.resolution.value.set(Mn.z, Mn.w))
  }
}
class At extends Mo {
  constructor() {
    super(), (this.isLineGeometry = !0), (this.type = 'LineGeometry')
  }
  setPositions(e) {
    const t = e.length - 3,
      n = new Float32Array(2 * t)
    for (let o = 0; o < t; o += 3)
      (n[2 * o] = e[o]),
        (n[2 * o + 1] = e[o + 1]),
        (n[2 * o + 2] = e[o + 2]),
        (n[2 * o + 3] = e[o + 3]),
        (n[2 * o + 4] = e[o + 4]),
        (n[2 * o + 5] = e[o + 5])
    return super.setPositions(n), this
  }
  setColors(e) {
    const t = e.length - 3,
      n = new Float32Array(2 * t)
    for (let o = 0; o < t; o += 3)
      (n[2 * o] = e[o]),
        (n[2 * o + 1] = e[o + 1]),
        (n[2 * o + 2] = e[o + 2]),
        (n[2 * o + 3] = e[o + 3]),
        (n[2 * o + 4] = e[o + 4]),
        (n[2 * o + 5] = e[o + 5])
    return super.setColors(n), this
  }
  setFromPoints(e) {
    const t = e.length - 1,
      n = new Float32Array(6 * t)
    for (let o = 0; o < t; o++)
      (n[6 * o] = e[o].x),
        (n[6 * o + 1] = e[o].y),
        (n[6 * o + 2] = e[o].z || 0),
        (n[6 * o + 3] = e[o + 1].x),
        (n[6 * o + 4] = e[o + 1].y),
        (n[6 * o + 5] = e[o + 1].z || 0)
    return super.setPositions(n), this
  }
  fromLine(e) {
    const t = e.geometry
    return this.setPositions(t.attributes.position.array), this
  }
}
class Oe extends Ur {
  constructor(e = new At(), t = new Je({ color: Math.random() * 16777215 })) {
    super(e, t), (this.isLine2 = !0), (this.type = 'Line2')
  }
}
const vi = new x(),
  zr = new Xt(),
  Si = new x()
class Or extends jn {
  constructor(e = document.createElement('div')) {
    super(),
      (this.isCSS3DObject = !0),
      (this.element = e),
      (this.element.style.position = 'absolute'),
      (this.element.style.pointerEvents = 'auto'),
      (this.element.style.userSelect = 'none'),
      this.element.setAttribute('draggable', !1),
      this.addEventListener('removed', function () {
        this.traverse(function (t) {
          t.element instanceof t.element.ownerDocument.defaultView.Element &&
            t.element.parentNode !== null &&
            t.element.remove()
        })
      })
  }
  copy(e, t) {
    return super.copy(e, t), (this.element = e.element.cloneNode(!0)), this
  }
}
class oe extends Or {
  constructor(e) {
    super(e), (this.isCSS3DSprite = !0), (this.rotation2D = 0)
  }
  copy(e, t) {
    return super.copy(e, t), (this.rotation2D = e.rotation2D), this
  }
}
const me = new Te(),
  Qr = new Te()
class Wr {
  constructor(e = {}) {
    const t = this
    let n, o, a, i
    const r = {
        camera: { style: '' },
        objects: /* @__PURE__ */ new WeakMap(),
      },
      c = e.element !== void 0 ? e.element : document.createElement('div')
    ;(c.style.overflow = 'hidden'), (this.domElement = c)
    const u = document.createElement('div')
    ;(u.style.transformOrigin = '0 0'), (u.style.pointerEvents = 'none'), c.appendChild(u)
    const d = document.createElement('div')
    ;(d.style.transformStyle = 'preserve-3d'),
      u.appendChild(d),
      (this.getSize = function () {
        return {
          width: n,
          height: o,
        }
      }),
      (this.render = function (g, y) {
        const E = y.projectionMatrix.elements[5] * i
        y.view && y.view.enabled
          ? ((u.style.transform = `translate( ${-y.view.offsetX * (n / y.view.width)}px, ${-y.view.offsetY * (o / y.view.height)}px )`),
            (u.style.transform += `scale( ${y.view.fullWidth / y.view.width}, ${y.view.fullHeight / y.view.height} )`))
          : (u.style.transform = ''),
          g.matrixWorldAutoUpdate === !0 && g.updateMatrixWorld(),
          y.parent === null && y.matrixWorldAutoUpdate === !0 && y.updateMatrixWorld()
        let M, S
        y.isOrthographicCamera && ((M = -(y.right + y.left) / 2), (S = (y.top + y.bottom) / 2))
        const P = y.view && y.view.enabled ? y.view.height / y.view.fullHeight : 1,
          T = y.isOrthographicCamera
            ? `scale( ${P} )scale(` +
              E +
              ')translate(' +
              l(M) +
              'px,' +
              l(S) +
              'px)' +
              h(y.matrixWorldInverse)
            : `scale( ${P} )translateZ(` + E + 'px)' + h(y.matrixWorldInverse),
          F =
            (y.isPerspectiveCamera ? 'perspective(' + E + 'px) ' : '') +
            T +
            'translate(' +
            a +
            'px,' +
            i +
            'px)'
        r.camera.style !== F && ((d.style.transform = F), (r.camera.style = F)), A(g, g, y)
      }),
      (this.setSize = function (g, y) {
        ;(n = g),
          (o = y),
          (a = n / 2),
          (i = o / 2),
          (c.style.width = g + 'px'),
          (c.style.height = y + 'px'),
          (u.style.width = g + 'px'),
          (u.style.height = y + 'px'),
          (d.style.width = g + 'px'),
          (d.style.height = y + 'px')
      })
    function l(g) {
      return Math.abs(g) < 1e-10 ? 0 : g
    }
    function h(g) {
      const y = g.elements
      return (
        'matrix3d(' +
        l(y[0]) +
        ',' +
        l(-y[1]) +
        ',' +
        l(y[2]) +
        ',' +
        l(y[3]) +
        ',' +
        l(y[4]) +
        ',' +
        l(-y[5]) +
        ',' +
        l(y[6]) +
        ',' +
        l(y[7]) +
        ',' +
        l(y[8]) +
        ',' +
        l(-y[9]) +
        ',' +
        l(y[10]) +
        ',' +
        l(y[11]) +
        ',' +
        l(y[12]) +
        ',' +
        l(-y[13]) +
        ',' +
        l(y[14]) +
        ',' +
        l(y[15]) +
        ')'
      )
    }
    function m(g) {
      const y = g.elements
      return (
        'translate(-50%,-50%)' +
        ('matrix3d(' +
          l(y[0]) +
          ',' +
          l(y[1]) +
          ',' +
          l(y[2]) +
          ',' +
          l(y[3]) +
          ',' +
          l(-y[4]) +
          ',' +
          l(-y[5]) +
          ',' +
          l(-y[6]) +
          ',' +
          l(-y[7]) +
          ',' +
          l(y[8]) +
          ',' +
          l(y[9]) +
          ',' +
          l(y[10]) +
          ',' +
          l(y[11]) +
          ',' +
          l(y[12]) +
          ',' +
          l(y[13]) +
          ',' +
          l(y[14]) +
          ',' +
          l(y[15]) +
          ')')
      )
    }
    function f(g) {
      g.isCSS3DObject && (g.element.style.display = 'none')
      for (let y = 0, E = g.children.length; y < E; y++) f(g.children[y])
    }
    function A(g, y, E, M) {
      if (g.visible === !1) {
        f(g)
        return
      }
      if (g.isCSS3DObject) {
        const S = g.layers.test(E.layers) === !0,
          P = g.element
        if (((P.style.display = S === !0 ? '' : 'none'), S === !0)) {
          g.onBeforeRender(t, y, E)
          let T
          g.isCSS3DSprite
            ? (me.copy(E.matrixWorldInverse),
              me.transpose(),
              g.rotation2D !== 0 && me.multiply(Qr.makeRotationZ(g.rotation2D)),
              g.matrixWorld.decompose(vi, zr, Si),
              me.setPosition(vi),
              me.scale(Si),
              (me.elements[3] = 0),
              (me.elements[7] = 0),
              (me.elements[11] = 0),
              (me.elements[15] = 1),
              (T = m(me)))
            : (T = m(g.matrixWorld))
          const D = r.objects.get(g)
          if (D === void 0 || D.style !== T) {
            P.style.transform = T
            const F = { style: T }
            r.objects.set(g, F)
          }
          P.parentNode !== d && d.appendChild(P), g.onAfterRender(t, y, E)
        }
      }
      for (let S = 0, P = g.children.length; S < P; S++) A(g.children[S], y, E)
    }
  }
}
class Gn extends Oe {
  constructor(e) {
    super(), (this.isMark = !0), (this.disposed = !1), (this.events = e)
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return
    const n = this,
      o = document.querySelectorAll('.mark-wrap-line.main-warp').length + 1,
      a = {
        type: 'MarkDistanceLine',
        name: t || 'line' + Date.now(),
        startPoint: e.toArray(),
        endPoint: e.toArray(),
        lineColor: '#eeee00',
        lineWidth: 3,
        mainTagColor: '#c4c4c4',
        mainTagBackground: '#2E2E30',
        mainTagOpacity: 0.8,
        mainTagVisible: !0,
        distanceTagColor: '#000000',
        distanceTagBackground: '#e0ffff',
        distanceTagOpacity: 0.9,
        distanceTagVisible: !0,
        title: '标记距离' + o,
      },
      i = new At()
    i.setPositions([...a.startPoint, ...a.endPoint])
    const r = new Je({ color: a.lineColor, linewidth: a.lineWidth })
    r.resolution.set(innerWidth, innerHeight), n.copy(new Oe(i, r))
    const c = new Re(0.05, 32),
      u = new ke({ color: 16777215, side: be })
    ;(u.transparent = !0), (u.opacity = 0.6)
    const d = new se(c, u)
    d.position.copy(e), (d.isMark = !0)
    const l = new se(c, u)
    l.position.copy(e), (l.isMark = !0)
    const h = document.createElement('div')
    ;(h.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`),
      h.classList.add('mark-wrap-line', `${a.name}`, 'main-warp'),
      (h.style.position = 'absolute'),
      (h.style.borderRadius = '4px'),
      (h.style.cursor = 'pointer'),
      (h.onclick = () => {
        if (n.events.fire(I).markMode) return
        const g = parent?.onActiveMark
        g?.(n.getMarkData(!0)), n.events.fire(te)
      }),
      (h.oncontextmenu = (g) => g.preventDefault())
    const m = new oe(h)
    m.position.copy(e),
      (m.element.style.pointerEvents = 'none'),
      m.scale.set(0.01, 0.01, 0.01),
      (m.visible = a.mainTagVisible)
    const f = document.createElement('div')
    ;(f.innerHTML = `<span class="${t}-distance-tag ${t}-distance-tag0" style="color:${a.distanceTagColor};background:${a.distanceTagBackground};opacity:${a.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`),
      f.classList.add('mark-wrap-line', `${t}`, 'distance-warp'),
      (f.style.position = 'absolute'),
      (f.style.borderRadius = '4px'),
      (f.style.pointerEvents = 'none')
    const A = new oe(f)
    A.position.set(e.x, e.y, e.z),
      (A.element.style.pointerEvents = 'none'),
      A.scale.set(8e-3, 8e-3, 8e-3),
      (A.visible = !1),
      n.add(d, l, m, A),
      (n.data = a),
      (n.circleStart = d),
      (n.circleEnd = l),
      (n.css3dTag = A),
      (n.css3dMainTag = m),
      n.events.fire(De, n)
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0) {
    if (this.disposed) return
    const n = this
    if (e?.endPoint) {
      t && (n.data.endPoint = [...e.endPoint])
      const o = new x().fromArray(n.data.startPoint),
        a = new x().fromArray(e.endPoint)
      n.geometry.setPositions([...n.data.startPoint, ...e.endPoint])
      const i = new x((o.x + a.x) / 2, (o.y + a.y) / 2, (o.z + a.z) / 2)
      n.css3dTag.position.set(i.x, i.y, i.z)
      const r = a.clone().sub(o).normalize()
      n.circleStart.lookAt(n.circleStart.position.clone().add(r)),
        n.circleEnd.lookAt(n.circleEnd.position.clone().add(r))
      const c = o.distanceTo(a)
      n.css3dTag.visible = c > 0.5
      const u = (c * n.events.fire(I).meterScale).toFixed(2) + ' m'
      n.css3dTag.element.childNodes[0].innerText = u
    }
    e?.lineColor && (t && (n.data.lineColor = e.lineColor), n.material.color.set(e.lineColor)),
      e?.lineWidth && (t && (n.data.lineWidth = e.lineWidth), (n.material.linewidth = e.lineWidth)),
      e?.mainTagColor &&
        (t && (n.data.mainTagColor = e.mainTagColor),
        (document.querySelector(`.${n.data.name}-main-tag`).style.color = e.mainTagColor)),
      e?.mainTagBackground &&
        (t && (n.data.mainTagBackground = e.mainTagBackground),
        (document.querySelector(`.${n.data.name}-main-tag`).style.background =
          e.mainTagBackground)),
      e?.mainTagOpacity &&
        (t && (n.data.mainTagOpacity = e.mainTagOpacity),
        (document.querySelector(`.${n.data.name}-main-tag`).style.opacity =
          e.mainTagOpacity.toString())),
      e?.mainTagVisible !== void 0 &&
        (t && (n.data.mainTagVisible = e.mainTagVisible),
        (n.css3dMainTag.visible = e.mainTagVisible)),
      e?.distanceTagColor &&
        (t && (n.data.distanceTagColor = e.distanceTagColor),
        (document.querySelector(`.${n.data.name}-distance-tag0`).style.color = e.distanceTagColor)),
      e?.distanceTagBackground &&
        (t && (n.data.distanceTagBackground = e.distanceTagBackground),
        (document.querySelector(`.${n.data.name}-distance-tag0`).style.background =
          e.distanceTagBackground)),
      e?.distanceTagOpacity &&
        (t && (n.data.distanceTagOpacity = e.distanceTagOpacity),
        (document.querySelector(`.${n.data.name}-distance-tag0`).style.opacity =
          e.distanceTagOpacity.toString())),
      e?.distanceTagVisible !== void 0 &&
        (t && (n.data.distanceTagVisible = e.distanceTagVisible),
        (n.css3dTag.visible = e.distanceTagVisible)),
      e?.title !== void 0 &&
        (t && (n.data.title = e.title),
        (this.css3dMainTag.element.querySelector(`.${n.data.name}-main-tag`).innerText = e.title)),
      e?.note !== void 0 && t && (n.data.note = e.note),
      n.events.fire(W)
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this,
      n = new x().fromArray(t.data.startPoint),
      o = new x().fromArray(t.data.endPoint),
      a = (n.distanceTo(o) * e).toFixed(2) + ' m'
    t.css3dTag.element.childNodes[0].innerText = a
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return
    const t = this
    t.data.endPoint = [...e.toArray()]
    const n = new x().fromArray(t.data.startPoint),
      o = new x().fromArray(t.data.endPoint),
      a = new x((n.x + o.x) / 2, (n.y + o.y) / 2, (n.z + o.z) / 2)
    t.geometry.setPositions([...t.data.startPoint, ...t.data.endPoint]),
      t.css3dTag.position.set(a.x, a.y, a.z)
    const i = o.clone().sub(n).normalize()
    t.circleStart.lookAt(t.circleStart.position.clone().add(i)),
      t.circleEnd.lookAt(t.circleEnd.position.clone().add(i)),
      t.circleEnd.position.copy(o)
    const r = n.distanceTo(o)
    t.css3dTag.visible = !0
    const c = (r * t.events.fire(I).meterScale).toFixed(2) + ' m'
    ;(t.css3dTag.element.childNodes[0].innerText = c), t.events.fire(_e)
    const u = parent?.onActiveMark,
      d = t.getMarkData(!0)
    ;(d.isNew = !0), (d.meterScale = t.events.fire(I).meterScale), u?.(d)
  }
  /**
   * 根据数据直接绘制
   */
  draw(e) {
    if (this.disposed) return
    const t = this,
      n = {
        type: 'MarkDistanceLine',
        name: e.name || 'line' + Date.now(),
        startPoint: [...e.startPoint],
        endPoint: [...e.endPoint],
        lineColor: e.lineColor || '#eeee00',
        lineWidth: e.lineWidth || 3,
        mainTagColor: e.mainTagColor || '#c4c4c4',
        mainTagBackground: e.mainTagBackground || '#2E2E30',
        mainTagOpacity: e.mainTagOpacity || 0.8,
        mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
        distanceTagColor: e.distanceTagColor || '#000000',
        distanceTagBackground: e.distanceTagBackground || '#e0ffff',
        distanceTagOpacity: e.distanceTagOpacity || 0.9,
        distanceTagVisible: e.distanceTagVisible === void 0 ? !0 : e.distanceTagVisible,
        title: e.title || '标记距离',
      },
      o = new x().fromArray(n.startPoint),
      a = new x().fromArray(n.endPoint),
      i = new x((o.x + a.x) / 2, (o.y + a.y) / 2, (o.z + a.z) / 2),
      r = new At()
    r.setPositions([...n.startPoint, ...n.endPoint])
    const c = new Je({ color: n.lineColor, linewidth: n.lineWidth })
    c.resolution.set(innerWidth, innerHeight), t.copy(new Oe(r, c))
    const u = new Re(0.05, 32),
      d = new ke({ color: 16777215, side: be })
    ;(d.transparent = !0), (d.opacity = 0.6)
    const l = new se(u, d)
    l.position.copy(o), (l.isMark = !0)
    const h = new se(u, d)
    h.position.copy(a), (h.isMark = !0)
    const m = a.clone().sub(o).normalize()
    l.lookAt(l.position.clone().add(m)), h.lookAt(h.position.clone().add(m))
    const f = n.name,
      A = document.createElement('div')
    ;(A.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${f}-main-tag" style="color:${n.mainTagColor};background:${n.mainTagBackground};opacity:${n.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${n.title}</span>
                             </div>`),
      A.classList.add('mark-wrap-line', `${n.name}`, 'main-warp'),
      (A.style.position = 'absolute'),
      (A.style.borderRadius = '4px'),
      (A.style.cursor = 'pointer'),
      (A.onclick = () => {
        if (t.events.fire(I).markMode) return
        const P = parent?.onActiveMark
        P?.(t.getMarkData(!0)), t.events.fire(te)
      }),
      (A.oncontextmenu = (P) => P.preventDefault())
    const g = new oe(A)
    g.position.copy(o),
      (g.element.style.pointerEvents = 'none'),
      g.scale.set(0.01, 0.01, 0.01),
      (g.visible = n.mainTagVisible)
    const E = (o.distanceTo(a) * t.events.fire(I).meterScale).toFixed(2) + ' m',
      M = document.createElement('div')
    ;(M.innerHTML = `<span class="${f}-distance-tag ${f}-distance-tag0" style="color:${n.distanceTagColor};background:${n.distanceTagBackground};opacity:${n.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${E}</span>`),
      M.classList.add('mark-wrap-line', `${f}`, 'distance-warp'),
      (M.style.position = 'absolute'),
      (M.style.borderRadius = '4px'),
      (M.style.pointerEvents = 'none')
    const S = new oe(M)
    S.position.copy(i),
      (S.element.style.pointerEvents = 'none'),
      S.scale.set(8e-3, 8e-3, 8e-3),
      (S.visible = n.distanceTagVisible),
      t.add(l, h, g, S),
      (t.data = n),
      (t.circleStart = l),
      (t.circleEnd = h),
      (t.css3dTag = S),
      (t.css3dMainTag = g),
      t.events.fire(De, t)
  }
  getMarkData(e = !1) {
    const t = { ...this.data }
    return (
      e
        ? (delete t.startPoint, delete t.endPoint)
        : ((t.startPoint = [...t.startPoint]), (t.endPoint = [...t.endPoint])),
      t
    )
  }
  dispose() {
    if (this.disposed) return
    const e = this
    ;(e.disposed = !0),
      e.events.fire(Ie, e),
      e.events.fire(z).remove(e),
      e.events.fire(tt, e),
      document.querySelectorAll(`.${e.data.name}`).forEach((n) => n.parentElement?.removeChild(n)),
      (e.events = null),
      (e.data = null),
      (e.circleStart = null),
      (e.circleEnd = null),
      (e.css3dTag = null),
      (e.css3dMainTag = null)
  }
}
class Xn extends Oe {
  constructor(e) {
    super(),
      (this.isMark = !0),
      (this.disposed = !1),
      (this.css3dTags = []),
      (this.group = new sn()),
      this.add(this.group),
      (this.events = e)
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return
    const n = document.querySelectorAll('.mark-wrap-lines.main-warp').length + 1,
      o = {
        type: 'MarkMultiLines',
        name: t || 'lines' + Date.now(),
        points: [...e.toArray(), ...e.toArray()],
        lineColor: '#eeee00',
        lineWidth: 3,
        mainTagColor: '#c4c4c4',
        mainTagBackground: '#2E2E30',
        mainTagOpacity: 0.8,
        mainTagVisible: !0,
        distanceTagColor: '#000000',
        distanceTagBackground: '#e0ffff',
        distanceTagOpacity: 0.9,
        distanceTagVisible: !0,
        title: '标记线' + n,
        note: '',
      }
    this.draw(o)
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0, n, o = !1) {
    if (this.disposed) return
    const a = this
    if (n)
      if (o) {
        const i = a.data.points.length,
          r = a.css3dTags.length - 1,
          c = new x().fromArray(a.data.points.slice(i - 6, i - 3)),
          u = n,
          d = new x((c.x + u.x) / 2, (c.y + u.y) / 2, (c.z + u.z) / 2),
          h = (c.distanceTo(u) * a.events.fire(I).meterScale).toFixed(2) + ' m'
        ;(a.css3dTags[r].element.childNodes[0].innerText = h),
          a.css3dTags[r].position.set(d.x, d.y, d.z),
          (a.css3dTags[r].visible = !0),
          a.data.points.pop(),
          a.data.points.pop(),
          a.data.points.pop(),
          (a.data.points = [...a.data.points, ...n.toArray(), ...n.toArray()]),
          (a.css3dTags[a.css3dTags.length - 1].visible = !0),
          this.draw(this.data)
      } else {
        const i = a.data.points.length
        ;(a.data.points[i - 3] = n.x), (a.data.points[i - 2] = n.y), (a.data.points[i - 1] = n.z)
        const r = a.css3dTags.length - 1,
          c = new x().fromArray(a.data.points.slice(i - 6, i - 3)),
          u = new x().fromArray(a.data.points.slice(i - 3)),
          d = new x((c.x + u.x) / 2, (c.y + u.y) / 2, (c.z + u.z) / 2),
          l = c.distanceTo(u),
          h = (l * a.events.fire(I).meterScale).toFixed(2) + ' m'
        ;(a.css3dTags[r].element.childNodes[0].innerText = h),
          a.geometry.setPositions([...a.data.points]),
          a.css3dTags[r].position.set(d.x, d.y, d.z),
          (a.css3dTags[r].visible = o ? !0 : l > 0.5)
      }
    e?.lineColor && (t && (a.data.lineColor = e.lineColor), a.material.color.set(e.lineColor)),
      e?.lineWidth && (t && (a.data.lineWidth = e.lineWidth), (a.material.linewidth = e.lineWidth)),
      e?.mainTagColor &&
        (t && (a.data.mainTagColor = e.mainTagColor),
        (document.querySelector(`.${a.data.name}-main-tag`).style.color = e.mainTagColor)),
      e?.mainTagBackground &&
        (t && (a.data.mainTagBackground = e.mainTagBackground),
        (document.querySelector(`.${a.data.name}-main-tag`).style.background =
          e.mainTagBackground)),
      e?.mainTagOpacity &&
        (t && (a.data.mainTagOpacity = e.mainTagOpacity),
        (document.querySelector(`.${a.data.name}-main-tag`).style.opacity =
          e.mainTagOpacity.toString())),
      e?.mainTagVisible !== void 0 &&
        (t && (a.data.mainTagVisible = e.mainTagVisible),
        (a.css3dMainTag.visible = e.mainTagVisible)),
      e?.distanceTagColor &&
        (t && (a.data.distanceTagColor = e.distanceTagColor),
        document
          .querySelectorAll(`.${a.data.name}-distance-tag`)
          ?.forEach((r) => (r.style.color = e.distanceTagColor))),
      e?.distanceTagBackground &&
        (t && (a.data.distanceTagBackground = e.distanceTagBackground),
        document
          .querySelectorAll(`.${a.data.name}-distance-tag`)
          ?.forEach((r) => (r.style.background = e.distanceTagBackground))),
      e?.distanceTagOpacity &&
        (t && (a.data.distanceTagOpacity = e.distanceTagOpacity),
        document
          .querySelectorAll(`.${a.data.name}-distance-tag`)
          ?.forEach((r) => (r.style.opacity = e.distanceTagOpacity.toString()))),
      e?.distanceTagVisible !== void 0 &&
        (t && (a.data.distanceTagVisible = e.distanceTagVisible),
        a.css3dTags.forEach((i) => (i.visible = e.distanceTagVisible))),
      e?.title !== void 0 &&
        (t && (a.data.title = e.title),
        (this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText = e.title)),
      e?.note !== void 0 && t && (a.data.note = e.note),
      a.events.fire(W)
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this,
      n = []
    for (let i = 0, r = t.data.points.length / 3; i < r; i++)
      n.push(new x(t.data.points[i * 3], t.data.points[i * 3 + 1], t.data.points[i * 3 + 2]))
    let o, a
    for (let i = 1; i < n.length; i++)
      (o = n[i - 1]),
        (a = n[i]),
        (t.css3dTags[i - 1].element.childNodes[0].innerText =
          (o.distanceTo(a) * e).toFixed(2) + ' m')
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return
    const t = this,
      n = t.data.points.length,
      o = new x().fromArray(t.data.points.slice(n - 6, n - 3)),
      a = new x().fromArray(t.data.points.slice(n - 3))
    if (
      ((!e || o.distanceTo(a) < 1e-3) &&
        (t.data.points.pop(), t.data.points.pop(), t.data.points.pop(), t.draw(t.data)),
      t.events.fire(_e),
      t.data.points.length < 6)
    ) {
      t.dispose()
      return
    } else {
      for (; t.css3dTags.length > t.data.points.length / 3 - 1; ) {
        const c = t.css3dTags.pop()
        t.group.remove(c), c.element.parentElement?.removeChild(c.element)
      }
      t.css3dTags[t.css3dTags.length - 1].visible = !0
    }
    const i = parent?.onActiveMark,
      r = t.getMarkData(!0)
    ;(r.isNew = !0), (r.meterScale = t.events.fire(I).meterScale), i?.(r)
  }
  /**
   * 根据数据直接绘制
   */
  draw(e, t = !1) {
    if (this.disposed) return
    const n = this
    this.css3dTags = this.css3dTags || []
    const o = {
        type: 'MarkMultiLines',
        name: e.name || 'lines' + Date.now(),
        points: [...e.points],
        lineColor: e.lineColor || '#eeee00',
        lineWidth: e.lineWidth || 3,
        mainTagColor: e.mainTagColor || '#c4c4c4',
        mainTagBackground: e.mainTagBackground || '#2E2E30',
        mainTagOpacity: e.mainTagOpacity || 0.8,
        mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
        distanceTagColor: e.distanceTagColor || '#000000',
        distanceTagBackground: e.distanceTagBackground || '#e0ffff',
        distanceTagOpacity: e.distanceTagOpacity || 0.9,
        distanceTagVisible: e.distanceTagVisible === void 0 ? !0 : e.distanceTagVisible,
        title:
          e.title ||
          '标记线' + (document.querySelectorAll('.mark-wrap-lines.main-warp').length + 1),
        note: e.note || '',
      },
      a = n.geometry,
      i = n.material,
      r = new At()
    r.setPositions([...o.points])
    const c = new Je({ color: o.lineColor, linewidth: o.lineWidth })
    c.resolution.set(innerWidth, innerHeight), n.copy(new Oe(r, c)), a?.dispose(), i?.dispose()
    const u = o.name,
      d = o.points.length / 3 - 1
    if (!n.css3dMainTag) {
      const l = document.createElement('div')
      ;(l.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${u}-main-tag" style="color:${o.mainTagColor};background:${o.mainTagBackground};opacity:${o.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${o.title}</span>
                                     </div>`),
        l.classList.add('mark-wrap-lines', `${o.name}`, 'main-warp'),
        (l.style.position = 'absolute'),
        (l.style.borderRadius = '4px'),
        (l.style.cursor = 'pointer'),
        (l.onclick = () => {
          if (n.events.fire(I).markMode) return
          const m = parent?.onActiveMark,
            f = n.getMarkData(!0)
          ;(f.meterScale = n.events.fire(I).meterScale), m?.(f), n.events.fire(te)
        }),
        (l.oncontextmenu = (m) => m.preventDefault())
      const h = new oe(l)
      h.position.set(o.points[0], o.points[1], o.points[2]),
        (h.element.style.pointerEvents = 'none'),
        h.scale.set(0.01, 0.01, 0.01),
        (h.visible = o.mainTagVisible),
        n.group.add(h),
        (n.css3dMainTag = h)
    }
    for (let l = n.css3dTags.length; l < d; l++) {
      const h = new x().fromArray(o.points.slice(l * 3, l * 3 + 3)),
        m = new x().fromArray(o.points.slice(l * 3 + 3, l * 3 + 6)),
        f = new x((h.x + m.x) / 2, (h.y + m.y) / 2, (h.z + m.z) / 2),
        g = (h.distanceTo(m) * n.events.fire(I).meterScale).toFixed(2) + ' m',
        y = document.createElement('div')
      ;(y.innerHTML = `<span class="${u}-distance-tag ${u}-distance-tag${l}" style="color:${o.distanceTagColor};background:${o.distanceTagBackground};opacity:${o.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${g}</span>`),
        y.classList.add('mark-wrap-lines', `${u}`, 'distance-warp'),
        (y.style.position = 'absolute'),
        (y.style.borderRadius = '4px'),
        (y.style.display = 'none')
      const E = new oe(y)
      E.position.copy(f),
        (E.element.style.pointerEvents = 'none'),
        E.scale.set(8e-3, 8e-3, 8e-3),
        (E.visible = o.distanceTagVisible),
        n.css3dTags.push(E),
        n.group.add(E)
    }
    t || (n.css3dTags[n.css3dTags.length - 1].visible = !1), (n.data = o), n.events.fire(De, n)
  }
  getMarkData(e = !1) {
    const t = { ...this.data }
    return e ? delete t.points : (t.points = [...t.points]), t
  }
  dispose() {
    if (this.disposed) return
    const e = this
    ;(e.disposed = !0),
      e.events.fire(Ie, e),
      e.events.fire(z).remove(e),
      e.events.fire(tt, e),
      e.geometry.dispose(),
      e.material.dispose(),
      document.querySelectorAll(`.${e.data.name}`).forEach((n) => n.parentElement?.removeChild(n)),
      (e.events = null),
      (e.data = null),
      (e.css3dTags = null),
      (e.group = null)
  }
}
class xo extends sn {
  constructor(e, t, n) {
    super(), (this.isMark = !0), (this.disposed = !1), (this.events = e)
    const o = this
    let a
    if (t instanceof x) {
      const c = document.querySelectorAll('.mark-wrap-point').length + 1
      a = {
        type: 'MarkSinglePoint',
        name: n || 'point' + Date.now(),
        point: t.toArray(),
        iconName: '#svgicon-point2',
        iconColor: '#eeee00',
        iconOpacity: 0.8,
        mainTagColor: '#c4c4c4',
        mainTagBackground: '#2E2E30',
        mainTagOpacity: 0.8,
        title: '标记点' + c,
        note: '',
      }
    } else
      a = {
        type: 'MarkSinglePoint',
        name: t.name || 'point' + Date.now(),
        point: [...t.point],
        iconName: t.iconName || '#svgicon-point2',
        iconColor: t.iconColor || '#eeee00',
        iconOpacity: t.iconOpacity || 0.8,
        mainTagColor: t.mainTagColor || '#c4c4c4',
        mainTagBackground: t.mainTagBackground || '#2E2E30',
        mainTagOpacity: t.mainTagOpacity || 0.8,
        title: t.title || '标记点',
        note: t.note || '',
      }
    const i = document.createElement('div')
    ;(i.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${a.name}" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                <svg height="20" width="20" style="color:${a.iconColor};opacity:${a.iconOpacity};"><use href="${a.iconName}" fill="currentColor" /></svg>
                             </div>`),
      i.classList.add('mark-wrap-point', `mark-wrap-${a.name}`),
      (i.style.position = 'absolute'),
      (i.style.borderRadius = '4px'),
      (i.style.cursor = 'pointer'),
      (i.onclick = () => {
        if (o.events.fire(I).markMode) return
        const c = parent?.onActiveMark
        c?.(o.getMarkData(!0)), o.events.fire(te)
      }),
      (i.oncontextmenu = (c) => c.preventDefault())
    const r = new oe(i)
    r.position.set(a.point[0], a.point[1], a.point[2]),
      (r.element.style.pointerEvents = 'none'),
      r.scale.set(0.01, 0.01, 0.01),
      (o.data = a),
      (o.css3dTag = r),
      o.add(r),
      e.fire(De, o)
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0) {
    if (this.disposed) return
    const n = this
    if (e?.iconName) {
      t && (n.data.iconName = e.iconName)
      const o = this.css3dTag.element.querySelector(`.mark-wrap-${n.data.name} svg`)
      o.innerHTML = `<use href="${e.iconName}" fill="currentColor" />`
    }
    if (e?.iconColor) {
      t && (n.data.iconColor = e.iconColor)
      const o = this.css3dTag.element.querySelector(`.mark-wrap-${n.data.name} svg`)
      o.style.color = e.iconColor
    }
    if (e?.iconOpacity) {
      t && (n.data.iconOpacity = e.iconOpacity)
      const o = this.css3dTag.element.querySelector(`.mark-wrap-${n.data.name} svg`)
      o.style.opacity = e.iconOpacity.toString()
    }
    e?.mainTagColor &&
      (t && (n.data.mainTagColor = e.mainTagColor),
      (this.css3dTag.element.querySelector(`.${n.data.name}`).style.color = e.mainTagColor)),
      e?.mainTagBackground &&
        (t && (n.data.mainTagBackground = e.mainTagBackground),
        (this.css3dTag.element.querySelector(`.${n.data.name}`).style.background =
          e.mainTagBackground)),
      e?.mainTagOpacity &&
        (t && (n.data.mainTagOpacity = e.mainTagOpacity),
        (this.css3dTag.element.querySelector(`.${n.data.name}`).style.opacity =
          e.mainTagOpacity.toString())),
      e?.title !== void 0 &&
        (t && (n.data.title = e.title),
        (this.css3dTag.element.querySelector(`.${n.data.name}`).innerText = e.title)),
      e?.note !== void 0 && t && (n.data.note = e.note),
      n.events.fire(W)
  }
  resetMeterScale(e) {
    e?.meterScale !== void 0 && (this.events.fire(I).meterScale = e.meterScale)
  }
  /**
   * 绘制结束
   */
  drawFinish() {
    if (this.disposed) return
    const e = this
    e.events.fire(_e)
    const t = parent?.onActiveMark,
      n = e.getMarkData(!0)
    ;(n.isNew = !0), (n.meterScale = e.events.fire(I).meterScale), t?.(n)
  }
  getMarkData(e = !1) {
    const t = { ...this.data }
    return e ? delete t.point : (t.point = [...t.point]), t
  }
  dispose() {
    if (this.disposed) return
    const e = this
    ;(e.disposed = !0), e.events.fire(Ie, e), e.events.fire(z).remove(e), e.events.fire(tt, e)
    const t = document.querySelector(`.mark-wrap-${e.data.name}`)
    t?.parentElement?.removeChild?.(t), (e.events = null), (e.data = null), (e.css3dTag = null)
  }
}
class Nn extends Oe {
  constructor(e) {
    super(),
      (this.isMark = !0),
      (this.disposed = !1),
      (this.css3dTags = []),
      (this.group = new sn()),
      this.add(this.group),
      (this.events = e)
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return
    const n = document.querySelectorAll('.mark-wrap-plans.main-warp').length + 1,
      o = {
        type: 'MarkMultiPlans',
        name: t || 'plans' + Date.now(),
        points: [...e.toArray(), ...e.toArray()],
        lineColor: '#eeee00',
        lineWidth: 3,
        mainTagColor: '#c4c4c4',
        mainTagBackground: '#2E2E30',
        mainTagOpacity: 0.8,
        mainTagVisible: !0,
        areaTagColor: '#000000',
        areaTagBackground: '#e0ffff',
        areaTagOpacity: 0.8,
        distanceTagVisible: !0,
        areaTagVisible: !0,
        planOpacity: 0.5,
        title: '标记面' + n,
        note: '',
      }
    this.draw(o)
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0, n, o = !1) {
    if (this.disposed) return
    const a = this
    if (n)
      if (o) {
        const i = a.data.points.length,
          r = a.css3dTags.length - 1,
          c = new x().fromArray(a.data.points.slice(i - 6, i - 3)),
          u = n,
          d = new x((c.x + u.x) / 2, (c.y + u.y) / 2, (c.z + u.z) / 2),
          h = (c.distanceTo(u) * a.events.fire(I).meterScale).toFixed(2) + ' m'
        ;(a.css3dTags[r].element.innerText = h),
          a.css3dTags[r].position.set(d.x, d.y, d.z),
          (a.css3dTags[r].visible = !0),
          a.data.points.pop(),
          a.data.points.pop(),
          a.data.points.pop(),
          (a.data.points = [...a.data.points, ...n.toArray(), ...n.toArray()]),
          this.draw(this.data)
      } else {
        const i = a.data.points.length
        ;(a.data.points[i - 3] = n.x), (a.data.points[i - 2] = n.y), (a.data.points[i - 1] = n.z)
        const r = a.css3dTags.length - 1,
          c = new x().fromArray(a.data.points.slice(i - 6, i - 3)),
          u = new x().fromArray(a.data.points.slice(i - 3)),
          d = new x((c.x + u.x) / 2, (c.y + u.y) / 2, (c.z + u.z) / 2),
          l = c.distanceTo(u),
          h = (l * a.events.fire(I).meterScale).toFixed(2) + ' m'
        ;(a.css3dTags[r].element.innerText = h),
          a.geometry.setPositions([...a.data.points]),
          a.css3dTags[r].position.set(d.x, d.y, d.z),
          (a.css3dTags[r].visible = o ? !0 : l > 0.5),
          a.meshPlans.geometry.setAttribute('position', new Ge(new Float32Array(a.data.points), 3)),
          (a.meshPlans.geometry.attributes.position.needsUpdate = !0)
        const m = a.events.fire(Dn, a.data.points)
        a.css3dAreaTag.position.copy(m)
        const f = a.events.fire(_n, a.data.points)
        a.css3dAreaTag.element.childNodes[0].innerText = f.toFixed(2) + ' m²'
      }
    if (
      (e?.lineColor &&
        (t && (a.data.lineColor = e.lineColor),
        a.material.color.set(e.lineColor),
        a.meshPlans.material.color.set(e.lineColor)),
      e?.lineWidth && (t && (a.data.lineWidth = e.lineWidth), (a.material.linewidth = e.lineWidth)),
      e?.mainTagColor &&
        (t && (a.data.mainTagColor = e.mainTagColor),
        (document.querySelector(`.${a.data.name}-main-tag`).style.color = e.mainTagColor)),
      e?.mainTagBackground &&
        (t && (a.data.mainTagBackground = e.mainTagBackground),
        (document.querySelector(`.${a.data.name}-main-tag`).style.background =
          e.mainTagBackground)),
      e?.mainTagOpacity &&
        (t && (a.data.mainTagOpacity = e.mainTagOpacity),
        (document.querySelector(`.${a.data.name}-main-tag`).style.opacity =
          e.mainTagOpacity.toString())),
      e?.mainTagVisible !== void 0 &&
        (t && (a.data.mainTagVisible = e.mainTagVisible),
        (a.css3dMainTag.visible = e.mainTagVisible)),
      e?.areaTagVisible !== void 0 &&
        (t && (a.data.areaTagVisible = e.areaTagVisible),
        (a.css3dAreaTag.visible = e.areaTagVisible)),
      e?.areaTagColor)
    ) {
      t && (a.data.areaTagColor = e.areaTagColor)
      const i = document.querySelector(`.${a.data.name}-area-tag`)
      i && (i.style.color = e.areaTagColor)
    }
    if (e?.areaTagBackground) {
      t && (a.data.areaTagBackground = e.areaTagBackground)
      const i = document.querySelector(`.${a.data.name}-area-tag`)
      i && (i.style.background = e.areaTagBackground)
    }
    if (e?.areaTagOpacity) {
      t && (a.data.areaTagOpacity = e.areaTagOpacity)
      const i = document.querySelector(`.${a.data.name}-area-tag`)
      i && (i.style.opacity = e.areaTagOpacity.toString())
    }
    e?.distanceTagVisible !== void 0 &&
      (t && (a.data.distanceTagVisible = e.distanceTagVisible),
      a.css3dTags.forEach((i) => (i.visible = e.distanceTagVisible))),
      e?.planOpacity &&
        (t && (a.data.planOpacity = e.planOpacity),
        (this.meshPlans.material.opacity = e.planOpacity)),
      e?.title !== void 0 &&
        (t && (a.data.title = e.title),
        (this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText = e.title)),
      e?.note !== void 0 && t && (a.data.note = e.note),
      a.events.fire(W)
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this,
      n = []
    for (let r = 0, c = t.data.points.length / 3; r < c; r++)
      n.push(new x(t.data.points[r * 3], t.data.points[r * 3 + 1], t.data.points[r * 3 + 2]))
    let o, a
    for (let r = 1; r < n.length; r++)
      (o = n[r - 1]),
        (a = n[r]),
        (t.css3dTags[r - 1].element.innerText = (o.distanceTo(a) * e).toFixed(2) + ' m')
    const i = t.events.fire(Di, n, e)
    t.css3dAreaTag.element.childNodes[0].innerText = i.toFixed(2) + ' m²'
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return
    const t = this,
      n = t.data.points.length,
      o = new x().fromArray(t.data.points.slice(n - 6, n - 3)),
      a = new x().fromArray(t.data.points.slice(n - 3))
    if (
      ((!e || o.distanceTo(a) < 1e-4) &&
        (t.data.points.pop(), t.data.points.pop(), t.data.points.pop()),
      t.events.fire(_e),
      t.data.points.length < 9)
    ) {
      t.dispose()
      return
    }
    for (; t.css3dTags.length > t.data.points.length / 3 - 1; ) {
      const c = t.css3dTags.pop()
      t.group.remove(c), c.element.parentElement?.removeChild(c.element)
    }
    t.data.points.push(t.data.points[0], t.data.points[1], t.data.points[2]),
      t.draw(t.data, !0),
      (t.css3dTags[t.css3dTags.length - 1].visible = !0)
    const i = parent?.onActiveMark,
      r = t.getMarkData(!0)
    ;(r.isNew = !0), (r.meterScale = t.events.fire(I).meterScale), i?.(r)
  }
  /**
   * 根据数据直接绘制
   */
  draw(e, t = !1) {
    if (this.disposed) return
    const n = this
    this.css3dTags = this.css3dTags || []
    const o = {
        type: 'MarkMultiPlans',
        name: e.name || 'plans' + Date.now(),
        points: [...e.points],
        lineColor: e.lineColor || '#eeee00',
        lineWidth: e.lineWidth || 3,
        mainTagColor: e.mainTagColor || '#c4c4c4',
        mainTagBackground: e.mainTagBackground || '#2E2E30',
        mainTagOpacity: e.mainTagOpacity || 0.8,
        mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
        areaTagColor: e.areaTagColor || '#000000',
        areaTagBackground: e.areaTagBackground || '#e0ffff',
        areaTagOpacity: e.areaTagOpacity || 0.9,
        areaTagVisible: e.areaTagVisible === void 0 ? !0 : e.areaTagVisible,
        distanceTagVisible: e.distanceTagVisible === void 0 ? !0 : e.distanceTagVisible,
        planOpacity: e.planOpacity || 0.5,
        title:
          e.title ||
          '标记面' + (document.querySelectorAll('.mark-wrap-plans.main-warp').length + 1),
        note: e.note || '',
      },
      a = n.geometry,
      i = n.material,
      r = new At()
    r.setPositions([...o.points])
    const c = new Je({ color: o.lineColor, linewidth: o.lineWidth })
    c.resolution.set(innerWidth, innerHeight), n.copy(new Oe(r, c)), a?.dispose(), i?.dispose()
    const u = o.name,
      d = o.points.length / 3 - 1
    if (!n.css3dMainTag) {
      const m = document.createElement('div')
      ;(m.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${u}-main-tag" style="color:${o.mainTagColor};background:${o.mainTagBackground};opacity:${o.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${o.title}</span>
                                     </div>`),
        m.classList.add('mark-wrap-plans', `${o.name}`, 'main-warp'),
        (m.style.position = 'absolute'),
        (m.style.borderRadius = '4px'),
        (m.style.cursor = 'pointer'),
        (m.onclick = () => {
          if (n.events.fire(I).markMode) return
          const A = parent?.onActiveMark,
            g = n.getMarkData(!0)
          ;(g.meterScale = n.events.fire(I).meterScale), A?.(g), n.events.fire(te)
        }),
        (m.oncontextmenu = (A) => A.preventDefault())
      const f = new oe(m)
      f.position.set(o.points[0], o.points[1], o.points[2]),
        (f.element.style.pointerEvents = 'none'),
        f.scale.set(0.01, 0.01, 0.01),
        (f.visible = o.mainTagVisible),
        n.group.add(f),
        (n.css3dMainTag = f)
    }
    const l = n.events.fire(_n, o.points).toFixed(2) + ' m²',
      h = n.events.fire(Dn, o.points)
    if (n.css3dAreaTag)
      n.css3dAreaTag.position.copy(h), (n.css3dAreaTag.element.childNodes[0].innerText = l)
    else {
      const m = document.createElement('div')
      ;(m.innerHTML = `<span class="${u}-area-tag" style="color:${o.areaTagColor};background:${o.areaTagBackground};opacity:${o.areaTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: none;">${l}</span>`),
        m.classList.add('mark-wrap-plans', `${o.name}`, 'area-warp'),
        (m.style.position = 'absolute'),
        (m.style.borderRadius = '4px')
      const f = new oe(m)
      f.position.copy(h),
        (f.element.style.pointerEvents = 'none'),
        f.scale.set(0.01, 0.01, 0.01),
        n.group.add(f),
        (n.css3dAreaTag = f)
    }
    n.css3dAreaTag.visible = o.points.length > 6 && o.areaTagVisible
    for (let m = n.css3dTags.length; m < d; m++) {
      const f = new x().fromArray(o.points.slice(m * 3, m * 3 + 3)),
        A = new x().fromArray(o.points.slice(m * 3 + 3, m * 3 + 6)),
        g = new x((f.x + A.x) / 2, (f.y + A.y) / 2, (f.z + A.z) / 2),
        E = (f.distanceTo(A) * n.events.fire(I).meterScale).toFixed(2) + ' m',
        M = document.createElement('div')
      ;(M.innerText = E),
        (M.style.color = 'white'),
        M.classList.add('mark-wrap-plans', `${u}`, 'distance-warp'),
        (M.style.position = 'absolute'),
        (M.style.borderRadius = '4px'),
        (M.style.display = 'none')
      const S = new oe(M)
      S.position.copy(g),
        (S.element.style.pointerEvents = 'none'),
        S.scale.set(8e-3, 8e-3, 8e-3),
        (S.visible = o.distanceTagVisible),
        n.css3dTags.push(S),
        n.group.add(S)
    }
    n.drawPlans(o),
      t || (n.css3dTags[n.css3dTags.length - 1].visible = !1),
      (n.data = o),
      n.events.fire(De, n)
  }
  drawPlans(e) {
    const t = this
    if (!t.meshPlans) {
      const n = new Wo()
      n.setAttribute('position', new Ge(new Float32Array(), 3)),
        (n.attributes.position.needsUpdate = !0)
      const o = new Ge(new Uint16Array([]), 1)
      n.setIndex(o)
      const a = new ke({
        color: e.lineColor,
        transparent: !0,
        opacity: e.planOpacity,
        side: be,
      })
      ;(t.meshPlans = new se(n, a)),
        (t.meshPlans.renderOrder = 1),
        (t.meshPlans.isMark = !0),
        t.group.add(t.meshPlans)
    }
    if (e.points.length > 6) {
      t.meshPlans.geometry.setAttribute('position', new Ge(new Float32Array(e.points), 3)),
        (t.meshPlans.geometry.attributes.position.needsUpdate = !0)
      let n = e.points.length / 3 - 2
      const o = new x().fromArray(e.points.slice(0, 3)),
        a = new x().fromArray(e.points.slice(-3))
      o.distanceTo(a) < 1e-4 && n--
      const i = new Uint16Array(n * 3)
      for (let r = 0; r < n; r++) (i[r * 3] = 0), (i[r * 3 + 1] = r + 1), (i[r * 3 + 2] = r + 2)
      t.meshPlans.geometry.setIndex(new Ge(i, 1))
    }
  }
  getMarkData(e = !1) {
    const t = { ...this.data }
    return e ? delete t.points : (t.points = [...t.points]), t
  }
  dispose() {
    if (this.disposed) return
    const e = this
    ;(e.disposed = !0),
      e.events.fire(Ie, e),
      e.events.fire(z).remove(e),
      e.events.fire(tt, e),
      e.geometry.dispose(),
      e.material.dispose(),
      document.querySelectorAll(`.${e.data.name}`).forEach((n) => n.parentElement?.removeChild(n)),
      (e.events = null),
      (e.data = null),
      (e.css3dTags = null),
      (e.group = null),
      (e.meshPlans = null),
      (e.css3dMainTag = null),
      (e.css3dAreaTag = null)
  }
}
class To extends sn {
  constructor(e) {
    super(), (this.isMark = !0), (this.disposed = !1), (this.events = e)
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return
    const n = this,
      o = document.querySelectorAll('.mark-wrap-circle.main-warp').length + 1,
      a = {
        type: 'MarkCirclePlan',
        name: t || 'circle' + Date.now(),
        startPoint: e.toArray(),
        radius: 0.05,
        circleColor: '#eeee00',
        circleOpacity: 0.5,
        mainTagColor: '#c4c4c4',
        mainTagBackground: '#2E2E30',
        mainTagOpacity: 0.8,
        mainTagVisible: !0,
        circleTagColor: '#000000',
        circleTagBackground: '#e0ffff',
        circleTagOpacity: 0.9,
        circleTagVisible: !0,
        title: '标记圆面' + o,
      },
      i = new Re(a.radius, 32),
      r = new ke({ color: a.circleColor, side: be, transparent: !0 })
    r.opacity = a.circleOpacity
    const c = new se(i, r)
    c.position.copy(e), (c.isMark = !0), (c.renderOrder = 1)
    const u = new x(0, 1, 0).normalize()
    c.lookAt(c.position.clone().add(u))
    const d = document.createElement('div')
    ;(d.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`),
      d.classList.add('mark-wrap-circle', `${a.name}`, 'main-warp'),
      (d.style.position = 'absolute'),
      (d.style.borderRadius = '4px'),
      (d.style.cursor = 'pointer'),
      (d.onclick = () => {
        if (n.events.fire(I).markMode) return
        const f = parent?.onActiveMark
        f?.(n.getMarkData(!0)), n.events.fire(te)
      }),
      (d.oncontextmenu = (f) => f.preventDefault())
    const l = new oe(d)
    l.position.copy(e),
      (l.element.style.pointerEvents = 'none'),
      l.scale.set(0.01, 0.01, 0.01),
      (l.visible = a.mainTagVisible)
    const h = document.createElement('div')
    ;(h.innerHTML = `<span class="${t}-circle-tag" style="color:${a.circleTagColor};background:${a.circleTagBackground};opacity:${a.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`),
      h.classList.add('mark-wrap-circle', `${t}`, 'circle-warp'),
      (h.style.position = 'absolute'),
      (h.style.borderRadius = '4px'),
      (h.style.pointerEvents = 'none')
    const m = new oe(h)
    m.position.set(e.x + Math.min(a.radius / 2, 0.5), e.y, e.z),
      (m.element.style.pointerEvents = 'none'),
      m.scale.set(8e-3, 8e-3, 8e-3),
      (m.visible = !1),
      n.add(c, l, m),
      (n.data = a),
      (n.circleMesh = c),
      (n.css3dTag = m),
      (n.css3dMainTag = l),
      n.events.fire(De, n)
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0, n) {
    if (this.disposed) return
    const o = this
    if (n) {
      const a = new x().fromArray(o.data.startPoint),
        i = a.distanceTo(new x(n.x, a.y, n.z))
      t && (o.data.radius = i),
        o.circleMesh.geometry.copy(new Re(i, 128)),
        (o.css3dTag.visible = i > 0.3)
      const r = i * o.events.fire(I).meterScale,
        c = (Math.PI * r * r).toFixed(2) + ' m²'
      ;(o.css3dTag.element.childNodes[0].innerText = c),
        o.css3dTag.position.set(a.x + Math.min(o.data.radius / 2, 0.5), a.y, a.z)
    }
    if (e?.radius) {
      const a = new x().fromArray(o.data.startPoint),
        i = e?.radius
      t && (o.data.radius = i),
        o.circleMesh.geometry.copy(new Re(i, 128)),
        (o.css3dTag.visible = i > 0.3)
      const r = i * o.events.fire(I).meterScale,
        c = (Math.PI * r * r).toFixed(2) + ' m²'
      ;(o.css3dTag.element.childNodes[0].innerText = c),
        o.css3dTag.position.set(a.x + Math.min(i / 2, 0.5), a.y, a.z)
    }
    e?.circleColor &&
      (t && (o.data.circleColor = e.circleColor), o.circleMesh.material.color.set(e.circleColor)),
      e?.circleOpacity &&
        (t && (o.data.circleOpacity = e.circleOpacity),
        (o.circleMesh.material.opacity = e.circleOpacity)),
      e?.mainTagColor &&
        (t && (o.data.mainTagColor = e.mainTagColor),
        (document.querySelector(`.${o.data.name}-main-tag`).style.color = e.mainTagColor)),
      e?.mainTagBackground &&
        (t && (o.data.mainTagBackground = e.mainTagBackground),
        (document.querySelector(`.${o.data.name}-main-tag`).style.background =
          e.mainTagBackground)),
      e?.mainTagOpacity &&
        (t && (o.data.mainTagOpacity = e.mainTagOpacity),
        (document.querySelector(`.${o.data.name}-main-tag`).style.opacity =
          e.mainTagOpacity.toString())),
      e?.mainTagVisible !== void 0 &&
        (t && (o.data.mainTagVisible = e.mainTagVisible),
        (o.css3dMainTag.visible = e.mainTagVisible)),
      e?.circleTagColor &&
        (t && (o.data.circleTagColor = e.circleTagColor),
        (document.querySelector(`.${o.data.name}-circle-tag`).style.color = e.circleTagColor)),
      e?.circleTagBackground &&
        (t && (o.data.circleTagBackground = e.circleTagBackground),
        (document.querySelector(`.${o.data.name}-circle-tag`).style.background =
          e.circleTagBackground)),
      e?.circleTagOpacity &&
        (t && (o.data.circleTagOpacity = e.circleTagOpacity),
        (document.querySelector(`.${o.data.name}-circle-tag`).style.opacity =
          e.circleTagOpacity.toString())),
      e?.circleTagVisible !== void 0 &&
        (t && (o.data.circleTagVisible = e.circleTagVisible),
        (o.css3dTag.visible = e.circleTagVisible)),
      e?.title !== void 0 &&
        (t && (o.data.title = e.title),
        (this.css3dMainTag.element.querySelector(`.${o.data.name}-main-tag`).innerText = e.title)),
      e?.note !== void 0 && t && (o.data.note = e.note),
      o.events.fire(W)
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this,
      n = t.data.radius * e,
      o = (Math.PI * n * n).toFixed(2) + ' m²'
    t.css3dTag.element.childNodes[0].innerText = o
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return
    const t = this,
      n = new x().fromArray(t.data.startPoint),
      o = n.distanceTo(new x(e.x, n.y, e.z))
    ;(t.data.radius = o), t.circleMesh.geometry.copy(new Re(o, 128))
    const a = t.data.radius * t.events.fire(I).meterScale,
      i = (Math.PI * a * a).toFixed(2) + ' m²'
    ;(t.css3dTag.element.childNodes[0].innerText = i), t.events.fire(_e)
    const r = parent?.onActiveMark,
      c = t.getMarkData(!0)
    ;(c.isNew = !0), (c.meterScale = t.events.fire(I).meterScale), r?.(c)
  }
  /**
   * 根据数据直接绘制
   */
  draw(e) {
    if (this.disposed) return
    const t = this,
      n = {
        type: 'MarkCirclePlan',
        name: e.name || 'circle' + Date.now(),
        startPoint: [...e.startPoint],
        radius: e.radius,
        circleColor: e.circleColor || '#eeee00',
        circleOpacity: e.circleOpacity || 0.5,
        mainTagColor: e.mainTagColor || '#c4c4c4',
        mainTagBackground: e.mainTagBackground || '#2E2E30',
        mainTagOpacity: e.mainTagOpacity || 0.8,
        mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
        circleTagColor: e.circleTagColor || '#000000',
        circleTagBackground: e.circleTagBackground || '#e0ffff',
        circleTagOpacity: e.circleTagOpacity || 0.9,
        circleTagVisible: e.circleTagVisible === void 0 ? !0 : e.circleTagVisible,
        title: e.title || '标记圆面',
      },
      o = new Re(n.radius, 128),
      a = new ke({ color: n.circleColor, side: be, transparent: !0 })
    a.opacity = n.circleOpacity
    const i = new se(o, a)
    i.position.fromArray(n.startPoint), (i.isMark = !0), (i.renderOrder = 1)
    const r = new x(0, 1, 0).normalize()
    i.lookAt(i.position.clone().add(r))
    const c = n.name,
      u = document.createElement('div')
    ;(u.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${c}-main-tag" style="color:${n.mainTagColor};background:${n.mainTagBackground};opacity:${n.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${n.title}</span>
                             </div>`),
      u.classList.add('mark-wrap-circle', `${n.name}`, 'main-warp'),
      (u.style.position = 'absolute'),
      (u.style.borderRadius = '4px'),
      (u.style.cursor = 'pointer'),
      (u.onclick = () => {
        if (t.events.fire(I).markMode) return
        const g = parent?.onActiveMark
        g?.(t.getMarkData(!0)), t.events.fire(te)
      }),
      (u.oncontextmenu = (g) => g.preventDefault())
    const d = new x().fromArray(n.startPoint),
      l = n.radius * t.events.fire(I).meterScale,
      h = (Math.PI * l * l).toFixed(2) + ' m²',
      m = new oe(u)
    m.position.copy(d),
      (m.element.style.pointerEvents = 'none'),
      m.scale.set(0.01, 0.01, 0.01),
      (m.visible = n.mainTagVisible)
    const f = document.createElement('div')
    ;(f.innerHTML = `<span class="${c}-distance-tag ${c}-circle-tag" style="color:${n.circleTagColor};background:${n.circleTagBackground};opacity:${n.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${h}</span>`),
      f.classList.add('mark-wrap-circle', `${c}`, 'circle-warp'),
      (f.style.position = 'absolute'),
      (f.style.borderRadius = '4px'),
      (f.style.pointerEvents = 'none')
    const A = new oe(f)
    A.position.set(d.x + Math.min(n.radius / 2, 0.5), d.y, d.z),
      (A.element.style.pointerEvents = 'none'),
      A.scale.set(8e-3, 8e-3, 8e-3),
      (A.visible = n.circleTagVisible),
      t.add(i, m, A),
      (t.data = n),
      (t.circleMesh = i),
      (t.css3dTag = A),
      (t.css3dMainTag = m),
      t.events.fire(De, t)
  }
  getMarkData(e = !1) {
    const t = { ...this.data }
    return e ? (delete t.startPoint, delete t.radius) : (t.startPoint = [...t.startPoint]), t
  }
  dispose() {
    if (this.disposed) return
    const e = this
    ;(e.disposed = !0),
      e.events.fire(Ie, e),
      e.events.fire(z).remove(e),
      e.events.fire(tt, e),
      document.querySelectorAll(`.${e.data.name}`).forEach((n) => n.parentElement?.removeChild(n)),
      (e.events = null),
      (e.data = null),
      (e.circleMesh = null),
      (e.css3dTag = null),
      (e.css3dMainTag = null)
  }
}
let Hr = class {
  constructor() {
    ;(this.down = 0),
      (this.move = !1),
      (this.downTime = 0),
      (this.isDbClick = !1),
      (this.x = 0),
      (this.y = 0),
      (this.lastClickX = 0),
      (this.lastClickY = 0),
      (this.lastClickPointTime = 0),
      (this.lastMovePoint = null),
      (this.lastMovePointTime = 0),
      (this.touchStartDistance = 0),
      (this.touchPrevDistance = 0),
      (this.touchStartX1 = 0),
      (this.touchStartY1 = 0),
      (this.touchStartX2 = 0),
      (this.touchStartY2 = 0),
      (this.touchPrevX1 = 0),
      (this.touchPrevY1 = 0),
      (this.touchPrevX2 = 0),
      (this.touchPrevY2 = 0)
  }
  // 第二个触摸点上一帧Y坐标
}
function Yr(s) {
  const e = (p, C, b) => s.on(p, C, b),
    t = (p, ...C) => s.fire(p, ...C),
    n = t(et)
  let o = /* @__PURE__ */ new Set(),
    a,
    i = new Hr(),
    r,
    c = 0,
    u = 0
  function d() {
    const p = t(O),
      C = new x()
    p.getWorldDirection(C), (c = Math.asin(C.y)), (u = Math.atan2(C.z, C.x))
  }
  e(Is, () => {
    t(U).autoRotate = t(I).autoRotate = !0
  }),
    e(te, (p = !0) => {
      ;(t(U).autoRotate = t(I).autoRotate = !1), p && t(nn)
    }),
    e(fn, (p = !0) => {
      if (a) return
      const C = t(U),
        b = p ? Math.PI / 128 : -(Math.PI / 128),
        w = new Te().makeRotationAxis(new x(0, 0, -1).transformDirection(C.object.matrixWorld), b)
      C.object.up.transformDirection(w), t(W)
    }),
    e(gn, () => {
      if (a) return
      if (t(I).useCustomControl === !0) {
        const b = t(O),
          w = t(z)
        let k
        if (
          (w.traverse((_) => {
            _ instanceof he && (k = _)
          }),
          k)
        ) {
          const _ = k.position.clone(),
            L = b.position.clone(),
            B = _.sub(L).normalize(),
            Z = -(Math.PI / 128),
            le = new Te().makeRotationAxis(B, Z)
          k.applyMatrix4(le)
        }
        t(W)
      } else s.fire(fn, !0)
    }),
    e(mn, () => {
      if (a) return
      if (t(I).useCustomControl === !0) {
        const b = t(O),
          w = t(z)
        let k
        if (
          (w.traverse((_) => {
            _ instanceof he && (k = _)
          }),
          k)
        ) {
          const _ = k.position.clone(),
            L = b.position.clone(),
            B = _.sub(L).normalize(),
            Z = Math.PI / 128,
            le = new Te().makeRotationAxis(B, Z)
          k.applyMatrix4(le)
        }
        t(W)
      } else s.fire(fn, !1)
    }),
    e(Jt, (p) => {
      const C = t(I)
      p ?? (p = !C.pointcloudMode),
        (C.pointcloudMode = p),
        t(z).traverse((w) => w instanceof he && w.fire(Ne, p))
    }),
    e(en, () => {
      t(z).traverse((C) => C instanceof he && C.fire(Fn))
    }),
    e(pt, (p) => {
      t(z).traverse((b) => b instanceof he && b.fire(pt, p))
    }),
    e(Ns, () => {
      const p = t(z)
      let C
      p.traverse((b) => b instanceof he && (C = b)), C && t(In, C.fire(uo))
    }),
    e(Tt, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = t(I),
        k = w.useCustomControl === !0,
        _ = p !== void 0 ? p : w.cameraMoveSpeed !== void 0 ? w.cameraMoveSpeed : 5e-3,
        L = new x()
      if ((C.getWorldDirection(L), C.position.addScaledVector(L, _), k)) {
        const B = new x().copy(C.position).add(L)
        b.target.copy(B)
      } else b.target.addScaledVector(L, _)
      t(W)
    }),
    e(kt, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = t(I),
        k = p !== void 0 ? p : w.cameraMoveSpeed !== void 0 ? w.cameraMoveSpeed : 5e-3,
        _ = new x()
      C.getWorldDirection(_),
        C.position.addScaledVector(_, -k),
        b.target.addScaledVector(_, -k),
        t(W)
    }),
    e(nt, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = t(I),
        k = p !== void 0 ? p : w.cameraMoveSpeed !== void 0 ? w.cameraMoveSpeed : 5e-3,
        _ = new x(),
        L = new x(0, 1, 0),
        B = new x()
      C.getWorldDirection(B),
        _.crossVectors(L, B).normalize(),
        C.position.addScaledVector(_, -k),
        b.target.addScaledVector(_, -k),
        t(W)
    }),
    e(st, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = t(I),
        k = p !== void 0 ? p : w.cameraMoveSpeed !== void 0 ? w.cameraMoveSpeed : 5e-3,
        _ = new x(),
        L = new x(0, 1, 0),
        B = new x()
      C.getWorldDirection(B),
        _.crossVectors(L, B).normalize(),
        C.position.addScaledVector(_, k),
        b.target.addScaledVector(_, k),
        t(W)
    }),
    e(Et, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = t(I),
        k = w.useCustomControl === !0,
        _ = p !== void 0 ? p : w.cameraMoveSpeed !== void 0 ? w.cameraMoveSpeed : 5e-3
      if (k) {
        const L = new x(0, 1, 0).applyQuaternion(C.quaternion)
        console.log('[CameraMoveUp] 第一人称模式下使用屏幕上方向:', L.toArray()),
          C.position.addScaledVector(L, _)
        const B = new x()
        C.getWorldDirection(B)
        const Z = new x().copy(C.position).add(B)
        b.target.copy(Z)
      } else {
        const L = new x(0, 1, 0)
        C.position.addScaledVector(L, -_), b.target.addScaledVector(L, -_)
      }
      t(W)
    }),
    e(Dt, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = t(I),
        k = w.useCustomControl === !0,
        _ = p !== void 0 ? p : w.cameraMoveSpeed !== void 0 ? w.cameraMoveSpeed : 5e-3
      if (k) {
        const L = new x(0, 1, 0).applyQuaternion(C.quaternion)
        console.log('[CameraMoveDown] 第一人称模式下使用屏幕下方向:', L.toArray()),
          C.position.addScaledVector(L, -_)
        const B = new x()
        C.getWorldDirection(B)
        const Z = new x().copy(C.position).add(B)
        b.target.copy(Z)
      } else {
        const L = new x(0, 1, 0)
        C.position.addScaledVector(L, _), b.target.addScaledVector(L, _)
      }
      t(W)
    }),
    e(_t, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = C.position.clone()
      ;(c += p !== void 0 ? p : 0.05),
        (c = Math.min(Math.PI / 2 - 0.1, Math.max(-Math.PI / 2 + 0.1, c)))
      const _ = new x(
          Math.cos(c) * Math.cos(u),
          Math.sin(c),
          Math.cos(c) * Math.sin(u),
        ).normalize(),
        L = new x().copy(w).add(_)
      b.target.copy(L), C.lookAt(L), C.position.copy(w), t(W)
    }),
    e(It, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = C.position.clone()
      ;(c -= p !== void 0 ? p : 0.05),
        (c = Math.min(Math.PI / 2 - 0.1, Math.max(-Math.PI / 2 + 0.1, c)))
      const _ = new x(
          Math.cos(c) * Math.cos(u),
          Math.sin(c),
          Math.cos(c) * Math.sin(u),
        ).normalize(),
        L = new x().copy(w).add(_)
      b.target.copy(L), C.lookAt(L), C.position.copy(w), t(W)
    }),
    e(Pt, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = C.position.clone()
      console.log('[CameraRotateLeftFPS] 旋转前相机位置:', w.toArray()),
        (u += p !== void 0 ? p : 0.05)
      const _ = new x(
          Math.cos(c) * Math.cos(u),
          Math.sin(c),
          Math.cos(c) * Math.sin(u),
        ).normalize(),
        L = new x().copy(w).add(_)
      b.target.copy(L),
        C.lookAt(L),
        C.position.copy(w),
        console.log('[CameraRotateLeftFPS] 旋转后相机位置:', C.position.toArray()),
        (b.minDistance = b.maxDistance = b.getDistance()),
        t(W)
    }),
    e(Lt, (p = void 0) => {
      if (a) return
      const C = t(O),
        b = t(U),
        w = C.position.clone()
      u -= p !== void 0 ? p : 0.05
      const _ = new x(
          Math.cos(c) * Math.cos(u),
          Math.sin(c),
          Math.cos(c) * Math.sin(u),
        ).normalize(),
        L = new x().copy(w).add(_)
      b.target.copy(L),
        C.lookAt(L),
        C.position.copy(w),
        (b.minDistance = b.maxDistance = b.getDistance()),
        t(W)
    }),
    e(an, () => {
      if (!o.size) return
      const p = t(I)
      if (!p.enableKeyboard) return o.clear()
      if (p.markMode && o.has('Escape')) {
        t(ot), o.clear()
        return
      }
      const C = p.useCustomControl === !0,
        b = o.has('Shift')
      b && C
        ? (o.has('KeyW') && t(It, 0.01),
          o.has('KeyS') && t(_t, 0.01),
          o.has('KeyA') && t(Pt, 0.01),
          o.has('KeyD') && t(Lt, 0.01))
        : C &&
          !b &&
          (o.has('KeyW') && t(Tt),
          o.has('KeyS') && t(kt),
          o.has('KeyA') && t(nt),
          o.has('KeyD') && t(st),
          o.has('ArrowLeft') && t(nt),
          o.has('ArrowRight') && t(st),
          o.has('ArrowUp') && t(Et),
          o.has('ArrowDown') && t(Dt)),
        o.has('Space')
          ? (p.bigSceneMode ? t(Jt) : t(en), o.clear())
          : o.has('KeyR')
            ? (p.autoRotate ? t(te) : t(Is), o.clear())
            : o.has('KeyM')
              ? (t(qe, !p.markVisible), o.clear())
              : C && o.has('KeyQ')
                ? (t(gn), o.clear())
                : C && o.has('KeyE')
                  ? (t(mn), o.clear())
                  : !C && o.has('ArrowLeft')
                    ? (t(gn), t(Yt, !0), o.clear())
                    : !C && o.has('ArrowRight')
                      ? (t(mn), t(Yt, !0), o.clear())
                      : o.has('KeyP')
                        ? (t(tn, !0), o.clear())
                        : o.has('Equal')
                          ? (t(ls), o.clear())
                          : o.has('Minus')
                            ? (t(ds), o.clear())
                            : o.has('KeyY')
                              ? (t(Un), o.clear())
                              : o.has('KeyI')
                                ? (t(oo), o.clear())
                                : o.has('KeyF')
                                  ? (t(Ns), o.clear())
                                  : o.has('F2') &&
                                    (!p.bigSceneMode &&
                                      window.open(
                                        '/editor/index.html?url=' +
                                          encodeURIComponent(t(ve).meta.url),
                                      ),
                                    o.clear())
    }),
    e(lt, async (p, C) => {
      if (i.move) return
      if (t(I).useCustomControl === !0) {
        console.log('[SelectPointAndLookAt] 第一人称模式下忽略点击选择')
        return
      }
      const k = await t(jt, p, C)
      k.length &&
        (console.log('[SelectPointAndLookAt] 设置相机目标点:', k[0].toArray()), t(In, k[0], !0))
    }),
    e(ue, async (p, C) => {
      const b = t(z),
        w = []
      b.traverse((_) => _ instanceof he && w.push(_))
      const k = await t(jt, p, C)
      return k.length
        ? (w.length && w[0].fire(Wt, k[0].x, k[0].y, k[0].z, !0), new x(k[0].x, k[0].y, k[0].z))
        : (w.length && w[0].fire(Wt, 0, 0, 0, !1), null)
    }),
    e(is, async () => {
      const p = t(z),
        C = []
      p.traverse((b) => b instanceof he && C.push(b))
      for (let b = 0; b < C.length; b++) C[b].fire(Wt, 0, 0, 0, !1)
    })
  const l = (p) => {
      p.target.type !== 'text' &&
        (a ||
          p.code === 'F5' ||
          (p.preventDefault(),
          p.code !== 'KeyR' &&
            (o.add(p.code),
            (p.code === 'ShiftLeft' || p.code === 'ShiftRight') && o.add('Shift'),
            t(te)),
          (r = Date.now())))
    },
    h = (p) => {
      p.target.type !== 'text' &&
        (a ||
          (p.code === 'KeyR' && o.add(p.code),
          (p.code === 'ArrowLeft' || p.code === 'ArrowRight') && t(Li),
          o.delete(p.code),
          (p.code === 'ShiftLeft' || p.code === 'ShiftRight') && o.delete('Shift'),
          (r = Date.now())))
    }
  e(
    He,
    () => {
      t(os) && Date.now() - r > 2e3 && (t(Yt, !1), t(Un))
    },
    !0,
  )
  const m = () => {
      o.clear()
    },
    f = (p) => {
      if ((parent && setTimeout(() => window.focus()), p.preventDefault(), a)) return
      if ((t(te), t(I).useCustomControl === !0)) {
        const w = t(O),
          k = t(U),
          _ = p.deltaY > 0 ? 1.02 : 0.98,
          L = w.position.clone().sub(k.target).normalize(),
          B = w.position.distanceTo(k.target),
          Z = Math.max(0.1, Math.min(1e3, B * _)),
          le = k.target.clone().add(L.multiplyScalar(Z))
        w.position.copy(le),
          t(W),
          console.log('[Wheel] useCustomControl模式下缩放，距离:', Z.toFixed(2))
      }
      r = Date.now()
    },
    A = (p) => {
      p.preventDefault(), !a && (t(te), (r = Date.now()))
    }
  let g, y, E, M
  e(ot, () => {
    g?.dispose(),
      y?.dispose(),
      E?.dispose(),
      M?.dispose(),
      (g = null),
      (y = null),
      (E = null),
      (M = null),
      (i.lastMovePoint = null),
      t(_e)
  })
  const S = async (p) => {
      if ((parent && setTimeout(() => window.focus()), p.preventDefault(), a)) return
      t(te),
        p.button === 1 ? (i.down = 3) : (i.down = p.button === 2 ? 2 : 1),
        (i.move = !1),
        (i.isDbClick = Date.now() - i.downTime < 300),
        (i.x = p.clientX),
        (i.y = p.clientY)
      const C = t(I)
      if (C.useCustomControl === !0) {
        const w = t(O),
          k = w.position.clone()
        console.log('[MouseDown] FPS模式下相机位置:', k.toArray())
        const _ = t(U)
        ;(_.enabled = !1),
          (_.minDistance = _.maxDistance = _.getDistance()),
          _.updateControlMode(C),
          console.log('[MouseDown] FPS模式下已禁用控制器，按钮:', i.down),
          i.down === 1 &&
            (console.log('[MouseDown] FPS模式下左键点击，位置:', p.clientX, p.clientY),
            Math.abs(c) < 1e-3 &&
              Math.abs(u) < 1e-3 &&
              (console.log('[MouseDown] 初始化欧拉角'), d()),
            w.position.copy(k),
            p.stopPropagation())
      }
      ;(r = Date.now()), (i.downTime = Date.now())
    },
    P = async (p) => {
      if ((p.preventDefault(), a)) return
      const C = t(I),
        b = C.useCustomControl === !0
      if (i.down) {
        const w = p.clientX - i.x,
          k = p.clientY - i.y
        if (i.down === 3) {
          p.stopPropagation()
          const _ = t(U)
          _.enabled = !1
          const L = C.cameraMoveSpeed !== void 0 ? C.cameraMoveSpeed : 5e-3,
            B = 0.5
          Math.abs(k) > 2 && (k > 0 ? t(kt, Math.abs(k) * B * L) : t(Tt, Math.abs(k) * B * L)),
            (i.x = p.clientX),
            (i.y = p.clientY),
            t(W),
            (i.move = !0),
            (r = Date.now())
          return
        }
        if (i.down === 2 && b) {
          p.stopPropagation()
          const _ = t(U)
          _.enabled = !1
          const L = C.cameraMoveSpeed !== void 0 ? C.cameraMoveSpeed : 5e-3,
            B = 0.5
          Math.abs(w) > 2 && (w > 0 ? t(st, Math.abs(w) * B * L) : t(nt, Math.abs(w) * B * L)),
            Math.abs(k) > 2 && (k > 0 ? t(Dt, Math.abs(k) * B * L) : t(Et, Math.abs(k) * B * L)),
            (i.x = p.clientX),
            (i.y = p.clientY),
            t(W),
            (i.move = !0),
            (r = Date.now())
          return
        }
        if (b && i.down === 1) {
          p.stopPropagation()
          const _ = t(U)
          _.enabled = !1
          const L = t(O),
            B = L.position.clone()
          console.log('[MouseMove] FPS模式下左键拖动，移动距离:', w, k)
          const Z = 1e-3,
            le = 1e-3
          Math.abs(c) < 1e-3 && Math.abs(u) < 1e-3 && d(),
            Math.abs(w) > 0 && (w > 0 ? t(Lt, Math.abs(w) * Z) : t(Pt, Math.abs(w) * Z)),
            Math.abs(k) > 0 && (k > 0 ? t(_t, Math.abs(k) * le) : t(It, Math.abs(k) * le)),
            L.position.copy(B),
            (_.minDistance = _.maxDistance = _.getDistance()),
            (i.x = p.clientX),
            (i.y = p.clientY),
            t(W),
            (i.move = !0),
            (r = Date.now())
          return
        }
        ;(i.move = !0), (r = Date.now())
      }
      if (!b && C.markMode) {
        const w = await t(ue, p.clientX, p.clientY)
        w && !i.down && C.markType === 'distance' && g
          ? g.drawUpdate({ endPoint: w.toArray() })
          : !i.down && C.markType === 'circle' && M
            ? w
              ? (M.drawUpdate(null, !0, w),
                (i.lastMovePoint = w),
                (i.lastMovePointTime = Date.now()))
              : ((i.lastMovePoint = null), (i.lastMovePointTime = 0))
            : !i.down && C.markType === 'lines' && y
              ? w
                ? (y.drawUpdate(null, !0, w),
                  (i.lastMovePoint = w),
                  (i.lastMovePointTime = Date.now()))
                : ((i.lastMovePoint = null), (i.lastMovePointTime = 0))
              : !i.down &&
                C.markType === 'plans' &&
                E &&
                (w
                  ? (E.drawUpdate(null, !0, w),
                    (i.lastMovePoint = w),
                    (i.lastMovePointTime = Date.now()))
                  : ((i.lastMovePoint = null), (i.lastMovePointTime = 0)))
      }
    },
    T = async (p) => {
      if ((p.preventDefault(), a)) return
      const C = t(I),
        b = C.useCustomControl === !0
      if (b) {
        const w = t(O),
          k = t(U),
          _ = w.position.clone()
        console.log('[MouseUp] FPS模式下松开鼠标，按钮:', i.down, '是否移动:', i.move),
          (k.enabled = !1),
          k.updateControlMode(C),
          (k.minDistance = k.maxDistance = k.getDistance()),
          w.position.copy(_),
          console.log('[MouseUp] FPS模式下相机位置:', w.position.toArray()),
          p.stopPropagation()
      } else if (b && i.down === 2 && i.move) {
        const w = t(U)
        ;(w.enabled = !0), w.update(), p.stopPropagation()
      } else if (i.down === 3 && i.move) {
        const w = t(U)
        ;(w.enabled = !0), w.update(), p.stopPropagation()
      }
      if (
        (i.isDbClick &&
          (y
            ? Math.abs(p.clientX - i.lastClickX) < 2 &&
              Math.abs(p.clientY - i.lastClickY) < 2 &&
              (y.drawFinish(i.lastClickPointTime > 0), (y = null), (i.lastMovePoint = null))
            : E &&
              Math.abs(p.clientX - i.lastClickX) < 2 &&
              Math.abs(p.clientY - i.lastClickY) < 2 &&
              (E.drawFinish(i.lastClickPointTime > 0), (E = null), (i.lastMovePoint = null))),
        C.markMode && i.down === 1 && !i.move && Date.now() - i.downTime < 500)
      ) {
        if (C.markType === 'point') {
          if (await t(ue, p.clientX, p.clientY)) {
            const k = new xo(s, await t(ue, p.clientX, p.clientY))
            t(z).add(k), k.drawFinish()
          }
        } else if (C.markType === 'distance')
          if (g) {
            const w = await t(ue, p.clientX, p.clientY)
            w ? (g.drawFinish(w), (g = null)) : i.isDbClick && t(ot)
          } else {
            const w = await t(ue, p.clientX, p.clientY)
            w && ((g = new Gn(s)), g.drawStart(w), t(z).add(g))
          }
        else if (C.markType === 'lines')
          if (y)
            if (i.lastMovePoint && t(Bn, p.clientX, p.clientY, i.lastMovePoint) < 0.03)
              y.drawUpdate(null, !0, i.lastMovePoint, !0), (i.lastClickPointTime = Date.now())
            else {
              const w = await t(ue, p.clientX, p.clientY)
              w
                ? (y.drawUpdate(null, !0, w, !0), (i.lastClickPointTime = Date.now()))
                : (i.lastClickPointTime = 0)
            }
          else {
            const w = await t(ue, p.clientX, p.clientY)
            w && ((y = new Xn(s)), y.drawStart(w), t(z).add(y))
          }
        else if (C.markType === 'plans')
          if (E)
            if (i.lastMovePoint && t(Bn, p.clientX, p.clientY, i.lastMovePoint) < 0.03)
              E.drawUpdate(null, !0, i.lastMovePoint, !0), (i.lastClickPointTime = Date.now())
            else {
              const w = await t(ue, p.clientX, p.clientY)
              w
                ? (E.drawUpdate(null, !0, w, !0), (i.lastClickPointTime = Date.now()))
                : (i.lastClickPointTime = 0)
            }
          else {
            const w = await t(ue, p.clientX, p.clientY)
            w && ((E = new Nn(s)), E.drawStart(w), t(z).add(E))
          }
        else if (C.markType === 'circle')
          if (M) {
            const w = await t(ue, p.clientX, p.clientY)
            w ? (M.drawFinish(w), (M = null)) : i.isDbClick && t(ot)
          } else {
            const w = await t(ue, p.clientX, p.clientY)
            w && ((M = new To(s)), M.drawStart(w), t(z).add(M))
          }
        ;(i.lastClickX = p.clientX), (i.lastClickY = p.clientY)
      }
      i.down === 2 &&
        !i.move &&
        (C.useCustomControl === !0
          ? console.log('[MouseUp] FPS模式下忽略右键点击')
          : (console.log('[MouseUp] 非FPS模式下右键点击，调整相机目标点'),
            t(lt, p.clientX, p.clientY))),
        (i.down = 0),
        (i.move = !1),
        (r = Date.now())
    }
  function D(p) {
    if ((p.preventDefault(), a)) return
    t(te), (i.down = p.touches.length)
    const b = t(I).useCustomControl === !0
    if (i.down === 1) {
      if (((i.move = !1), (i.x = p.touches[0].clientX), (i.y = p.touches[0].clientY), b)) {
        Math.abs(c) < 1e-3 && Math.abs(u) < 1e-3 && (console.log('[TouchStart] 初始化欧拉角'), d())
        const w = t(U)
        w.enabled = !1
      }
    } else if (i.down === 2 && b) {
      const w = p.touches[0],
        k = p.touches[1]
      ;(i.touchStartX1 = w.clientX),
        (i.touchStartY1 = w.clientY),
        (i.touchStartX2 = k.clientX),
        (i.touchStartY2 = k.clientY),
        (i.touchPrevX1 = w.clientX),
        (i.touchPrevY1 = w.clientY),
        (i.touchPrevX2 = k.clientX),
        (i.touchPrevY2 = k.clientY)
      const _ = w.clientX - k.clientX,
        L = w.clientY - k.clientY
      ;(i.touchStartDistance = Math.sqrt(_ * _ + L * L)),
        (i.touchPrevDistance = i.touchStartDistance),
        console.log('[TouchStart] 双指触摸开始, 初始距离:', i.touchStartDistance)
      const B = t(U)
      B.enabled = !1
    }
    r = Date.now()
  }
  function F(p) {
    if ((p.preventDefault(), a)) return
    const C = t(I),
      b = C.useCustomControl === !0
    if ((console.log('[TouchMove] 触摸移动', p.touches.length, b), p.touches.length === 1)) {
      if (((i.move = !0), b)) {
        const w = p.touches[0],
          k = w.clientX - i.x,
          _ = w.clientY - i.y,
          L = t(O),
          B = t(U),
          Z = L.position.clone(),
          le = 1e-3,
          vt = 1e-3
        console.log('[TouchMove] 单指模式视角控制，移动距离:', k, _),
          Math.abs(k) > 0 && (k < 0 ? t(Pt, Math.abs(k) * le) : t(Lt, Math.abs(k) * le)),
          Math.abs(_) > 0 && (_ < 0 ? t(It, Math.abs(_) * vt) : t(_t, Math.abs(_) * vt)),
          L.position.copy(Z),
          (B.minDistance = B.maxDistance = B.getDistance()),
          (i.x = w.clientX),
          (i.y = w.clientY),
          t(W)
      }
    } else if (p.touches.length === 2 && b) {
      console.warn('[TouchMove] 双指移动 - 在自定义控制模式下处理')
      const w = p.touches[0],
        k = p.touches[1],
        _ = w.clientX - k.clientX,
        L = w.clientY - k.clientY,
        B = Math.sqrt(_ * _ + L * L),
        Z = B - i.touchPrevDistance,
        le = (w.clientX + k.clientX) / 2,
        vt = (w.clientY + k.clientY) / 2,
        _o = (i.touchPrevX1 + i.touchPrevX2) / 2,
        Io = (i.touchPrevY1 + i.touchPrevY2) / 2,
        bs = le - _o,
        Cs = vt - Io,
        ln = C.cameraMoveSpeed !== void 0 ? C.cameraMoveSpeed : 0.05,
        dn = 0.1,
        Ms = Math.abs(Z),
        St = Math.abs(bs),
        bt = Math.abs(Cs)
      if (Ms > 1) {
        const ae = Ms * dn * ln
        Z > 0
          ? (t(Tt, ae), console.log('[TouchMove] 双指分开 - 向前移动, 速度:', ae))
          : (t(kt, ae), console.log('[TouchMove] 双指靠拢 - 向后移动, 速度:', ae))
      }
      if (St > 1 || bt > 1) {
        if (St > bt && St > 1) {
          const ae = St * dn * ln
          bs < 0
            ? (t(st, ae), console.log('[TouchMove] 双指右移 - 向右移动, 速度:', ae))
            : (t(nt, ae), console.log('[TouchMove] 双指左移 - 向左移动, 速度:', ae))
        } else if (bt > 1) {
          const ae = bt * dn * ln
          Cs < 0
            ? (t(Dt, ae), console.log('[TouchMove] 双指下移 - 向下移动, 速度:', ae))
            : (t(Et, ae), console.log('[TouchMove] 双指上移 - 向上移动, 速度:', ae))
        }
      }
      ;(i.touchPrevX1 = w.clientX),
        (i.touchPrevY1 = w.clientY),
        (i.touchPrevX2 = k.clientX),
        (i.touchPrevY2 = k.clientY),
        (i.touchPrevDistance = B),
        t(W)
    }
    r = Date.now()
  }
  function H(p) {
    if (a) return
    const b = t(I).useCustomControl === !0
    if (b) {
      const w = t(U)
      w.enabled = !1
    }
    i.down === 1 &&
      !i.move &&
      (b ? console.log('[TouchEnd] FPS模式下忽略点击选择') : t(lt, i.x, i.y)),
      p.touches.length === 0 ? ((i.down = 0), (i.move = !1)) : (i.down = p.touches.length),
      (r = Date.now())
  }
  window.addEventListener('keydown', l),
    window.addEventListener('keyup', h),
    window.addEventListener('blur', m),
    window.addEventListener('wheel', f, { passive: !1 }),
    n.addEventListener('contextmenu', A),
    n.addEventListener('mousedown', S),
    n.addEventListener('mousemove', P),
    n.addEventListener('mouseup', T),
    n.addEventListener('touchstart', D, { passive: !1 }),
    n.addEventListener('touchmove', F, { passive: !1 }),
    n.addEventListener('touchend', H, { passive: !1 }),
    window.addEventListener('resize', G),
    G()
  function G() {
    const { width: p, height: C, top: b, left: w } = t(We),
      k = t(O)
    ;(k.aspect = p / C), k.updateProjectionMatrix()
    const _ = t(as)
    _.setSize(p, C),
      (_.domElement.style.position = 'absolute'),
      (_.domElement.style.left = `${w}px`),
      (_.domElement.style.top = `${b}px`)
    const L = t(ft)
    L.setPixelRatio(Math.min(devicePixelRatio, 2)), L.setSize(p, C)
  }
  e(ts, () => {
    ;(a = !0),
      window.removeEventListener('keydown', l),
      window.removeEventListener('keyup', h),
      window.removeEventListener('blur', m),
      window.removeEventListener('wheel', f),
      n.removeEventListener('contextmenu', A),
      n.removeEventListener('mousedown', S),
      n.removeEventListener('mousemove', P),
      n.removeEventListener('mouseup', T),
      n.removeEventListener('touchstart', D),
      n.removeEventListener('touchmove', F),
      n.removeEventListener('touchend', H),
      window.removeEventListener('resize', G)
  })
}
function ko(s) {
  const e = new Ho(),
    t = 0.02,
    n = (a, i, r) => s.on(a, i, r),
    o = (a, ...i) => s.fire(a, ...i)
  n(jt, async (a, i) => {
    const { width: r, height: c, left: u, top: d } = o(We),
      l = new ee()
    ;(l.x = ((a - u) / r) * 2 - 1), (l.y = ((d - i) / c) * 2 + 1)
    const h = o(O)
    e.setFromCamera(l, h)
    const m = [],
      f = o(z),
      A = [],
      g = []
    f.traverse(function (S) {
      S instanceof he ? g.push(S) : S.isMesh && !S.ignoreIntersect && !S.isMark && A.push(S)
    })
    const y = e.intersectObjects(A, !0)
    for (let S = 0; S < y.length; S++) m.push(new ct(y[S].point, e.ray.distanceToPoint(y[S].point)))
    const E = h.projectionMatrix.clone().multiply(h.matrixWorldInverse)
    for (let S = 0; S < g.length; S++) {
      const P = g[S].fire(Rn)
      if (P)
        if (P.length !== void 0) {
          const T = P,
            D = T.length / 3
          for (let F = 0; F < D; F++) {
            const H = new x(T[3 * F + 0], T[3 * F + 1], T[3 * F + 2]),
              G = new Ce(H.x, H.y, H.z, 1).applyMatrix4(E),
              p = G.x / G.w,
              C = G.y / G.w
            Math.sqrt((p - l.x) ** 2 + (C - l.y) ** 2) <= t &&
              m.push(new ct(H, e.ray.distanceToPoint(H)))
          }
        } else
          for (let T of Object.keys(P)) {
            const D = T.split(','),
              F = new x(Number(D[0]), Number(D[1]), Number(D[2]))
            if (e.ray.distanceToPoint(F) <= 1.4143) {
              const H = P[T]
              for (let G = 0, p = H.length / 3; G < p; G++) {
                const C = new x(H[3 * G + 0], H[3 * G + 1], H[3 * G + 2]),
                  b = new Ce(C.x, C.y, C.z, 1).applyMatrix4(E),
                  w = b.x / b.w,
                  k = b.y / b.w
                Math.sqrt((w - l.x) ** 2 + (k - l.y) ** 2) <= t &&
                  m.push(new ct(C, e.ray.distanceToPoint(C)))
              }
            }
          }
    }
    m.sort((S, P) => S.radius - P.radius)
    const M = []
    for (let S = 0; S < m.length; S++) M.push(m[S].center)
    return M
  }),
    n(Bn, (a, i, r) => {
      const { width: c, height: u, left: d, top: l } = o(We),
        h = new ee()
      ;(h.x = ((a - d) / c) * 2 - 1), (h.y = ((l - i) / u) * 2 + 1)
      const m = o(O)
      return e.setFromCamera(h, m), e.ray.distanceToPoint(r)
    })
}
function Vr(s) {
  const e = (r, c, u) => s.on(r, c, u),
    t = (r, ...c) => s.fire(r, ...c)
  e(wt, () => t(O).fov),
    e(Qe, (r = !1) => (r ? t(O).position.clone() : t(O).position)),
    e(Ee, (r = !1) => (r ? t(U).target.clone() : t(U).target)),
    e(je, (r = !1) => (r ? t(O).up.clone() : t(O).up)),
    e(In, (r, c = !1) => {
      if ((t(qi, r), !c)) {
        t(U).target.copy(r), t(qt)
        return
      }
      const u = t(U).target.clone()
      let d = 0
      t(
        ut,
        () => {
          ;(d += 0.03), t(U).target.copy(u.clone().lerp(r, d)), t(qt)
        },
        () => d < 1,
      )
    }),
    e(Ii, () => {
      let r = t(Qe).toArray(),
        c = t(je).toArray(),
        u = t(Ee).toArray()
      return { position: r, lookUp: c, lookAt: u }
    }),
    e(Pi, () => t(U).update()),
    e(Li, () => t(U).updateRotateAxis())
  const n = 0.01
  let o = new x(),
    a = new x(),
    i = 0
  e(Wi, () => {
    const r = t(U).object,
      c = r.fov,
      u = r.position.clone(),
      d = r.getWorldDirection(new x())
    return Math.abs(i - c) < n &&
      Math.abs(u.x - o.x) < n &&
      Math.abs(u.y - o.y) < n &&
      Math.abs(u.z - o.z) < n &&
      Math.abs(d.x - a.x) < n &&
      Math.abs(d.y - a.y) < n &&
      Math.abs(d.z - a.z) < n
      ? !1
      : ((i = c), (o = u), (a = d), !0)
  })
}
class Gr extends se {
  constructor() {
    super(...arguments), (this.ignoreIntersect = !0)
  }
}
function Xr(s) {
  let e = !1
  const t = (r, c, u) => s.on(r, c, u),
    n = (r, ...c) => s.fire(r, ...c),
    o = [],
    a = new x(),
    i = new Te()
  t(ji, () => {
    const r = new Yo(0.5, 32, 32),
      c = Nr()
    ;(c.depthTest = !1), (c.depthWrite = !1), (c.transparent = !0)
    const u = new Gr()
    return (
      u.copy(new se(r, c)),
      t(ta, () => c),
      t(Hs, (d) => {
        e ||
          ((c.uniforms[Vn].value = d),
          d <= 0.01 ? (u.visible = !1) : (u.visible = !0),
          (c.uniformsNeedUpdate = !0),
          n(W))
      }),
      t(qi, (d) => {
        if (e) return
        const l = n(O)
        i.copy(l.matrixWorld).invert(),
          a.copy(d).applyMatrix4(i),
          a.normalize().multiplyScalar(10),
          a.applyMatrix4(l.matrixWorld)
        const { width: h, height: m } = n(We)
        c.uniforms[yt].value.set(h, m),
          u.position.copy(a),
          c.uniforms[So].value.copy(d),
          c.uniforms[yt].value.set(h, m),
          (c.uniforms[Vn].value = 1),
          (c.uniformsNeedUpdate = !0),
          n(Ys),
          n(W)
      }),
      t(na, () => {
        e || ((e = !0), c.dispose(), r.dispose())
      }),
      (u.renderOrder = 99999),
      u
    )
  }),
    t(Ys, () => {
      for (; o.length; ) o.pop().opacity = 0
      let r = { opacity: 1 }
      o.push(r),
        n(
          es,
          () => {
            !e &&
              r.opacity > 0 &&
              (r.opacity < 0.2
                ? (r.opacity = 0)
                : r.opacity > 0.6
                  ? (r.opacity = Math.max((r.opacity -= 0.01), 0))
                  : (r.opacity = Math.max((r.opacity -= 0.03), 0)),
              n(Hs, r.opacity))
          },
          () => !e && r.opacity > 0,
        )
    })
}
function Nr() {
  const s = {
    [Va]: { type: 'v3', value: new Jn() },
    [So]: { type: 'v3', value: new x() },
    [yt]: { type: 'v2', value: new ee() },
    [Vn]: { value: 0 },
  }
  return new Zn({
    uniforms: s,
    vertexShader: Wa(),
    fragmentShader: Ha(),
    transparent: !0,
    depthTest: !1,
    depthWrite: !1,
    side: Vo,
  })
}
function Eo(s) {
  const e = (i, r, c) => s.on(i, r, c),
    t = (i, ...r) => s.fire(i, ...r),
    n = /* @__PURE__ */ new Map(),
    o = document.createElement('div')
  o.classList.add('mark-warp'), document.body.appendChild(o)
  const a = new Wr()
  a.setSize(innerWidth, innerHeight),
    (a.domElement.style.position = 'absolute'),
    (a.domElement.style.top = '0px'),
    (a.domElement.style.pointerEvents = 'none'),
    o.appendChild(a.domElement),
    e(oa, () => o),
    e(as, () => a),
    e(rs, () => document.body.removeChild(o)),
    e(He, () => a.render(t(z), t(O)), !0),
    e(De, (i) => {
      const r = i?.getMarkData?.()?.name || i?.name
      r && n.set(r, new WeakRef(i))
    }),
    e(tt, (i) => {
      const r = i?.getMarkData?.()?.name || i?.name
      n.delete(r)
    }),
    e(dt, (i) => n.get(i)?.deref()),
    e(aa, (i, r) => {
      const c = t(dt, i)
      !c || !r || c.drawUpdate?.(r)
    }),
    e(ra, (i) => {
      const r = t(dt, i)
      return r ? r.getMarkData?.() : {}
    }),
    e(qe, (i) => {
      i !== void 0 && (t(I).markVisible = i),
        t(z).traverse((r) => r.isMark && (r.visible = t(I).markVisible)),
        t(W)
    }),
    e(Un, async () => {
      const i = []
      t(z).traverse((c) => {
        if (c.isMark) {
          const u = c.getMarkData?.()
          u && i.push(u)
        }
      })
      const r = t(ve).meta || {}
      return i.length ? (r.marks = i) : delete r.marks, (r.cameraInfo = t(Ii)), await t(Be, r)
    }),
    e(Vt, async () => {
      const i = []
      t(z).traverse((c) => {
        if (c.isMark) {
          const u = c.getMarkData?.()
          u && i.push(u)
        }
      })
      const r = t(ve).meta || {}
      return i.length ? (r.marks = i) : delete r.marks, await t(Be, r)
    }),
    e(to, async () => {
      const i = t(ve).meta || {}
      delete i.marks
      const r = await t(Be, i),
        c = []
      return n.forEach((u) => c.push(u)), c.forEach((u) => u.deref()?.dispose?.()), t(W), r
    }),
    e(no, async () => {
      t(z).traverse((r) => {
        r.isMark && r.getMarkData?.()
      })
      const i = t(ve).meta || {}
      return (i.watermark = t(Ji) || ''), await t(Be, i)
    }),
    e(zn, (i) => {
      i.meterScale &&
        ((t(I).meterScale = i.meterScale), t(ce, { scale: `1 : ${t(I).meterScale} m` })),
        t(U).updateByOptions({ ...i, ...(i.cameraInfo || {}) }),
        (i.marks || []).forEach((c) => {
          if (c.type === 'MarkSinglePoint') {
            const u = new xo(s, c)
            ;(u.visible = !1), t(z).add(u)
          } else if (c.type === 'MarkDistanceLine') {
            const u = new Gn(s)
            u.draw(c), (u.visible = !1), t(z).add(u)
          } else if (c.type === 'MarkMultiLines') {
            const u = new Xn(s)
            u.draw(c, !0), (u.visible = !1), t(z).add(u)
          } else if (c.type === 'MarkMultiPlans') {
            const u = new Nn(s)
            u.draw(c, !0), (u.visible = !1), t(z).add(u)
          } else if (c.type === 'MarkCirclePlan') {
            const u = new To(s)
            u.draw(c), (u.visible = !1), t(z).add(u)
          }
        }),
        t(us, i.flyPositions || []),
        t(hs, i.flyTargets || [])
    }),
    e(_e, () => {
      const i = t(I)
      ;(i.markMode = !1), t(is), t(W)
    }),
    e(eo, (i, r = !0) => {
      const c = i?.meterScale
      if (c) {
        if (typeof c != 'number' || c <= 0) {
          console.warn('meterScale is not a number or <= 0', i)
          return
        }
        r && (t(I).meterScale = i.meterScale), t(ce, { scale: `1 : ${c} m` })
        for (const u of n.values()) {
          const d = u.deref()
          d && (d instanceof Gn || d instanceof Xn || d instanceof Nn) && d.updateByMeterScale(c)
        }
      }
    }),
    e(Dn, (i) => {
      const r = new x().fromArray(i.slice(0, 3)),
        c = new x().fromArray(i.slice(-6, -3)),
        u = new x().fromArray(i.slice(-3)),
        d = r.distanceTo(u) < 1e-4,
        l = c.distanceTo(u) < 1e-4,
        h = new x(),
        m = d || l ? i.length / 3 - 1 : i.length / 3
      for (let f = 0; f < m; f++) h.add(new x(i[f * 3], i[f * 3 + 1], i[f * 3 + 2]))
      return h.divideScalar(m), h
    }),
    e(_n, (i) => {
      const r = []
      for (let l = 0, h = i.length / 3; l < h; l++)
        r.push(new x(i[l * 3], i[l * 3 + 1], i[l * 3 + 2]))
      const c = r[0].distanceTo(r[r.length - 1]) < 1e-4,
        u = r[r.length - 2].distanceTo(r[r.length - 1]) < 1e-4
      if (((c || u) && r.pop(), r.length < 3)) return 0
      let d = 0
      for (let l = 0, h = r.length - 2; l < h; l++)
        d += t(un, r[0], r[l + 1], r[l + 2], t(I).meterScale)
      return d
    }),
    e(Di, (i, r) => {
      let c = 0
      for (let u = 0, d = i.length - 2; u < d; u++) c += t(un, i[0], i[u + 1], i[u + 2], r)
      return c
    }),
    e(un, (i, r, c, u) => {
      const d = i.distanceTo(r) * u,
        l = r.distanceTo(c) * u,
        h = c.distanceTo(i) * u,
        m = (d + l + h) / 2
      return Math.sqrt(m * (m - d) * (m - l) * (m - h))
    })
}
function Kr(s) {
  const e = (h, ...m) => s.fire(h, ...m),
    t = (h, m, f) => s.on(h, m, f),
    n = [],
    o = []
  let a = !1,
    i = !1
  t(nn, () => (a = !1)),
    t(Xs, () => (a = !0)),
    t(Gs, () => n),
    t(ca, () => {
      const h = []
      for (let m = 0, f = n.length; m < f; m++) h.push(...n[m].toArray())
      return h
    }),
    t(la, () => {
      const h = []
      for (let m = 0, f = o.length; m < f; m++) h.push(...o[m].toArray())
      return h
    }),
    t(us, (h) => {
      for (let m = 0, f = (h.length / 3) | 0; m < f; m++)
        n[m] = new x(h[m * 3 + 0], h[m * 3 + 1], h[m * 3 + 2])
    }),
    t(hs, (h) => {
      for (let m = 0, f = (h.length / 3) | 0; m < f; m++)
        o[m] = new x(h[m * 3 + 0], h[m * 3 + 1], h[m * 3 + 2])
    }),
    t(ls, () => {
      const h = e(U)
      n.push(h.object.position.clone()), o.push(h.target.clone())
    }),
    t(ds, () => {
      ;(n.length = 0), (o.length = 0)
    }),
    t(io, async () => {
      const h = e(ve).meta || {}
      if (n.length) {
        const m = [],
          f = []
        for (let A = 0, g = n.length; A < g; A++)
          m.push(...n[A].toArray()), f.push(...o[A].toArray())
        ;(h.flyPositions = m), (h.flyTargets = f)
      } else delete h.flyPositions, delete h.flyTargets
      return await e(Be, h)
    }),
    t(On, () => {
      i || ((i = !0) && e(tn))
    })
  let r = 0
  const c = 120 * 1e3
  let u = 0,
    d,
    l
  t(tn, (h) => {
    if (((r = 0), (u = Date.now()), (d = null), (l = null), !n.length || (!h && !e(U).autoRotate)))
      return
    const m = e(U),
      f = [m.object.position.clone()],
      A = [m.target.clone()],
      g = e(Gs) || []
    for (let y = 0, E = Math.min(g.length, 100); y < E; y++)
      g[y] && f.push(g[y]), o[y] && A.push(o[y])
    ;(d = new Es(f)), (d.closed = !0), (l = new Es(A)), (l.closed = !0), e(Xs), e(te, !1)
  }),
    t(
      gt,
      () => {
        if ((Date.now() - u > c && e(nn), !a || !d || !l)) return
        const h = e(U)
        r = (Date.now() - u) / c
        const m = d.getPoint(r),
          f = l.getPoint(r)
        h.object.position.set(m.x, m.y, m.z), h.target.set(f.x, f.y, f.z)
      },
      !0,
    )
}
class oc {
  constructor(e = {}) {
    ;(this.disposed = !1),
      (this.updateTime = 0),
      (this.needUpdate = !0),
      console.info('Reall3dViewer', rn),
      this.init(hi(e)),
      !e.disableDropLocalFile && this.enableDropLocalFile()
  }
  init(e) {
    const t = this
    ;(e.position = e.position ? [...e.position] : [0, -5, 15]),
      (e.lookAt = e.lookAt ? [...e.lookAt] : [0, 0, 0]),
      (e.lookUp = e.lookUp ? [...e.lookUp] : [0, -1, 0])
    const n = fr(e),
      o = (e.scene = e.scene || new Ti())
    ;(o.background = new Jn(e.background)), gr(e)
    const a = (e.controls = new Lr(e))
    a.updateByOptions(e)
    const i = new fs()
    ;(e.viewerEvents = i), (t.events = i)
    const r = (l, h, m) => i.on(l, h, m),
      c = (l, ...h) => i.fire(l, ...h)
    r(I, () => e),
      r(et, () => n.domElement),
      r(ft, () => n),
      r(z, () => o),
      r(U, () => a),
      r(O, () => a.object),
      r(ne, () => e.bigSceneMode),
      r(ia, (l) => (e.pointcloudMode = l))
    const u = []
    r(W, () => {
      for (t.needUpdate = !0; u.length; ) u.pop().stop = !0
      let l = { count: 0, stop: !1 }
      u.push(l),
        c(
          es,
          () => {
            !t.disposed && (t.needUpdate = !0), l.count++ >= 600 && (l.stop = !0)
          },
          () => !t.disposed && (c(os) || !l.stop),
          10,
        )
    }),
      ws(i),
      hr(i),
      vs(i),
      Vr(i),
      Eo(i),
      Yr(i),
      ko(i),
      Xr(i),
      Kr(i),
      (t.splatMesh = new he(mr(e))),
      r(ve, () => t.splatMesh),
      o.add(t.splatMesh),
      wr(i),
      o.add(new ki('#ffffff', 2)),
      o.add(c(ji)),
      n.setAnimationLoop(t.update.bind(t)),
      r(Vs, () => {
        c(I).useCustomControl === !0 || a.update(), !t.needUpdate && c(Wi) && c(W)
      }),
      r($e, () => c(an), !0),
      r($e, () => c(Vs), !0),
      r(
        He,
        () => {
          try {
            !(t.needUpdate = !1) && n.render(o, c(O))
          } catch (l) {
            console.warn(l.message)
          }
        },
        !0,
      ),
      r(gt, () => {}, !0),
      r(on, () => t.dispose()),
      r(oo, () => console.info(JSON.stringify(c(ve).meta || {}, null, 2)))
    let d = ''
    r(xt, (l = '') => {
      ;(d = l), t.splatMesh.fire(cs, d, !0)
    }),
      r(Ji, () => d),
      c(ce, { scale: `1 : ${c(I).meterScale} m` }),
      t.initGsApi()
  }
  /**
   * 允许拖拽本地文件进行渲染
   */
  enableDropLocalFile() {
    const e = this
    document.addEventListener('dragover', function (t) {
      t.preventDefault(), t.stopPropagation()
    }),
      document.addEventListener('drop', async function (t) {
        t.preventDefault(), t.stopPropagation()
        let n = t.dataTransfer.files[0]
        if (!n) return
        let o
        if (n.name.endsWith('.spx')) o = 'spx'
        else if (n.name.endsWith('.splat')) o = 'splat'
        else if (n.name.endsWith('.ply')) o = 'ply'
        else if (n.name.endsWith('.spz')) o = 'spz'
        else return console.error('unsupported format:', n.name)
        const a = URL.createObjectURL(n),
          i = e.events.fire(I)
        ;(i.bigSceneMode = !1),
          (i.pointcloudMode = !0),
          (i.autoRotate = !0),
          (i.debugMode = !0),
          e.reset(i),
          setTimeout(async () => {
            await e.addModel({ url: a, format: o }), URL.revokeObjectURL(a)
          })
      })
  }
  /**
   * 刷新
   */
  update() {
    const e = this
    if (e.disposed) return
    const t = (n, ...o) => e.events.fire(n, ...o)
    Date.now() - e.updateTime > 30 && (t($e), e.needUpdate && t(He), t(gt))
  }
  // 开发调试用临时接口
  fire(e, t, n) {
    const o = this
    e === 1 && o.splatMesh.fire(Pn, t),
      e === 2 && o.events.fire(ls),
      e === 3 && o.events.fire(tn, !0),
      e === 4 && o.events.fire(ds),
      e === 5 && o.events.fire(io),
      e === 6 && o.events.fire(Vt),
      e === 7 && o.events.fire(to),
      e === 8 &&
        (async () => {
          let a = await o.splatMesh.fire(ps)
          t && (a = o.splatMesh.fire(lo) + t), o.splatMesh.fire(ns, a)
        })()
  }
  /**
   * 设定或者获取最新配置项
   * @param opts 配置项
   * @returns 最新配置项
   */
  options(e) {
    const t = this
    if (t.disposed) return {}
    const n = (r, ...c) => t.events.fire(r, ...c)
    let o
    return (
      n(z).traverse((r) => !o && r instanceof he && (o = r.options())),
      e &&
        (e.autoRotate !== void 0 &&
          ((n(U).autoRotate = e.autoRotate), (n(I).autoRotate = e.autoRotate)),
        e.pointcloudMode !== void 0 && n(Jt, e.pointcloudMode),
        e.lightFactor !== void 0 && n(pt, e.lightFactor),
        e.maxRenderCountOfMobile && (n(I).maxRenderCountOfMobile = e.maxRenderCountOfMobile),
        e.maxRenderCountOfPc && (n(I).maxRenderCountOfPc = e.maxRenderCountOfPc),
        e.debugMode !== void 0 && (n(I).debugMode = e.debugMode),
        e.cameraMoveSpeed !== void 0 && (n(I).cameraMoveSpeed = e.cameraMoveSpeed),
        e.useCustomControl !== void 0 && (n(I).useCustomControl = e.useCustomControl),
        e.useCustomControl !== void 0 && n(U).updateControlMode(n(I)),
        e.markType !== void 0 && (n(I).markType = e.markType),
        e.markVisible !== void 0 && n(qe, e.markVisible),
        e.meterScale !== void 0 && (n(I).meterScale = e.meterScale),
        e.markMode !== void 0 &&
          ((n(I).markMode = e.markMode),
          !e.markMode && n(is),
          (n(U).autoRotate = n(I).autoRotate = !1)),
        e.disableTransitionEffect !== void 0 &&
          (n(I).disableTransitionEffect = e.disableTransitionEffect)),
      n(U).updateByOptions(e),
      n(ce, { scale: `1 : ${n(I).meterScale} m` }),
      Object.assign({ ...n(I) }, o)
    )
  }
  /**
   * 重置
   */
  reset(e = {}) {
    const t = this
    t.dispose(), (t.disposed = !1), t.init(hi(e))
  }
  /**
   * 光圈过渡切换显示
   * @returns
   */
  switchDeiplayMode() {
    const e = this
    e.disposed || e.events.fire(en)
  }
  /**
   * 添加场景
   * @param sceneUrl 场景地址
   */
  async addScene(e) {
    const t = this
    if (t.disposed) return
    const n = (i, ...r) => t.events.fire(i, ...r)
    let o = {}
    try {
      const i = await fetch(e, { mode: 'cors', credentials: 'omit', cache: 'reload' })
      if (i.status === 200) o = await i.json()
      else return console.error('scene file fetch failed, status:', i.status)
    } catch (i) {
      return console.error('scene file fetch failed', i.message)
    }
    if (!o.url) return console.error('missing model file url')
    if (!o.url.endsWith('.bin') && !o.url.endsWith('.spx'))
      return console.error('The format is unsupported in the large scene mode', o.url)
    const a = { ...o, ...(o.cameraInfo || {}) }
    ;(o.autoCut = Math.min(Math.max(o.autoCut || 0, 0), 50)),
      (a.bigSceneMode = o.autoCut > 1),
      t.reset({ ...a }),
      !a.bigSceneMode && delete o.autoCut,
      (t.splatMesh.meta = o),
      pe && (o.cameraInfo?.position || o.cameraInfo?.lookAt) && t.events.fire(U)._dollyOut(0.75),
      a.bigSceneMode ? (n(us, o.flyPositions || []), n(hs, o.flyTargets || [])) : n(zn, o),
      await t.splatMesh.addModel({ url: o.url }, o),
      await n(xt, o.watermark),
      n(U).updateRotateAxis()
  }
  /**
   * 添加要渲染的高斯模型（小场景模式）
   * @param urlOpts 高斯模型链接或元数据文件链接或选项
   */
  async addModel(e) {
    const t = this
    if (t.disposed) return
    const n = (c, ...u) => t.events.fire(c, ...u)
    let o = '',
      a = { url: '' }
    if (
      (Object.prototype.toString.call(e) === '[object String]'
        ? e.endsWith('.meta.json')
          ? (o = e)
          : (a.url = e)
        : (a = e),
      !a.url && !o)
    )
      return console.error('model url is empty')
    const i = n(I)
    i.bigSceneMode = !1
    let r = {}
    if (!a.url.startsWith('blob:'))
      try {
        o = o || a.url.substring(0, a.url.lastIndexOf('.')) + '.meta.json'
        const c = await fetch(o, { mode: 'cors', credentials: 'omit', cache: 'reload' })
        c.status === 200
          ? (r = await c.json())
          : console.warn('meta file fetch failed, status:', c.status)
      } catch (c) {
        console.warn('meta file fetch failed', c.message, a.url)
      }
    if (
      ((r.showWatermark = r.showWatermark !== !1),
      (r.url = r.url || a.url),
      delete r.autoCut,
      !a.format)
    )
      if (((a.url = a.url || r.url), a.url.endsWith('.spx'))) a.format = 'spx'
      else if (a.url.endsWith('.splat')) a.format = 'splat'
      else if (a.url.endsWith('.ply')) a.format = 'ply'
      else if (a.url.endsWith('.spz')) a.format = 'spz'
      else {
        console.error('unknow format!', a.url)
        return
      }
    n(zn, r), await t.splatMesh.addModel(a, r), await n(xt, r.watermark)
  }
  /**
   * 根据需要暴露的接口
   */
  initGsApi() {
    const e = (d, ...l) => this.events.fire(d, ...l),
      t = () => {
        setTimeout(() => window.focus()),
          e(nn),
          (e(U).autoRotate = e(I).autoRotate = !e(I).autoRotate)
      },
      n = (d = !0) => {
        setTimeout(() => window.focus()), (d = !!d)
        const l = e(I)
        l.pointcloudMode !== d && (l.bigSceneMode ? e(Jt, d) : e(en))
      },
      o = (d = !0) => {
        setTimeout(() => window.focus()), e(qe, !!d)
      },
      a = (d) => {
        setTimeout(() => window.focus())
        const l = e(I)
        if (l.markMode) return !1
        if (d === 1) l.markType = 'point'
        else if (d === 2) l.markType = 'lines'
        else if (d === 3) l.markType = 'plans'
        else if (d === 4) l.markType = 'distance'
        else return !1
        return (l.markMode = !0), e(te), !0
      },
      i = async (d) => (
        setTimeout(() => window.focus()), d ? (e(dt, d)?.dispose(), e(W), await e(Vt)) : !1
      ),
      r = async (d, l = !0) => (
        e(eo, d, l),
        e(dt, d?.name)?.drawUpdate?.(d, l),
        e(W),
        l ? (setTimeout(() => window.focus()), await e(Vt)) : !0
      ),
      c = (d = !0) => {
        setTimeout(() => window.focus()), e(Pn, !!d)
      },
      u = (d, l = !0) => {
        e(xt, d), l && e(no, d)
      }
    window.$api = {
      switchAutoRotate: t,
      changePointCloudMode: n,
      showMark: o,
      startMark: a,
      deleteMark: i,
      updateMark: r,
      showWaterMark: c,
      setWaterMark: u,
    }
  }
  /**
   * 销毁渲染器不再使用
   */
  dispose() {
    const e = this
    if (e.disposed) return
    e.disposed = !0
    const t = (a, ...i) => e.events.fire(a, ...i),
      n = t(ft),
      o = n.domElement
    t(ss),
      t(Qi),
      t(rs),
      t(ts),
      t(U).dispose(),
      t(Ie, t(z)),
      n.clear(),
      n.dispose(),
      o.parentElement.removeChild(o),
      (e.splatMesh = null),
      e.events.clear(),
      (e.events = null)
  }
  /**
   * 启用或禁用第一人称模式
   *
   * 第一人称模式提供类似FPS游戏的控制体验：
   * - 使用WASD键控制相机前后左右移动
   * - 使用箭头键控制相机上下移动
   * - 使用鼠标左键拖动来旋转视角
   *
   * @example
   * // 启用第一人称模式
   * viewer.enableFirstPersonMode(true);
   *
   * // 启用第一人称模式并设置较快的移动速度
   * viewer.enableFirstPersonMode(true, 0.01);
   *
   * // 禁用第一人称模式，恢复默认控制
   * viewer.enableFirstPersonMode(false);
   *
   * @param enable 是否启用第一人称模式，默认为true
   * @param cameraMoveSpeed 相机移动速度，值越大移动越快，默认为0.005
   * @returns 当前的ViewerOptions
   */
  enableFirstPersonMode(e = !0, t) {
    console.log('[enableFirstPersonMode] 开始设置第一人称模式, enable:', e)
    const n = this,
      o = (l, ...h) => n.events.fire(l, ...h),
      a = o(O),
      i = o(U),
      r = a.position.clone()
    console.log('[enableFirstPersonMode] 启用前相机位置:', r.toArray())
    const c = new x()
    a.getWorldDirection(c)
    const u = {
      useCustomControl: e,
    }
    t !== void 0 && (u.cameraMoveSpeed = t)
    const d = this.options(u)
    if (e) {
      a.position.copy(r)
      const l = r.clone().add(c)
      i.target.copy(l)
      const h = i.getDistance()
      ;(i.minDistance = h),
        (i.maxDistance = h),
        i.updateControlMode(o(I)),
        o(W),
        console.log('[enableFirstPersonMode] 重置后相机位置:', a.position.toArray())
    }
    return console.log('[enableFirstPersonMode] 启用后相机位置:', a.position.toArray()), d
  }
}
function $r(s) {
  const e = (i, r, c) => s.on(i, r, c),
    t = (i, ...r) => s.fire(i, ...r),
    n = pe ? 1 : 20,
    o = /* @__PURE__ */ new Map(),
    a = /* @__PURE__ */ new Map()
  e(Nt, () => t(de) && o.set(Date.now(), 1)),
    e(Kt, () => t(de) && t(Ke, o)),
    e($t, () => t(de) && a.set(Date.now(), 1)),
    e(Zt, () => t(de) && t(Ke, a)),
    e(Ke, (i) => {
      let r = [],
        c = Date.now(),
        u = 0
      for (const d of i.keys()) c - d <= 1e3 ? u++ : r.push(d)
      return r.forEach((d) => i.delete(d)), Math.min(u, 30)
    }),
    e($, () => {
      const i = t(z),
        r = t(O),
        c = []
      return (
        i?.traverse(function (u) {
          u.isWarpSplatMesh && u.splatMesh?.visible && c.push(u)
        }),
        c.sort((u, d) => r.position.distanceTo(u.position) - r.position.distanceTo(d.position)),
        (window.splat = c[0]?.splatMesh),
        c[0]?.splatMesh
      )
    }),
    e(yo, () => {
      const i = t(z),
        r = t(O),
        c = []
      i?.traverse(function (u) {
        u.isWarpSplatMesh && c.push(u)
      }),
        c.sort((u, d) => r.position.distanceTo(u.position) - r.position.distanceTo(d.position))
      for (let u = 0; u < c.length; u++)
        (c[u].active = u < n), c[u].splatMesh && (c[u].splatMesh.renderOrder = 1e3 - u)
    }),
    e(Ao, () => {
      const i = t(z),
        r = []
      i?.traverse((c) => r.push(c)),
        r.forEach((c) => {
          c.dispose
            ? c.dispose()
            : (c.geometry?.dispose?.(),
              c.material && c.material instanceof Array
                ? c.material.forEach((u) => u?.dispose?.())
                : c.material?.dispose?.())
        }),
        i?.clear()
    }),
    e(Qn, (i = 0.1) => {
      const r = t($)
      r && r.visible && r.rotateOnAxis(new x(1, 0, 0), xe.degToRad(i))
    }),
    e(Wn, (i = 0.1) => {
      const r = t($)
      r && r.visible && r.rotateOnAxis(new x(0, 1, 0), xe.degToRad(i))
    }),
    e(Hn, (i = 0.1) => {
      const r = t($)
      r && r.visible && r.rotateOnAxis(new x(0, 0, 1), xe.degToRad(i))
    }),
    e(ua, (i = 0.01) => {
      const r = t($)
      r && r.visible && (r.position.x += i)
    }),
    e(mo, (i = 0.01) => {
      const r = t($)
      r && r.visible && (r.position.y += i)
    }),
    e(ha, (i = 0.01) => {
      const r = t($)
      r && r.visible && (r.position.z += i)
    }),
    e(pa, (i) => {
      const r = t($)
      r && r.visible && i && r.position.copy(i), console.info(r, i)
    }),
    e(fa, (i = 0.01) => {
      const r = t($)
      r && r.visible && r.scale.set(r.scale.x + i, r.scale.y + i, r.scale.z + i)
    }),
    e(ga, () => {
      const i = t($)
      i && (i.visible = !i.visible)
    }),
    e(ma, async (i) => {
      if ((!i && (i = t($)), !i)) return
      const r = i.meta || {}
      return (r.transform = i.matrix.toArray()), await t(Be, JSON.stringify(r), i.meta.url)
    }),
    e(ho, () => {
      const i = t(I).root,
        r = new Ci({
          antialias: !1,
          logarithmicDepthBuffer: !0,
          stencil: !0,
          alpha: !0,
          precision: 'highp',
        })
      return (
        r.setSize(i.clientWidth, i.clientHeight),
        r.setPixelRatio(Math.min(devicePixelRatio, 2)),
        e(ft, () => r),
        e(et, () => r.domElement),
        r
      )
    }),
    e(po, () => {
      const i = new Ti(),
        r = 14414079
      return (i.background = new Jn(r)), (i.fog = new Ds(r, 0)), e(z, () => i), i
    }),
    e(fo, () => {
      const r = t(O),
        c = t(z),
        u = t(I),
        d = new Fr(t(O), u.root)
      return (
        (d.screenSpacePanning = !1),
        (d.minDistance = 0.1),
        (d.maxDistance = 1e5),
        (d.maxPolarAngle = 1.2),
        (d.enableDamping = !0),
        (d.dampingFactor = 0.07),
        (d.zoomToCursor = !0),
        d.addEventListener('change', () => {
          const l = Math.max(d.getPolarAngle(), 0.1),
            h = Math.max(d.getDistance(), 0.1)
          ;(d.zoomSpeed = Math.max(Math.log(h), 0) + 0.5),
            (r.far = xe.clamp((h / l) * 8, 100, 2e5)),
            (r.near = r.far / 1e3),
            r.updateProjectionMatrix(),
            c.fog instanceof Ds && (c.fog.density = (l / (h + 5)) * 1 * 0.25),
            (d.maxPolarAngle = Math.min(Math.pow(1e4 / h, 4), 1.2))
        }),
        e(U, () => d),
        e(wt, () => r.fov),
        e(Qe, (l = !1) => (l ? r.position.clone() : r.position)),
        e(Ee, (l = !1) => (l ? d.target.clone() : d.target)),
        e(je, (l = !1) => (l ? r.up.clone() : r.up)),
        d
      )
    }),
    e(go, () => {
      const i = new Go(16777215, 1)
      return i.position.set(0, 2e3, 1e3), i
    }),
    window.addEventListener('beforeunload', () => t(on))
}
function Zr(s) {
  let { root: e = '#map', debugMode: t } = s
  e
    ? (e = typeof e == 'string' ? document.querySelector(e) || document.querySelector('#map') : e)
    : (e = document.querySelector('#map')),
    e ||
      ((e = document.createElement('div')),
      (e.id = 'map'),
      (document.querySelector('#gsviewer') || document.querySelector('body')).appendChild(e))
  const n = { ...s }
  return (n.root = e), n
}
function jr() {
  const s = _s.TileSource.create({
      dataType: 'image',
      attribution: 'ArcGIS',
      url: 'https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    }),
    e = new _s.TileMap({ imgSource: s, lon0: 90, minLevel: 2, maxLevel: 16 })
  return e.scale.set(10, 10, 10), e.rotateX(-Math.PI / 2), (e.autoUpdate = !1), e
}
class qr {
  constructor() {
    ;(this.down = 0),
      (this.move = !1),
      (this.downTime = 0),
      (this.isDbClick = !1),
      (this.x = 0),
      (this.y = 0),
      (this.lastClickX = 0),
      (this.lastClickY = 0),
      (this.lastClickPointTime = 0),
      (this.lastMovePoint = null),
      (this.lastMovePointTime = 0)
  }
}
function Jr(s) {
  const e = (T, D, F) => s.on(T, D, F),
    t = (T, ...D) => s.fire(T, ...D),
    n = t(et)
  let o = /* @__PURE__ */ new Set(),
    a,
    i = new qr()
  e(an, () => {
    if (!o.size) return
    if (!t(I).enableKeyboard) return o.clear()
    if (o.has('KeyH')) {
      const D = t($)
      D && (D.visible = !D.visible), o.clear()
    } else if (o.has('Equal')) {
      const D = t($)
      if (!D || !D.visible) return o.clear()
      if (o.has('KeyX')) {
        const F = xe.degToRad(0.1)
        D.rotateOnAxis(new x(1, 0, 0), F)
      } else D.scale.set(D.scale.x + 0.01, D.scale.y + 0.01, D.scale.z + 0.01)
    } else if (o.has('Minus')) {
      const D = t($)
      if (!D || !D.visible) return o.clear()
      if (o.has('KeyX')) {
        const F = xe.degToRad(-0.1)
        D.rotateOnAxis(new x(1, 0, 0), F)
      } else D.scale.set(D.scale.x - 0.01, D.scale.y - 0.01, D.scale.z - 0.01)
    } else if (o.has('ArrowUp')) {
      const D = t($)
      D && D.visible && (D.position.z += 0.1)
    } else if (o.has('ArrowDown')) {
      const D = t($)
      D && D.visible && (D.position.z -= 0.1)
    } else if (o.has('ArrowRight')) {
      const D = t($)
      D && D.visible && (D.position.x += 0.1)
    } else if (o.has('ArrowLeft')) {
      const D = t($)
      D && D.visible && (D.position.x -= 0.1)
    } else if (o.has('KeyU')) {
      const D = t($)
      if (D) {
        const F = D.meta || {}
        F.transform = D.matrix.toArray()
      }
    } else
      o.has('KeyQ')
        ? t(Qn, 0.1)
        : o.has('KeyW')
          ? t(Wn, 0.1)
          : o.has('KeyE')
            ? t(Hn, 0.1)
            : o.has('KeyA')
              ? t(Qn, -0.1)
              : o.has('KeyS')
                ? t(Wn, -0.1)
                : o.has('KeyD')
                  ? t(Hn, -0.1)
                  : o.has('KeyY')
                    ? t(mo, o.has('ShiftLeft') || o.has('ShiftRight') ? -0.1 : 0.1)
                    : o.has('KeyC') &&
                      console.info('position=', t(Qe).toArray(), 'lookat=', t(Ee).toArray())
  })
  const r = (T) => {
      console.info(T.code),
        T.target.type !== 'text' && (a || T.code === 'F5' || (T.preventDefault(), o.add(T.code)))
    },
    c = (T) => {
      T.target.type !== 'text' && (a || o.clear())
    },
    u = () => {
      o.clear()
    },
    d = (T) => {
      parent && setTimeout(() => window.focus()), T.preventDefault()
    },
    l = async (T) => {
      T.preventDefault()
    }
  let h, m, f, A
  e(ot, () => {
    h?.dispose(),
      m?.dispose(),
      f?.dispose(),
      A?.dispose(),
      (h = null),
      (m = null),
      (f = null),
      (A = null),
      (i.lastMovePoint = null),
      t(_e)
  })
  const g = async (T) => {
      parent && setTimeout(() => window.focus()),
        T.preventDefault(),
        !a &&
          ((i.down = T.button === 2 ? 2 : 1),
          (i.move = !1),
          (i.isDbClick = Date.now() - i.downTime < 300),
          (i.downTime = Date.now()))
    },
    y = async (T) => {
      T.preventDefault(), !a && i.down && (i.move = !0)
    },
    E = async (T) => {
      T.preventDefault(), !a && ((i.down = 0), (i.move = !1))
    }
  function M(T) {
    T.preventDefault(),
      !a &&
        ((i.down = T.touches.length),
        i.down === 1 && ((i.move = !1), (i.x = T.touches[0].clientX), (i.y = T.touches[0].clientY)))
  }
  function S(T) {
    T.touches.length === 1 && (i.move = !0)
  }
  function P(T) {
    i.down === 1 && !i.move && t(lt, i.x, i.y)
  }
  window.addEventListener('keydown', r),
    window.addEventListener('keyup', c),
    window.addEventListener('blur', u),
    window.addEventListener('wheel', d, { passive: !1 }),
    n.addEventListener('contextmenu', l),
    n.addEventListener('mousedown', g),
    n.addEventListener('mousemove', y),
    n.addEventListener('mouseup', E),
    n.addEventListener('touchstart', M, { passive: !1 }),
    n.addEventListener('touchmove', S, { passive: !1 }),
    n.addEventListener('touchend', P, { passive: !1 }),
    e(ts, () => {
      ;(a = !0),
        window.removeEventListener('keydown', r),
        window.removeEventListener('keyup', c),
        window.removeEventListener('blur', u),
        window.removeEventListener('wheel', d),
        n.removeEventListener('contextmenu', l),
        n.removeEventListener('mousedown', g),
        n.removeEventListener('mousemove', y),
        n.removeEventListener('mouseup', E),
        n.removeEventListener('touchstart', M),
        n.removeEventListener('touchmove', S),
        n.removeEventListener('touchend', P)
    }),
    e(lt, async (T, D) => await t(jt, T, D))
}
var Ze = Object.freeze({
    Linear: Object.freeze({
      None: function (s) {
        return s
      },
      In: function (s) {
        return s
      },
      Out: function (s) {
        return s
      },
      InOut: function (s) {
        return s
      },
    }),
    Quadratic: Object.freeze({
      In: function (s) {
        return s * s
      },
      Out: function (s) {
        return s * (2 - s)
      },
      InOut: function (s) {
        return (s *= 2) < 1 ? 0.5 * s * s : -0.5 * (--s * (s - 2) - 1)
      },
    }),
    Cubic: Object.freeze({
      In: function (s) {
        return s * s * s
      },
      Out: function (s) {
        return --s * s * s + 1
      },
      InOut: function (s) {
        return (s *= 2) < 1 ? 0.5 * s * s * s : 0.5 * ((s -= 2) * s * s + 2)
      },
    }),
    Quartic: Object.freeze({
      In: function (s) {
        return s * s * s * s
      },
      Out: function (s) {
        return 1 - --s * s * s * s
      },
      InOut: function (s) {
        return (s *= 2) < 1 ? 0.5 * s * s * s * s : -0.5 * ((s -= 2) * s * s * s - 2)
      },
    }),
    Quintic: Object.freeze({
      In: function (s) {
        return s * s * s * s * s
      },
      Out: function (s) {
        return --s * s * s * s * s + 1
      },
      InOut: function (s) {
        return (s *= 2) < 1 ? 0.5 * s * s * s * s * s : 0.5 * ((s -= 2) * s * s * s * s + 2)
      },
    }),
    Sinusoidal: Object.freeze({
      In: function (s) {
        return 1 - Math.sin(((1 - s) * Math.PI) / 2)
      },
      Out: function (s) {
        return Math.sin((s * Math.PI) / 2)
      },
      InOut: function (s) {
        return 0.5 * (1 - Math.sin(Math.PI * (0.5 - s)))
      },
    }),
    Exponential: Object.freeze({
      In: function (s) {
        return s === 0 ? 0 : Math.pow(1024, s - 1)
      },
      Out: function (s) {
        return s === 1 ? 1 : 1 - Math.pow(2, -10 * s)
      },
      InOut: function (s) {
        return s === 0
          ? 0
          : s === 1
            ? 1
            : (s *= 2) < 1
              ? 0.5 * Math.pow(1024, s - 1)
              : 0.5 * (-Math.pow(2, -10 * (s - 1)) + 2)
      },
    }),
    Circular: Object.freeze({
      In: function (s) {
        return 1 - Math.sqrt(1 - s * s)
      },
      Out: function (s) {
        return Math.sqrt(1 - --s * s)
      },
      InOut: function (s) {
        return (s *= 2) < 1
          ? -0.5 * (Math.sqrt(1 - s * s) - 1)
          : 0.5 * (Math.sqrt(1 - (s -= 2) * s) + 1)
      },
    }),
    Elastic: Object.freeze({
      In: function (s) {
        return s === 0
          ? 0
          : s === 1
            ? 1
            : -Math.pow(2, 10 * (s - 1)) * Math.sin((s - 1.1) * 5 * Math.PI)
      },
      Out: function (s) {
        return s === 0
          ? 0
          : s === 1
            ? 1
            : Math.pow(2, -10 * s) * Math.sin((s - 0.1) * 5 * Math.PI) + 1
      },
      InOut: function (s) {
        return s === 0
          ? 0
          : s === 1
            ? 1
            : ((s *= 2),
              s < 1
                ? -0.5 * Math.pow(2, 10 * (s - 1)) * Math.sin((s - 1.1) * 5 * Math.PI)
                : 0.5 * Math.pow(2, -10 * (s - 1)) * Math.sin((s - 1.1) * 5 * Math.PI) + 1)
      },
    }),
    Back: Object.freeze({
      In: function (s) {
        var e = 1.70158
        return s === 1 ? 1 : s * s * ((e + 1) * s - e)
      },
      Out: function (s) {
        var e = 1.70158
        return s === 0 ? 0 : --s * s * ((e + 1) * s + e) + 1
      },
      InOut: function (s) {
        var e = 2.5949095
        return (s *= 2) < 1
          ? 0.5 * (s * s * ((e + 1) * s - e))
          : 0.5 * ((s -= 2) * s * ((e + 1) * s + e) + 2)
      },
    }),
    Bounce: Object.freeze({
      In: function (s) {
        return 1 - Ze.Bounce.Out(1 - s)
      },
      Out: function (s) {
        return s < 1 / 2.75
          ? 7.5625 * s * s
          : s < 2 / 2.75
            ? 7.5625 * (s -= 1.5 / 2.75) * s + 0.75
            : s < 2.5 / 2.75
              ? 7.5625 * (s -= 2.25 / 2.75) * s + 0.9375
              : 7.5625 * (s -= 2.625 / 2.75) * s + 0.984375
      },
      InOut: function (s) {
        return s < 0.5 ? Ze.Bounce.In(s * 2) * 0.5 : Ze.Bounce.Out(s * 2 - 1) * 0.5 + 0.5
      },
    }),
    generatePow: function (s) {
      return (
        s === void 0 && (s = 4),
        (s = s < Number.EPSILON ? Number.EPSILON : s),
        (s = s > 1e4 ? 1e4 : s),
        {
          In: function (e) {
            return Math.pow(e, s)
          },
          Out: function (e) {
            return 1 - Math.pow(1 - e, s)
          },
          InOut: function (e) {
            return e < 0.5 ? Math.pow(e * 2, s) / 2 : (1 - Math.pow(2 - e * 2, s)) / 2 + 0.5
          },
        }
      )
    },
  }),
  rt = function () {
    return performance.now()
  },
  ec =
    /** @class */
    (function () {
      function s() {
        for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t]
        ;(this._tweens = {}), (this._tweensAddedDuringUpdate = {}), this.add.apply(this, e)
      }
      return (
        (s.prototype.getAll = function () {
          var e = this
          return Object.keys(this._tweens).map(function (t) {
            return e._tweens[t]
          })
        }),
        (s.prototype.removeAll = function () {
          this._tweens = {}
        }),
        (s.prototype.add = function () {
          for (var e, t = [], n = 0; n < arguments.length; n++) t[n] = arguments[n]
          for (var o = 0, a = t; o < a.length; o++) {
            var i = a[o]
            ;(e = i._group) === null || e === void 0 || e.remove(i),
              (i._group = this),
              (this._tweens[i.getId()] = i),
              (this._tweensAddedDuringUpdate[i.getId()] = i)
          }
        }),
        (s.prototype.remove = function () {
          for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t]
          for (var n = 0, o = e; n < o.length; n++) {
            var a = o[n]
            ;(a._group = void 0),
              delete this._tweens[a.getId()],
              delete this._tweensAddedDuringUpdate[a.getId()]
          }
        }),
        (s.prototype.allStopped = function () {
          return this.getAll().every(function (e) {
            return !e.isPlaying()
          })
        }),
        (s.prototype.update = function (e, t) {
          e === void 0 && (e = rt()), t === void 0 && (t = !0)
          var n = Object.keys(this._tweens)
          if (n.length !== 0)
            for (; n.length > 0; ) {
              this._tweensAddedDuringUpdate = {}
              for (var o = 0; o < n.length; o++) {
                var a = this._tweens[n[o]],
                  i = !t
                a && a.update(e, i) === !1 && !t && this.remove(a)
              }
              n = Object.keys(this._tweensAddedDuringUpdate)
            }
        }),
        s
      )
    })(),
  Kn = {
    Linear: function (s, e) {
      var t = s.length - 1,
        n = t * e,
        o = Math.floor(n),
        a = Kn.Utils.Linear
      return e < 0
        ? a(s[0], s[1], n)
        : e > 1
          ? a(s[t], s[t - 1], t - n)
          : a(s[o], s[o + 1 > t ? t : o + 1], n - o)
    },
    Utils: {
      Linear: function (s, e, t) {
        return (e - s) * t + s
      },
    },
  },
  Do =
    /** @class */
    (function () {
      function s() {}
      return (
        (s.nextId = function () {
          return s._nextId++
        }),
        (s._nextId = 0),
        s
      )
    })(),
  $n = new ec(),
  tc =
    /** @class */
    (function () {
      function s(e, t) {
        ;(this._isPaused = !1),
          (this._pauseStart = 0),
          (this._valuesStart = {}),
          (this._valuesEnd = {}),
          (this._valuesStartRepeat = {}),
          (this._duration = 1e3),
          (this._isDynamic = !1),
          (this._initialRepeat = 0),
          (this._repeat = 0),
          (this._yoyo = !1),
          (this._isPlaying = !1),
          (this._reversed = !1),
          (this._delayTime = 0),
          (this._startTime = 0),
          (this._easingFunction = Ze.Linear.None),
          (this._interpolationFunction = Kn.Linear),
          (this._chainedTweens = []),
          (this._onStartCallbackFired = !1),
          (this._onEveryStartCallbackFired = !1),
          (this._id = Do.nextId()),
          (this._isChainStopped = !1),
          (this._propertiesAreSetUp = !1),
          (this._goToEnd = !1),
          (this._object = e),
          typeof t == 'object'
            ? ((this._group = t), t.add(this))
            : t === !0 && ((this._group = $n), $n.add(this))
      }
      return (
        (s.prototype.getId = function () {
          return this._id
        }),
        (s.prototype.isPlaying = function () {
          return this._isPlaying
        }),
        (s.prototype.isPaused = function () {
          return this._isPaused
        }),
        (s.prototype.getDuration = function () {
          return this._duration
        }),
        (s.prototype.to = function (e, t) {
          if ((t === void 0 && (t = 1e3), this._isPlaying))
            throw new Error(
              'Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.',
            )
          return (
            (this._valuesEnd = e),
            (this._propertiesAreSetUp = !1),
            (this._duration = t < 0 ? 0 : t),
            this
          )
        }),
        (s.prototype.duration = function (e) {
          return e === void 0 && (e = 1e3), (this._duration = e < 0 ? 0 : e), this
        }),
        (s.prototype.dynamic = function (e) {
          return e === void 0 && (e = !1), (this._isDynamic = e), this
        }),
        (s.prototype.start = function (e, t) {
          if ((e === void 0 && (e = rt()), t === void 0 && (t = !1), this._isPlaying)) return this
          if (((this._repeat = this._initialRepeat), this._reversed)) {
            this._reversed = !1
            for (var n in this._valuesStartRepeat)
              this._swapEndStartRepeatValues(n), (this._valuesStart[n] = this._valuesStartRepeat[n])
          }
          if (
            ((this._isPlaying = !0),
            (this._isPaused = !1),
            (this._onStartCallbackFired = !1),
            (this._onEveryStartCallbackFired = !1),
            (this._isChainStopped = !1),
            (this._startTime = e),
            (this._startTime += this._delayTime),
            !this._propertiesAreSetUp || t)
          ) {
            if (((this._propertiesAreSetUp = !0), !this._isDynamic)) {
              var o = {}
              for (var a in this._valuesEnd) o[a] = this._valuesEnd[a]
              this._valuesEnd = o
            }
            this._setupProperties(
              this._object,
              this._valuesStart,
              this._valuesEnd,
              this._valuesStartRepeat,
              t,
            )
          }
          return this
        }),
        (s.prototype.startFromCurrentValues = function (e) {
          return this.start(e, !0)
        }),
        (s.prototype._setupProperties = function (e, t, n, o, a) {
          for (var i in n) {
            var r = e[i],
              c = Array.isArray(r),
              u = c ? 'array' : typeof r,
              d = !c && Array.isArray(n[i])
            if (!(u === 'undefined' || u === 'function')) {
              if (d) {
                var l = n[i]
                if (l.length === 0) continue
                for (var h = [r], m = 0, f = l.length; m < f; m += 1) {
                  var A = this._handleRelativeValue(r, l[m])
                  if (isNaN(A)) {
                    ;(d = !1), console.warn('Found invalid interpolation list. Skipping.')
                    break
                  }
                  h.push(A)
                }
                d && (n[i] = h)
              }
              if ((u === 'object' || c) && r && !d) {
                t[i] = c ? [] : {}
                var g = r
                for (var y in g) t[i][y] = g[y]
                o[i] = c ? [] : {}
                var l = n[i]
                if (!this._isDynamic) {
                  var E = {}
                  for (var y in l) E[y] = l[y]
                  n[i] = l = E
                }
                this._setupProperties(g, t[i], l, o[i], a)
              } else
                (typeof t[i] > 'u' || a) && (t[i] = r),
                  c || (t[i] *= 1),
                  d ? (o[i] = n[i].slice().reverse()) : (o[i] = t[i] || 0)
            }
          }
        }),
        (s.prototype.stop = function () {
          return (
            this._isChainStopped || ((this._isChainStopped = !0), this.stopChainedTweens()),
            this._isPlaying
              ? ((this._isPlaying = !1),
                (this._isPaused = !1),
                this._onStopCallback && this._onStopCallback(this._object),
                this)
              : this
          )
        }),
        (s.prototype.end = function () {
          return (this._goToEnd = !0), this.update(this._startTime + this._duration), this
        }),
        (s.prototype.pause = function (e) {
          return (
            e === void 0 && (e = rt()),
            this._isPaused || !this._isPlaying
              ? this
              : ((this._isPaused = !0), (this._pauseStart = e), this)
          )
        }),
        (s.prototype.resume = function (e) {
          return (
            e === void 0 && (e = rt()),
            !this._isPaused || !this._isPlaying
              ? this
              : ((this._isPaused = !1),
                (this._startTime += e - this._pauseStart),
                (this._pauseStart = 0),
                this)
          )
        }),
        (s.prototype.stopChainedTweens = function () {
          for (var e = 0, t = this._chainedTweens.length; e < t; e++) this._chainedTweens[e].stop()
          return this
        }),
        (s.prototype.group = function (e) {
          return e
            ? (e.add(this), this)
            : (console.warn(
                'tween.group() without args has been removed, use group.add(tween) instead.',
              ),
              this)
        }),
        (s.prototype.remove = function () {
          var e
          return (e = this._group) === null || e === void 0 || e.remove(this), this
        }),
        (s.prototype.delay = function (e) {
          return e === void 0 && (e = 0), (this._delayTime = e), this
        }),
        (s.prototype.repeat = function (e) {
          return e === void 0 && (e = 0), (this._initialRepeat = e), (this._repeat = e), this
        }),
        (s.prototype.repeatDelay = function (e) {
          return (this._repeatDelayTime = e), this
        }),
        (s.prototype.yoyo = function (e) {
          return e === void 0 && (e = !1), (this._yoyo = e), this
        }),
        (s.prototype.easing = function (e) {
          return e === void 0 && (e = Ze.Linear.None), (this._easingFunction = e), this
        }),
        (s.prototype.interpolation = function (e) {
          return e === void 0 && (e = Kn.Linear), (this._interpolationFunction = e), this
        }),
        (s.prototype.chain = function () {
          for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t]
          return (this._chainedTweens = e), this
        }),
        (s.prototype.onStart = function (e) {
          return (this._onStartCallback = e), this
        }),
        (s.prototype.onEveryStart = function (e) {
          return (this._onEveryStartCallback = e), this
        }),
        (s.prototype.onUpdate = function (e) {
          return (this._onUpdateCallback = e), this
        }),
        (s.prototype.onRepeat = function (e) {
          return (this._onRepeatCallback = e), this
        }),
        (s.prototype.onComplete = function (e) {
          return (this._onCompleteCallback = e), this
        }),
        (s.prototype.onStop = function (e) {
          return (this._onStopCallback = e), this
        }),
        (s.prototype.update = function (e, t) {
          var n = this,
            o
          if (
            (e === void 0 && (e = rt()), t === void 0 && (t = s.autoStartOnUpdate), this._isPaused)
          )
            return !0
          var a
          if (!this._goToEnd && !this._isPlaying)
            if (t) this.start(e, !0)
            else return !1
          if (((this._goToEnd = !1), e < this._startTime)) return !0
          this._onStartCallbackFired === !1 &&
            (this._onStartCallback && this._onStartCallback(this._object),
            (this._onStartCallbackFired = !0)),
            this._onEveryStartCallbackFired === !1 &&
              (this._onEveryStartCallback && this._onEveryStartCallback(this._object),
              (this._onEveryStartCallbackFired = !0))
          var i = e - this._startTime,
            r =
              this._duration +
              ((o = this._repeatDelayTime) !== null && o !== void 0 ? o : this._delayTime),
            c = this._duration + this._repeat * r,
            u = function () {
              if (n._duration === 0 || i > c) return 1
              var A = Math.trunc(i / r),
                g = i - A * r,
                y = Math.min(g / n._duration, 1)
              return y === 0 && i === n._duration ? 1 : y
            },
            d = u(),
            l = this._easingFunction(d)
          if (
            (this._updateProperties(this._object, this._valuesStart, this._valuesEnd, l),
            this._onUpdateCallback && this._onUpdateCallback(this._object, d),
            this._duration === 0 || i >= this._duration)
          )
            if (this._repeat > 0) {
              var h = Math.min(Math.trunc((i - this._duration) / r) + 1, this._repeat)
              isFinite(this._repeat) && (this._repeat -= h)
              for (a in this._valuesStartRepeat)
                !this._yoyo &&
                  typeof this._valuesEnd[a] == 'string' &&
                  (this._valuesStartRepeat[a] = // eslint-disable-next-line
                    // @ts-ignore FIXME?
                    this._valuesStartRepeat[a] + parseFloat(this._valuesEnd[a])),
                  this._yoyo && this._swapEndStartRepeatValues(a),
                  (this._valuesStart[a] = this._valuesStartRepeat[a])
              return (
                this._yoyo && (this._reversed = !this._reversed),
                (this._startTime += r * h),
                this._onRepeatCallback && this._onRepeatCallback(this._object),
                (this._onEveryStartCallbackFired = !1),
                !0
              )
            } else {
              this._onCompleteCallback && this._onCompleteCallback(this._object)
              for (var m = 0, f = this._chainedTweens.length; m < f; m++)
                this._chainedTweens[m].start(this._startTime + this._duration, !1)
              return (this._isPlaying = !1), !1
            }
          return !0
        }),
        (s.prototype._updateProperties = function (e, t, n, o) {
          for (var a in n)
            if (t[a] !== void 0) {
              var i = t[a] || 0,
                r = n[a],
                c = Array.isArray(e[a]),
                u = Array.isArray(r),
                d = !c && u
              d
                ? (e[a] = this._interpolationFunction(r, o))
                : typeof r == 'object' && r
                  ? this._updateProperties(e[a], i, r, o)
                  : ((r = this._handleRelativeValue(i, r)),
                    typeof r == 'number' && (e[a] = i + (r - i) * o))
            }
        }),
        (s.prototype._handleRelativeValue = function (e, t) {
          return typeof t != 'string'
            ? t
            : t.charAt(0) === '+' || t.charAt(0) === '-'
              ? e + parseFloat(t)
              : parseFloat(t)
        }),
        (s.prototype._swapEndStartRepeatValues = function (e) {
          var t = this._valuesStartRepeat[e],
            n = this._valuesEnd[e]
          typeof n == 'string'
            ? (this._valuesStartRepeat[e] = this._valuesStartRepeat[e] + parseFloat(n))
            : (this._valuesStartRepeat[e] = this._valuesEnd[e]),
            (this._valuesEnd[e] = t)
        }),
        (s.autoStartOnUpdate = !1),
        s
      )
    })()
Do.nextId
var we = $n
we.getAll.bind(we)
we.removeAll.bind(we)
we.add.bind(we)
we.remove.bind(we)
we.update.bind(we)
const bi = navigator.userAgent.includes('Mobi')
class nc extends se {
  constructor(e, t) {
    super(),
      (this.isWarpSplatMesh = !0),
      (this.lastActiveTime = Date.now()),
      (this.active = !1),
      (this.disposed = !1),
      (this.mapViewer = t),
      this.addScene(e),
      (this.frustumCulled = !1)
  }
  async addScene(e) {
    const t = this,
      { renderer: n, scene: o, controls: a, tileMap: i } = t.mapViewer
    fetch(e, { mode: 'cors', credentials: 'omit', cache: 'reload' })
      .then((r) => (r.ok ? r.json() : {}))
      .then((r) => {
        const c = new Te()
        if (r.transform) c.fromArray(r.transform)
        else if (r.WGS84) {
          const f = i.geo2world(new x().fromArray(r.WGS84))
          c.makeTranslation(f.x, f.y, f.z)
        }
        r.autoCut && (r.autoCut = Math.min(Math.max(r.autoCut, 1), 50))
        const u = r.autoCut && r.autoCut > 1,
          d = !1,
          l = !1,
          h = r.showWatermark !== !1,
          m = {
            renderer: n,
            scene: o,
            controls: a,
            pointcloudMode: d,
            bigSceneMode: u,
            matrix: c,
            showWatermark: h,
            depthTest: l,
          }
        m.maxRenderCountOfMobile ??
          (m.maxRenderCountOfMobile = m.bigSceneMode ? 128 * 10240 : 400 * 1e4),
          m.maxRenderCountOfPc ?? (m.maxRenderCountOfPc = m.bigSceneMode ? 320 * 1e4 : 400 * 1e4),
          (m.debugMode = this.mapViewer.events.fire(I).debugMode),
          (t.opts = m),
          (t.meta = r),
          o.add(t),
          t.initCSS3DSprite(m),
          t.applyMatrix4(c)
      })
      .catch((r) => {
        console.error(r.message)
      })
  }
  async initCSS3DSprite(e) {
    const t = this,
      n = document.createElement('div')
    ;(n.innerHTML = `<div title="${t.meta.name}" style='flex-direction: column;align-items: center;display: flex;pointer-events: auto;margin-bottom: 20px;'>
                               <svg height="20" width="20" style="color:#eeee00;opacity:0.9;"><use href="#svgicon-point3" fill="currentColor" /></svg>
                            </div>`),
      n.classList.add('splatmesh-point'),
      (n.style.position = 'absolute'),
      (n.style.borderRadius = '4px'),
      (n.style.cursor = 'pointer')
    let o = null
    ;(n.onclick = () => {
      if (o) return
      const i = t.position,
        r = e.controls,
        c = r.object.position.clone(),
        u = bi ? 6 : 2,
        d = i.clone().sub(c).normalize(),
        l = i.clone().sub(d.multiplyScalar(u)),
        h = { x: c.x, y: c.y, z: c.z, tx: r.target.x, ty: r.target.y, tz: r.target.z },
        m = { x: l.x, y: l.y, z: l.z, tx: i.x, ty: i.y, tz: i.z }
      ;(o = new tc(h).to(m, 3e3)),
        o
          .easing(Ze.Sinusoidal.InOut)
          .start()
          .onUpdate(() => {
            const f = h.y < 0.1 ? 0.1 : h.y
            r.object.position.set(h.x, f, h.z), r.target.set(h.tx, h.ty, h.tz)
          })
          .onComplete(() => {
            o = null
          })
    }),
      (n.oncontextmenu = (i) => i.preventDefault())
    const a = new oe(n)
    ;(a.element.style.pointerEvents = 'none'),
      (a.visible = !1),
      a.applyMatrix4(e.matrix),
      (t.css3dTag = a),
      e.scene.add(a),
      (t.onBeforeRender = () => {
        o?.update()
        const i = bi ? 50 : 30,
          r = t.position.distanceTo(t.mapViewer.controls.object.position)
        if (r > i) {
          t.css3dTag.visible = t.opts.controls.object.position.y > 2
          let c = 2e-3 * r
          a.scale.set(c, c, c), (t.css3dTag.visible = !0), t.splatMesh && (t.splatMesh.visible = !1)
        } else {
          if (!t.active) {
            t.splatMesh && (t.splatMesh.visible = !1)
            let c = 2e-3 * r
            a.scale.set(c, c, c), (t.css3dTag.visible = !0)
            return
          }
          if (((t.lastActiveTime = Date.now()), (t.css3dTag.visible = !1), t.splatMesh))
            t.splatMesh.visible = !0
          else {
            const c = t.meta,
              u = { ...t.opts }
            c.autoCut && (u.bigSceneMode = !0)
            const d = new he(u)
            ;(t.splatMesh = d), t.opts.scene.add(d), d.applyMatrix4(t.opts.matrix), (d.meta = c)
            const l = c.watermark || c.name || ''
            ;(c.showWatermark = c.showWatermark !== !1),
              d.fire(cs, l, !0, !1),
              d.addModel({ url: t.meta.url }, t.meta)
          }
        }
      }),
      (t.onAfterRender = () => {
        t.splatMesh &&
          (!t.active || Date.now() - t.lastActiveTime > 1 * 60 * 1e3) &&
          setTimeout(() => {
            t.splatMesh?.dispose(), (t.splatMesh = null)
          }, 5)
      })
  }
  /**
   * 销毁
   */
  dispose() {
    const e = this
    e.disposed ||
      ((e.disposed = !0),
      e.opts.scene.remove(e.css3dTag),
      e.splatMesh?.dispose(),
      (e.meta = null),
      (e.splatMesh = null),
      (e.opts = null),
      (e.css3dTag = null),
      (e.mapViewer = null))
  }
}
class ac extends Xo {
  constructor(e = {}) {
    console.info('Reall3dMapViewer', rn),
      super(),
      (this.clock = new No()),
      (this.updateTime = 0),
      (this.disposed = !1)
    const t = this,
      n = new fs()
    t.events = n
    const o = (r, c, u) => n.on(r, c, u),
      a = (r, ...c) => n.fire(r, ...c)
    t.tileMap = jr()
    const i = Zr(e)
    o(I, () => i),
      ws(n),
      vs(n),
      $r(n),
      ko(n),
      (t.camera = new Mi(60, 1, 0.01, 1e4)),
      o(O, () => t.camera),
      (t.container = i.root),
      (t.renderer = a(ho)),
      (t.scene = a(po)),
      (t.controls = a(fo)),
      (t.ambLight = new ki(16777215, 1)),
      t.scene.add(t.ambLight),
      (t.dirLight = a(go)),
      t.scene.add(t.dirLight),
      t.scene.add(t.tileMap),
      t.container.appendChild(t.renderer.domElement),
      Eo(n),
      Jr(n),
      window.addEventListener('resize', t.resize.bind(t)),
      t.resize(),
      t.renderer.setAnimationLoop(t.animate.bind(t)),
      o(on, () => t.dispose()),
      o(
        $e,
        () => {
          a($t), t.controls.update(), a(yo), a(an)
        },
        !0,
      ),
      o(
        gt,
        () => {
          t.dispatchEvent({ type: 'update', delta: t.clock.getDelta() }),
            (t.updateTime = Date.now()),
            a(de) &&
              a(ce, {
                fps: a(Kt),
                realFps: a(Zt),
                fov: a(wt),
                position: a(Ue, a(Qe)),
                lookAt: a(Ue, a(Ee)),
                lookUp: a(Ue, a(je)),
              })
        },
        !0,
      ),
      o(
        He,
        () => {
          t.tileMap.update(t.camera)
          try {
            t.renderer.render(t.scene, t.camera)
          } catch (r) {
            console.warn(r.message)
          }
        },
        !0,
      )
  }
  /**
   * 打开地图场景
   * @param 场景索引文件地址
   */
  addScenes(e) {
    const t = this
    fetch(e, { mode: 'cors', credentials: 'omit', cache: 'reload' })
      .then((n) => (n.ok ? n.json() : {}))
      .then((n) => {
        const o = new x().fromArray(n.position || [17e3, 3e4, -35e3]),
          a = new x().fromArray(n.lookAt || [17e3, 0, -35e3])
        t.controls.object.position.copy(o),
          t.controls.target.copy(a),
          t.dirLight.target.position.copy(a)
        const i = /* @__PURE__ */ new Set()
        for (let r of n.scenes) i.has(r) || (new nc(r, t), i.add(r))
      })
      .catch((n) => {
        console.error(n.message)
      })
  }
  resize() {
    const e = this
    if (e.disposed) return
    const { width: t, height: n, top: o, left: a } = e.container.getBoundingClientRect()
    e.renderer.setPixelRatio(Math.min(devicePixelRatio, 2)),
      e.renderer.setSize(t, n),
      (e.camera.aspect = t / n),
      e.camera.updateProjectionMatrix()
    const i = e.events.fire(as)
    i.setSize(t, n),
      (i.domElement.style.position = 'absolute'),
      (i.domElement.style.left = `${a}px`),
      (i.domElement.style.top = `${o}px`)
  }
  animate() {
    const e = this,
      t = (n, ...o) => e.events.fire(n, ...o)
    t(Nt), Date.now() - e.updateTime > 30 && (t($e), t(He), t(gt))
  }
  /**
   * 销毁
   */
  dispose() {
    const e = this
    if (e.disposed) return
    e.disposed = !0
    const t = e.renderer.domElement
    e.events.fire(rs),
      e.events.fire(Ao),
      e.renderer.clear(),
      e.renderer.dispose(),
      e.events.clear(),
      (e.scene = null),
      (e.renderer = null),
      (e.camera = null),
      (e.controls = null),
      (e.ambLight = null),
      (e.dirLight = null),
      e.container.removeChild(t),
      e.container.classList.add('hidden'),
      (e.container = null),
      (e.clock = null),
      (e.events = null),
      (e.tileMap = null)
  }
}
export { ac as Reall3dMapViewer, oc as Reall3dViewer, he as SplatMesh }
