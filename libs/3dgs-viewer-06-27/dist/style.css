body {
  overflow: hidden;
  margin: 0;
  height: 100vh;
  width: 100vw;
  font-family:
    <PERSON>Fang SC,
    Microsoft YaHei,
    sans-serif;
  font-size: 14px;
  background: #000;
  color: #fff;
}
#map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.gsviewer-canvas {
  width: 100%;
  height: 100%;
  touch-action: none;
}
#gsviewer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
#gsviewer .logo {
  z-index: 9999;
  width: 40px;
  height: 40px;
  animation-fill-mode: backwards;
}
#gsviewer .logo.loading {
  animation: spin infinite 2s linear;
}
@keyframes spin {
  0% {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
#gsviewer .hidden {
  display: none !important;
}
#gsviewer .info {
  z-index: 9999;
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: #eee;
  background-color: #333;
  padding: 3px 10px;
}
#gsviewer .debug {
  z-index: 9999;
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: #eee;
  background-color: #333;
  padding: 5px 10px;
  opacity: 0.85;
  pointer-events: none;
}
#gsviewer .debug table {
  width: 100%;
  font-size: 14px;
}
#gsviewer .operation {
  z-index: 9999;
  position: absolute;
  top: 30px;
  right: 20px;
  color: #eee;
  background-color: #333;
  padding: 5px;
  opacity: 0.85;
}
#gsviewer .operation table {
  width: 100%;
  font-size: 14px;
}
#gsviewer .operation table tr {
  margin-top: 10px;
  margin-bottom: 10px;
}
#gsviewer .operation table tr span {
  cursor: pointer;
  padding-left: 2px;
  padding-right: 2px;
  user-select: none;
}
#gsviewer .operation table tr span.disable {
  cursor: not-allowed;
}
#gsviewer .operation table tr span:hover {
  background-color: #999;
}
#gsviewer .operation table.plus tr.tr-data {
  display: none;
}
#gsviewer .grid-wrapper-text {
  display: flex !important;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
#gsviewer .grid-container {
  display: grid;
  grid-template-columns: repeat(20, 1fr);
  grid-template-rows: repeat(20, 1fr);
  gap: 1px;
  width: 400px;
  height: 400px;
  background-color: #aaa;
  position: relative;
  overflow: visible;
  user-select: none;
}
#gsviewer .grid-item {
  background-color: #000;
  transition: background-color 0.3s;
  user-select: none;
}
#gsviewer .yellow {
  background-color: #ff0;
}
#gsviewer .grid-line {
  background-color: #ddd;
  position: absolute;
  z-index: 5;
}
#gsviewer .grid-line.vertical {
  width: 100%;
  height: 1px;
  top: 50%;
}
#gsviewer .grid-line.horizontal {
  height: 100%;
  width: 1px;
  left: 50%;
}
#gsviewer .grid-line.left {
  height: 100%;
  width: 1px;
  left: 0%;
  background-color: #aaa;
}
#gsviewer .grid-line.top {
  width: 100%;
  height: 1px;
  top: 0%;
  background-color: #aaa;
}
#gsviewer .overlay-text {
  position: absolute;
  padding-top: 20px;
  top: 0;
  left: 0;
  width: 400px;
  height: 400px;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 380px;
  color: #ffffff4d;
  z-index: 10;
}
