# @reall3d/reall3dviewer

Install

```shell
npm i @reall3d/reall3dviewer
```

Use Reall3dViewer

```js
const viewer = new Reall3dViewer({ root: '#viewer2' })
viewer.addModel(`https://reall3d.com/demo-models/yz.spx`)
```

Use Reall3dMapViewer

```js
const mapViewer = new Reall3dMapViewer({ root: '#viewer4' })
mapViewer.addScenes('https://reall3d.com/demo-models/map/00.scenes.json')
```

# Links

- https://github.com/reall3d-com/Reall3dViewer
- https://github.com/reall3d-com/reall3dviewer-samples-use-npm-package
