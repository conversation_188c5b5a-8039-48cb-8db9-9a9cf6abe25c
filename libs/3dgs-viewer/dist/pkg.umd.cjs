(function(pt,l){typeof exports=="object"&&typeof module<"u"?l(exports,require("three"),require("@gotoeasy/three-tile")):typeof define=="function"&&define.amd?define(["exports","three","@gotoeasy/three-tile"],l):(pt=typeof globalThis<"u"?globalThis:pt||self,l(pt.reall3dviewer={},pt.THREE,pt.tt))})(this,function(pt,l,$s){"use strict";var He=typeof document<"u"?document.currentScript:null;function Zs(i){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(i){for(const e in i)if(e!=="default"){const n=Object.getOwnPropertyDescriptor(i,e);Object.defineProperty(t,e,n.get?n:{enumerable:!0,get:()=>i[e]})}}return t.default=i,Object.freeze(t)}const qn=Zs($s);let S=1;const Wt=S++,Ye=S++,Mt=S++,Jn=S++,Ge=S++,Xe=S++,ti=S++,Ne=S++,ei=S++,_t=S++,O=S++,U=S++,ni=S++,Ke=S++,vt=S++,Pt=S++,xt=S++,Ht=S++,ii=S++,si=S++,oi=S++,js=S++,ai=S++,ri=S++,$e=S++,qs=S++,Js=S++,ci=S++,j=S++;S++;const st=S++,Yt=S++,Lt=S++,li=S++,di=S++,ui=S++,pi=S++,to=S++,fi=S++,Ze=S++,je=S++,hi=S++,gi=S++,mi=S++,Ft=S++,yi=S++,Gt=S++,Ai=S++,oe=S++,ae=S++,re=S++,wi=S++,qe=S++,eo=S++,Je=S++,vi=S++,Si=S++,ce=S++,le=S++,de=S++,ue=S++,bi=S++,tn=S++,_=S++,Tt=S++,Xt=S++,V=S++,pe=S++,Ci=S++,no=S++,et=S++,en=S++,rt=S++,Mi=S++,xi=S++,Ti=S++,ki=S++,nn=S++,Ei=S++,kt=S++,Ii=S++,Di=S++,fe=S++,io=S++,_i=S++,Pi=S++;S++;const he=S++,sn=S++,on=S++,an=S++,rn=S++,ge=S++,cn=S++,Nt=S++,dt=S++,ln=S++,Li=S++,so=S++,Fi=S++,Ri=S++,Bi=S++,oo=S++,ao=S++,me=S++,ye=S++,dn=S++,Rt=S++,Et=S++,Kt=S++,Ae=S++,Ui=S++,N=S++,Q=S++,ro=S++,Vi=S++,we=S++,ve=S++,co=S++,un=S++,pn=S++,St=S++,$t=S++,Bt=S++,lo=S++,Oi=S++,uo=S++;S++,S++,S++,S++,S++;const bt=S++,Ut=S++,Se=S++,fn=S++,zi=S++,Qi=S++,hn=S++,Ct=S++,Zt=S++,Wi=S++,gn=S++,Hi=S++,po=S++,fo=S++,mn=S++,Yi=S++,yn=S++,An=S++,wn=S++,be=S++,vn=S++,Gi=S++,Ce=S++,wt=S++,Xi=S++,Ni=S++;S++;const jt=S++,Ki=S++,$i=S++,Sn=S++,Zi=S++,ho=S++,ji=S++,qi=S++,Ji=S++,ts=S++;S++;const es=S++,ns=S++,K=S++,bn=S++,Cn=S++,Mn=S++,go=S++,is=S++,mo=S++,yo=S++,Ao=S++,wo=S++,vo=S++,ss=S++,os=S++;S++,S++,S++,S++,S++;const Me=S++,xe=S++,qt=S++,Jt=S++,Te=S++,ke=S++,Ee=S++,Ie=S++,De=S++,_e=S++;class xn{constructor(){this.map=new Map}on(t,e=null,n=!1){if(!t)return console.error("Invalid event key",t),null;if(!e)return this.map.get(t);if(n){let o=this.map.get(t);o?typeof o=="function"?console.error("Invalid event type","multiFn=true",t):o.push(e):(o=[],this.map.set(t,o),o.push(e))}else{let o=this.map.get(t);o?typeof o=="function"?console.warn("Replace event",t):console.error("Invalid event type","multiFn=false",t):this.map.set(t,e)}return this.map.get(t)}fire(t,...e){const n=this.map.get(t);if(!n){this.map.size&&console.log("Undefined event:",t,"(",...e,")");return}if(typeof n=="function")return n(...e);let o=[];return n.forEach(a=>o.push(a(...e))),o}tryFire(t,...e){return this.map.get(t)?this.fire(t,...e):void 0}off(t){this.map.delete(t)}clear(){this.map.clear()}}class So{}class bo{constructor(t,e={}){this.fileSize=0,this.downloadSize=0,this.status=0,this.splatData=null,this.watermarkData=null,this.dataSplatCount=0,this.watermarkCount=0,this.sh12Data=[],this.sh3Data=[],this.sh12Count=0,this.sh3Count=0,this.rowLength=0,this.modelSplatCount=-1,this.downloadSplatCount=0,this.renderSplatCount=0,this.header=null,this.dataShDegree=0,this.minX=1/0,this.maxX=-1/0,this.minY=1/0,this.maxY=-1/0,this.minZ=1/0,this.maxZ=-1/0,this.topY=0,this.currentRadius=0,this.textWatermarkVersion=0,this.lastTextWatermarkVersion=0,this.fetchLimit=0,this.opts={...t},this.meta=e,e.autoCut&&(this.map=new Map),t.format||(t.url?.endsWith(".spx")?this.opts.format="spx":t.url?.endsWith(".splat")?this.opts.format="splat":t.url?.endsWith(".ply")?this.opts.format="ply":t.url?.endsWith(".spz")?this.opts.format="spz":console.error("unknow format!")),this.abortController=new AbortController}}var R=(i=>(i[i.FetchReady=0]="FetchReady",i[i.Fetching=1]="Fetching",i[i.FetchDone=2]="FetchDone",i[i.FetchAborted=3]="FetchAborted",i[i.FetchFailed=4]="FetchFailed",i[i.Invalid=5]="Invalid",i))(R||{});const Pe="v1.5.0-dev-pkg",ut=navigator.userAgent.includes("Mobi"),Co="QWERTYUIOPLKJHGFDSAZXCVBNM1234567890qwertyuioplkjhgfdsazxcvbnm`~!@#$%^&*()-_=+\\|]}[{'\";::,<.>//? 	",Vt=128,Mo=32,z=32,xo=20,To=16,as=64*1024,ko=1024*1e4,Eo=10240*1e4,Le=.28209479177387814,Io=0,Do=20,Tn=1,te=2,kn=3,_o=3141592653,En="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",In="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";async function Po(i){const t=new Uint32Array(i.buffer),e=new Float32Array(i.buffer),n=new So;n.Fixed=String.fromCharCode(i[0])+String.fromCharCode(i[1])+String.fromCharCode(i[2]),n.Version=i[3],n.SplatCount=t[1],n.MinX=e[2],n.MaxX=e[3],n.MinY=e[4],n.MaxY=e[5],n.MinZ=e[6],n.MaxZ=e[7],n.MinTopY=e[8],n.MaxTopY=e[9],n.CreateDate=t[10],n.CreaterId=t[11],n.ExclusiveId=t[12],n.ShDegree=i[52],n.Flag1=i[53],n.Flag2=i[54],n.Flag3=i[55],n.Reserve1=t[14],n.Reserve2=t[15];let o="";for(let f=64;f<124;f++)o+=String.fromCharCode(i[f]);if(n.Comment=o.trim(),n.HashCheck=!0,n.Fixed!=="spx"&&n.Version!==1)return null;const a=n.CreaterId==1202056903?In:En,s=WebAssembly.compile(Uint8Array.from(atob(a),f=>f.charCodeAt(0)).buffer),r=new WebAssembly.Memory({initial:1,maximum:1}),p=(await WebAssembly.instantiate(await s,{env:{memory:r,expf:Fe}})).exports.H;return new Uint8Array(r.buffer).set(i,0),p(0)&&(n.HashCheck=!1),n}async function ft(i){const t=new Uint32Array(i.slice(0,8).buffer),e=t[0],n=t[1],o=Tn==n,a=te==n,s=kn==n,r=o||a||s,c=!r,p=n==20||r?In:En,u=e*(r?To:z),d=WebAssembly.compile(Uint8Array.from(atob(p),E=>E.charCodeAt(0)).buffer),f=Math.floor((u+i.byteLength)/as)+2,y=new WebAssembly.Memory({initial:f,maximum:f}),w=(await WebAssembly.instantiate(await d,{env:{memory:y,expf:Fe}})).exports.D,m=new Uint8Array(y.buffer);return m.set(i,u),w(0,u)?{splatCount:e,blockFormat:n,success:!1}:{splatCount:e,blockFormat:n,success:!0,datas:m.slice(0,u),isSplat:c,isSh:r,isSh1:o,isSh2:a,isSh3:s}}async function Dn(i,t){const e=WebAssembly.compile(Uint8Array.from(atob(In),p=>p.charCodeAt(0)).buffer),n=Math.floor(t*z/as)+2,o=new WebAssembly.Memory({initial:n,maximum:n}),s=(await WebAssembly.instantiate(await e,{env:{memory:o,expf:Fe}})).exports.s,r=new Uint8Array(o.buffer);r.set(i.slice(0,t*z),0);const c=s(0,t);return c?(console.error("splat data parser failed:",c),new Uint8Array(0)):r.slice(0,t*z)}async function Lo(i,t,e=!0,n=!0){const o=WebAssembly.compile(Uint8Array.from(atob(En),d=>d.charCodeAt(0)).buffer),a=new WebAssembly.Memory({initial:1,maximum:1}),r=(await WebAssembly.instantiate(await o,{env:{memory:a,expf:Fe}})).exports.w,c=new Uint8Array(a.buffer),p=new Float32Array(c.buffer),u=n?-1:1;return p[0]=i,e?p[1]=u*t:p[2]=u*t,r(0,e?1:0),c.slice(0,z)}function Fe(i){return Math.exp(i)}const Ot=ut?20480:51200;async function Fo(i){let t=0;try{i.status=R.Fetching;const n=i.abortController.signal,o=i.opts.fetchReload?"reload":"default",a=await fetch(i.opts.url,{mode:"cors",credentials:"omit",cache:o,signal:n});if(a.status!=200){console.warn(`fetch error: ${a.status}`),i.status===R.Fetching&&(i.status=R.FetchFailed);return}const s=a.body.getReader(),r=parseInt(a.headers.get("content-length")||"0");i.rowLength=32,i.fileSize=r;const c=r/i.rowLength|0;if(c<1){console.warn("data empty",i.opts.url),i.status===R.Fetching&&(i.status=R.Invalid);return}i.modelSplatCount=c,i.downloadSplatCount=0,i.splatData=new Uint8Array(Math.min(i.modelSplatCount,i.fetchLimit)*32),i.watermarkData=new Uint8Array(0);let p=new Uint8Array(32),u=0;for(;;){let{done:d,value:f}=await s.read();if(d)break;u+f.byteLength<i.rowLength?(p.set(f,u),u+=f.byteLength,t+=f.length,i.downloadSize=t):(u=await e(i,u,p,f),u&&p.set(f.slice(f.byteLength-u),0)),i.downloadSplatCount>=i.fetchLimit&&i.abortController.abort()}}catch(n){n.name==="AbortError"?(console.log("Fetch Abort",i.opts.url),i.status===R.Fetching&&(i.status=R.FetchAborted)):(console.error(n),i.status===R.Fetching&&(i.status=R.FetchFailed))}finally{i.status===R.Fetching&&(i.status=R.FetchDone)}async function e(n,o,a,s){return new Promise(async r=>{let c=(o+s.byteLength)/n.rowLength|0,p=(o+s.byteLength)%n.rowLength,u;o?(u=new Uint8Array(c*n.rowLength),u.set(a.slice(0,o),0),u.set(s.slice(0,s.byteLength-p),o)):u=s.slice(0,c*n.rowLength),n.downloadSplatCount+c>n.fetchLimit&&(c=n.fetchLimit-n.downloadSplatCount,p=0);const d=async()=>{if(c>Ot){const f=await Dn(u,Ot);rs(n,f),n.downloadSplatCount+=Ot,t+=Ot*n.rowLength,n.downloadSize=t,c-=Ot,u=u.slice(Ot*n.rowLength),setTimeout(d)}else{const f=await Dn(u,c);rs(n,f),n.downloadSplatCount+=c,t+=c*n.rowLength,n.downloadSize=t,r(p)}};await d()})}}function rs(i,t){let e=t.byteLength/z;const n=Math.min(i.fetchLimit,i.modelSplatCount);i.dataSplatCount+e>n?(e=n-i.dataSplatCount,i.splatData.set(t.slice(0,e*z),i.dataSplatCount*z)):i.splatData.set(t,i.dataSplatCount*z);const o=new Float32Array(t.buffer);for(let s=0,r=0,c=0,p=0;s<e;s++)r=o[s*8],c=o[s*8+1],p=o[s*8+2],i.minX=Math.min(i.minX,r),i.maxX=Math.max(i.maxX,r),i.minY=Math.min(i.minY,c),i.maxY=Math.max(i.maxY,c),i.minZ=Math.min(i.minZ,p),i.maxZ=Math.max(i.maxZ,p);i.dataSplatCount+=e;const a=i.header?.MinTopY||0;i.currentRadius=Math.sqrt(i.maxX*i.maxX+a*a+i.maxZ*i.maxZ),i.aabbCenter=new l.Vector3((i.minX+i.maxX)/2,(i.minY+i.maxY)/2,(i.minZ+i.maxZ)/2)}function _n(i){let t=!1;const e=(g,w,m)=>i.on(g,w,m),n=(g,...w)=>i.fire(g,...w),o=!n(_).renderer;e(rt,()=>n(_).debugMode),e(tn,()=>t=!0);let a=0;(function g(){a++,!t&&requestAnimationFrame(g)})(),e(Wt,(g,w=null,m=0)=>{const A=()=>{t||(m>0?!(a%m)&&g(a):g(a),w&&w()&&requestAnimationFrame(A))};A()}),e(Ye,(g,w=null,m=20)=>{const A=()=>{t||(g(),w&&w()&&setTimeout(A,m))};A()});let s=!1,r=0,c=0;e(Ii,()=>{if(s=!0,n(_).debugMode)(async()=>{const g=document.querySelector("#gsviewer #progressBarWrap");if(g){g.style.display="block";const w=document.querySelector("#gsviewer #progressBar");w&&(w.style.width="0%")}})(),(async()=>document.querySelector("#gsviewer .logo")?.classList.add("loading"))();else if(n(_).useCustomControl===!1)try{parent?.onProgress&&parent.onProgress(.001,"0.001%")}catch(w){console.debug("Cross-origin access blocked:",w.message)}}),e(fe,g=>{if(g&&(r=g),s=!1,g!==void 0&&((async()=>{const m=document.querySelector("#gsviewer #progressBarWrap");m&&(m.style.display="none")})(),(async()=>document.querySelector("#gsviewer .logo")?.classList.remove("loading"))(),n(_).useCustomControl===!1))try{parent?.onProgress&&parent.onProgress(0,"100%",9)}catch(m){console.debug("Cross-origin access blocked:",m.message)}}),e(Di,g=>{if(s=!0,n(_).debugMode)(async()=>{const w=document.querySelector("#gsviewer #progressBar");w&&(w.style.width=`${g}%`)})();else if(n(_).useCustomControl===!1)try{parent?.onProgress&&parent.onProgress(g,`${g}%`)}catch(m){console.debug("Cross-origin access blocked:",m.message)}}),e(io,()=>s),e(_i,g=>{c=g}),e(Pi,()=>!s&&r&&c>=r),e(Tt,()=>{const w=n(_t).parentElement.getBoundingClientRect();return{width:w.width,height:w.height,left:w.left,top:w.top}}),e(kt,g=>{let w=g.x.toFixed(3).split("."),m=g.y.toFixed(3).split("."),A=g.z.toFixed(3).split(".");return(w[1]==="000"||w[1]==="00000")&&(w[1]="0"),(m[1]==="000"||m[1]==="00000")&&(m[1]="0"),(A[1]==="000"||A[1]==="00000")&&(A[1]="0"),`${w.join(".")}, ${m.join(".")}, ${A.join(".")}`}),e(qs,g=>btoa(g)),e(Js,g=>atob(g));const p=.001;let u=new l.Vector3,d=new l.Vector3,f=0;e(no,()=>{const g=n(O),w=g.fov,m=g.position.clone(),A=g.getWorldDirection(new l.Vector3);return Math.abs(f-w)<p&&Math.abs(m.x-u.x)<p&&Math.abs(m.y-u.y)<p&&Math.abs(m.z-u.z)<p&&Math.abs(A.x-d.x)<p&&Math.abs(A.y-d.y)<p&&Math.abs(A.z-d.z)<p?!1:(f=w,u=m,d=A,!0)}),e(Ct,g=>{if(!g)return;const w=[];g.traverse(m=>w.push(m)),w.forEach(m=>{m.dispose?m.dispose():(m.geometry?.dispose?.(),m.material&&m.material instanceof Array?m.material.forEach(A=>A?.dispose?.()):m.material?.dispose?.())}),g.clear()}),e(st,async({renderSplatCount:g,visibleSplatCount:w,modelSplatCount:m,fps:A,realFps:E,sortTime:x,fov:b,position:P,lookUp:T,lookAt:I,worker:F,scene:W,scale:G,cuts:h,shDegree:M}={})=>{if(!n(rt))return;M!==void 0&&y("shDegree",`${M}`),g!==void 0&&y("renderSplatCount",`${g}`),w!==void 0&&y("visibleSplatCount",`${w}`),m!==void 0&&y("modelSplatCount",`${m}`),A!==void 0&&y("fps",A),E!==void 0&&y("realFps",`raw ${E}`),x!==void 0&&y("sort",`${x} ms`),!o&&n(et)?h!==void 0&&y("cuts",`（${h} cuts）`):y("cuts",""),F&&y("worker",`${F}`),W&&y("scene",W),b&&y("fov",b),P&&y("position",P),T&&y("lookUp",T),I&&y("lookAt",I),I&&y("viewer-version",Pe);let C=performance.memory||{usedJSHeapSize:0,totalJSHeapSize:0,jsHeapSizeLimit:0},v="",k=C.usedJSHeapSize/1024/1024;k>1e3?v+=(k/1024).toFixed(2)+" G":v+=k.toFixed(0)+" M",v+=" / ";let D=C.totalJSHeapSize/1024/1024;D>1e3?v+=(D/1024).toFixed(2)+" G":v+=D.toFixed(0)+" M";let L=C.jsHeapSizeLimit/1024/1024;v+=" / ",L>1e3?v+=(L/1024).toFixed(2)+" G":v+=L.toFixed(0)+" M",y("memory",v),G&&y("scale",G)});async function y(g,w){let m=document.querySelector(`#gsviewer .debug .${g}`);m&&(m.innerText=w)}}function Ro(i=0){const t=new Date;return t.setDate(t.getDate()-7),i>=t.getFullYear()*1e4+(t.getMonth()+1)*100+t.getDate()}async function cs(i){try{const t=new ReadableStream({async start(n){n.enqueue(i),n.close()}}),e=new Response(t.pipeThrough(new DecompressionStream("gzip")));return new Uint8Array(await e.arrayBuffer())}catch(t){return console.error("unGzip Failed:",t),null}}function nt(i){return i<0?0:i>255?255:i|0}function Re(i){const t=nt(Math.round(i*128)+128);return nt(Math.floor((t+4)/8)*8)}const Bo=[Io,_o];async function Uo(i){try{i.status=R.Fetching;const t=i.abortController.signal,e=i.opts.fetchReload?"reload":"default",n=await fetch(i.opts.url,{mode:"cors",credentials:"omit",cache:e,signal:t});if(n.status!=200){console.warn(`fetch error: ${n.status}`),i.status===R.Fetching&&(i.status=R.FetchFailed);return}const o=n.body.getReader(),a=parseInt(n.headers.get("content-length")||"0");if(a-Vt<xo){console.warn("data empty",i.opts.url),i.status===R.Fetching&&(i.status=R.Invalid);return}i.fileSize=a;let r=[],c=new Uint8Array(Vt),p=new Uint8Array(z),u=0,d=!1,f=0,y=0,g,w=!1;for(;;){let{done:m,value:A}=await o.read();if(m)break;if(i.downloadSize+=A.byteLength,r){r.push(A);let b=0;for(let I=0;I<r.length;I++)b+=r[I].byteLength;if(b<Vt)continue;let P=0;for(let I=0;I<r.length;I++)P+r[I].byteLength<Vt?(c.set(r[I],P),P+=r[I].byteLength):(c.set(r[I].slice(0,Vt-P),P),A=new Uint8Array(r[I].slice(Vt-P)));const T=await Po(c);if(!T){i.abortController.abort(),i.status===R.Fetching&&(i.status=R.Invalid),console.error("invalid spx format");continue}if(i.header=T,i.modelSplatCount=T.SplatCount,i.dataShDegree=T.ShDegree,i.aabbCenter=new l.Vector3((T.MaxX+T.MaxX)/2,(T.MinY+T.MaxY)/2,(T.MinZ+T.MaxZ)/2),r=null,c=null,!Bo.includes(T.ExclusiveId)){i.abortController.abort(),i.status=R.Invalid,console.error("Unrecognized format, creater id =",T.CreaterId,", exclusive id =",T.ExclusiveId,T.Comment);continue}}if(!d){if(u+A.byteLength<4){p.set(A,u),u+=A.byteLength;continue}const b=new Uint8Array(u+A.byteLength);b.set(p.slice(0,u),0),b.set(A,u),A=b.slice(4),u=0,d=!0,g=[],y=0;const P=new Int32Array(b.slice(0,4).buffer);w=P[0]<0,f=Math.abs(P[0])}let E=y+A.byteLength;if(g.push(A),E<f){y+=A.byteLength;continue}for(;E>=f;){let b=new Uint8Array(f),P=0;for(let I=0;I<g.length;I++)P+g[I].byteLength<f?(b.set(g[I],P),P+=g[I].byteLength):(b.set(g[I].slice(0,f-P),P),A=new Uint8Array(g[I].slice(f-P)));w&&(b=await cs(b));const T=await ft(b);if(!T.success){console.error("spx block data parser failed. block format:",T.blockFormat),i.abortController.abort(),i.status=R.Invalid;break}if(T.isSplat)i.downloadSplatCount+=T.datas.byteLength/32,Vo(i,T.datas);else{const I=Math.min(i.fetchLimit,i.modelSplatCount);if(T.isSh3)if(i.sh3Count+T.splatCount>I){const F=I-i.sh3Count;i.sh3Data.push(T.datas.slice(0,F*16)),i.sh3Count+=F}else i.sh3Data.push(T.datas),i.sh3Count+=T.splatCount;else if(i.sh12Count+T.splatCount>I){const F=I-i.sh12Count;i.sh12Data.push(T.datas.slice(0,F*16)),i.sh12Count+=F}else i.sh12Data.push(T.datas),i.sh12Count+=T.splatCount}if(A.byteLength<4){p.set(A,0),u=A.byteLength,d=!1;break}else{const I=new Int32Array(A.slice(0,4).buffer);f=Math.abs(I[0]),w=I[0]<0,A=A.slice(4),E=A.byteLength,g=[A],y=A.byteLength}}const x=i.fetchLimit;i.header.ShDegree===3?i.downloadSplatCount>=x&&i.sh12Count>=x&&i.sh3Count>=x&&i.abortController.abort():i.header.ShDegree?i.downloadSplatCount>=x&&i.sh12Count>=x&&i.abortController.abort():i.downloadSplatCount>=x&&i.abortController.abort()}}catch(t){t.name==="AbortError"?(console.log("Fetch Abort",i.opts.url),i.status===R.Fetching&&(i.status=R.FetchAborted)):(console.error(t),i.status===R.Fetching&&(i.status=R.FetchFailed),i.abortController.abort())}finally{i.status===R.Fetching&&(i.status=R.FetchDone)}}function Vo(i,t){let e=!!i.meta.autoCut,n=t.byteLength/32;const o=4096;if(!e){const c=Math.min(i.fetchLimit,i.modelSplatCount);if(!i.splatData&&(i.splatData=new Uint8Array(c*z)),!i.watermarkData&&(i.watermarkData=new Uint8Array(0)),i.dataSplatCount+n>c&&(n=c-i.dataSplatCount,!n))return;const p=new Float32Array(t.buffer),u=new Uint32Array(t.buffer);for(let f=0,y=0,g=0,w=0;f<n;f++)if(y=p[f*8],g=p[f*8+1],w=p[f*8+2],i.minX=Math.min(i.minX,y),i.maxX=Math.max(i.maxX,y),i.minY=Math.min(i.minY,g),i.maxY=Math.max(i.maxY,g),i.minZ=Math.min(i.minZ,w),i.maxZ=Math.max(i.maxZ,w),u[f*8+3]>>16){if(i.watermarkCount*z===i.watermarkData.byteLength){const m=new Uint8Array((i.watermarkCount+o)*z);m.set(i.watermarkData,0),i.watermarkData=m}i.watermarkData.set(t.slice(f*32,f*32+32),i.watermarkCount++*z)}else i.splatData.set(t.slice(f*32,f*32+32),i.dataSplatCount++*z);const d=i.header.MinTopY||0;i.currentRadius=Math.sqrt(i.maxX*i.maxX+d*d+i.maxZ*i.maxZ);return}let a=Math.min(Math.max(i.meta.autoCut,2),50);!i.watermarkData&&(i.watermarkData=new Uint8Array(0));const s=new Float32Array(t.buffer),r=new Uint32Array(t.buffer);for(let c=0,p=Math.floor(t.byteLength/z),u=0,d=0,f=0,y="";c<p;c++){if(r[c*8+3]>>16){if(i.watermarkCount*z===i.watermarkData.byteLength){const A=new Uint8Array((i.watermarkCount+o)*z);A.set(i.watermarkData,0),i.watermarkData=A}i.watermarkData.set(t.slice(c*32,c*32+32),i.watermarkCount++*z);continue}u=s[c*8],d=s[c*8+1],f=s[c*8+2];let g=Math.min(a-1,Math.floor(Math.max(0,u-i.header.MinX)/(i.header.MaxX-i.header.MinX)*a)),w=Math.min(a-1,Math.floor(Math.max(0,f-i.header.MinZ)/(i.header.MaxZ-i.header.MinZ)*a));y=`${g}-${w}`;let m=i.map.get(y);if(!m)m={},m.minX=u,m.maxX=u,m.minY=d,m.maxY=d,m.minZ=f,m.maxZ=f,m.centerX=u,m.centerY=d,m.centerZ=f,m.radius=0,m.splatData=new Uint8Array(o*z),m.splatData.set(t.slice(c*z,c*z+z),0),m.splatCount=1,i.map.set(y,m);else{if(m.splatData.byteLength/z==m.splatCount){const b=new Uint8Array(m.splatData.byteLength+o*z);b.set(m.splatData,0),m.splatData=b}m.minX=Math.min(m.minX,u),m.maxX=Math.max(m.maxX,u),m.minY=Math.min(m.minY,d),m.maxY=Math.max(m.maxY,d),m.minZ=Math.min(m.minZ,f),m.maxZ=Math.max(m.maxZ,f),m.centerX=(m.maxX+m.minX)/2,m.centerY=(m.maxY+m.minY)/2,m.centerZ=(m.maxZ+m.minZ)/2;const A=m.maxX-m.minX,E=m.maxY-m.minY,x=m.maxZ-m.minZ;m.radius=Math.sqrt(A*A+E*E+x*x)/2,m.splatData.set(t.slice(c*z,c*z+z),m.splatCount++*z)}i.dataSplatCount++}}const ee=ut?20480:51200;async function Oo(i){try{i.status=R.Fetching;const o=i.abortController.signal,a=i.opts.fetchReload?"reload":"default",s=await fetch(i.opts.url,{mode:"cors",credentials:"omit",cache:a,signal:o});if(s.status!=200){console.warn(`fetch error: ${s.status}`),i.status===R.Fetching&&(i.status=R.FetchFailed);return}const r=s.body.getReader(),c=parseInt(s.headers.get("content-length")||"0");i.fileSize=c,i.downloadSize=0,i.downloadSplatCount=0,i.watermarkData=new Uint8Array(0);let p=new Uint8Array(256),u=0,d=[],f;for(;;){let{done:y,value:g}=await r.read();if(y)break;if(i.downloadSize+=g.byteLength,d){if(d.push(g),i.downloadSize<200)continue;const w=new Uint8Array(i.downloadSize);for(let m=0,A=0;m<d.length;m++)w.set(d[m],A),A+=d[m].byteLength;if(f=n(w),!f){d=[w];continue}d=null,g=w.slice(f.headerLength),i.rowLength=f.rowLength,i.dataShDegree=f.shDegree,i.modelSplatCount=f.vertexCount,i.splatData=new Uint8Array(Math.min(i.modelSplatCount,i.fetchLimit)*32)}u+g.byteLength<i.rowLength?(p.set(g,u),u+=g.byteLength):(u=await t(f,i,u,p,g),u&&p.set(g.slice(g.byteLength-u),0)),i.downloadSplatCount>=i.fetchLimit&&i.abortController.abort()}}catch(o){o.name==="AbortError"?(console.log("Fetch Abort",i.opts.url),i.status===R.Fetching&&(i.status=R.FetchAborted)):(console.error(o),i.status===R.Fetching&&(i.status=R.FetchFailed))}finally{i.status===R.Fetching&&(i.status=R.FetchDone)}async function t(o,a,s,r,c){return new Promise(async p=>{let u=(s+c.byteLength)/a.rowLength|0,d=(s+c.byteLength)%a.rowLength,f;s?(f=new Uint8Array(u*a.rowLength),f.set(r.slice(0,s),0),f.set(c.slice(0,c.byteLength-d),s)):f=c.slice(0,u*a.rowLength),a.downloadSplatCount+u>a.fetchLimit&&(u=a.fetchLimit-a.downloadSplatCount,d=0);const y=async()=>{if(u>ee){const g=await e(o,f,ee);ls(a,g),a.downloadSplatCount+=ee,u-=ee,f=f.slice(ee*a.rowLength),setTimeout(y)}else{const g=await e(o,f,u);ls(a,g),a.downloadSplatCount+=u,p(d)}};await y()})}async function e(o,a,s){const r=new Uint8Array(s*Mo),c=new Float32Array(a.buffer),p=new Float32Array(r.buffer);for(let u=0;u<s;u++)p[u*8+0]=c[(u*i.rowLength+o.offsets.x)/4],p[u*8+1]=c[(u*i.rowLength+o.offsets.y)/4],p[u*8+2]=c[(u*i.rowLength+o.offsets.z)/4],p[u*8+3]=Math.exp(c[(u*i.rowLength+o.offsets.scale_0)/4]),p[u*8+4]=Math.exp(c[(u*i.rowLength+o.offsets.scale_1)/4]),p[u*8+5]=Math.exp(c[(u*i.rowLength+o.offsets.scale_2)/4]),r[u*32+24]=nt((.5+Le*c[(u*i.rowLength+o.offsets.f_dc_0)/4])*255),r[u*32+25]=nt((.5+Le*c[(u*i.rowLength+o.offsets.f_dc_1)/4])*255),r[u*32+26]=nt((.5+Le*c[(u*i.rowLength+o.offsets.f_dc_2)/4])*255),r[u*32+27]=nt(1/(1+Math.exp(-c[(u*i.rowLength+o.offsets.opacity)/4]))*255),r[u*32+28]=nt(c[(u*i.rowLength+o.offsets.rot_0)/4]*128+128),r[u*32+29]=nt(c[(u*i.rowLength+o.offsets.rot_1)/4]*128+128),r[u*32+30]=nt(c[(u*i.rowLength+o.offsets.rot_2)/4]*128+128),r[u*32+31]=nt(c[(u*i.rowLength+o.offsets.rot_3)/4]*128+128);if(o.shDegree==3){const d=new Uint8Array(s*24+8),f=new Uint8Array(s*21+8),y=new Uint32Array(2);y[0]=s,y[1]=te,d.set(new Uint8Array(y.buffer),0);const g=new Uint32Array(2);g[0]=s,g[1]=kn,f.set(new Uint8Array(g.buffer),0);for(let A=0,E=0;A<s;A++)for(let x=0;x<8;x++)for(let b=0;b<3;b++)d[8+E++]=Re(c[(A*i.rowLength+o.offsets["f_rest_"+(x+b*15)])/4]);for(let A=0,E=0;A<s;A++)for(let x=8;x<15;x++)for(let b=0;b<3;b++)f[8+E++]=Re(c[(A*i.rowLength+o.offsets["f_rest_"+(x+b*15)])/4]);const w=await ft(d);i.sh12Data.push(w.datas);const m=await ft(f);i.sh3Data.push(m.datas)}else if(o.shDegree==2){const d=new Uint8Array(s*24+8),f=new Uint32Array(2);f[0]=s,f[1]=te,d.set(new Uint8Array(f.buffer),0);for(let g=0,w=0;g<s;g++)for(let m=0;m<8;m++)for(let A=0;A<3;A++)d[8+w++]=Re(c[(g*i.rowLength+o.offsets["f_rest_"+(m+A*8)])/4]);const y=await ft(d);i.sh12Data.push(y.datas)}else if(o.shDegree==1){const d=new Uint8Array(s*9+8),f=new Uint32Array(2);f[0]=s,f[1]=Tn,d.set(new Uint8Array(f.buffer),0);for(let g=0,w=0;g<s;g++)for(let m=0;m<3;m++)for(let A=0;A<3;A++)d[8+w++]=Re(c[(g*i.rowLength+o.offsets["f_rest_"+(m+A*3)])/4]);const y=await ft(d);i.sh12Data.push(y.datas)}return await Dn(r,s)}function n(o){let a=new TextDecoder().decode(o.slice(0,2048));const s=`end_header
`,r=a.indexOf(s);if(r<0){if(o.byteLength>1024*2)throw new Error("Unable to read .ply file header");return null}if(!a.startsWith("ply")||a.indexOf("format binary_little_endian 1.0")<0)throw new Error("Unknow .ply file header");const c=parseInt(/element vertex (\d+)\n/.exec(a)[1]);let p=0,u={},d={};const f={double:"getFloat64",int:"getInt32",uint:"getUint32",float:"getFloat32",short:"getInt16",ushort:"getUint16",uchar:"getUint8"};for(let w of a.slice(0,r).split(`
`).filter(m=>m.startsWith("property "))){const[m,A,E]=w.split(" "),x=f[A]||"getInt8";d[E]=x,u[E]=p,p+=parseInt(x.replace(/[^\d]/g,""))/8}let y=0;d.f_rest_44?y=3:d.f_rest_23?y=2:d.f_rest_8&&(y=1);const g=["x","y","z","scale_0","scale_1","scale_2","f_dc_0","f_dc_1","f_dc_2","opacity","rot_0","rot_1","rot_2","rot_3"];for(let w=0;w<g.length;w++){const m=g[w];if(!d[m])throw new Error(`Property not found: ${m}`)}return{headerLength:r+s.length,offsets:u,rowLength:p,vertexCount:c,shDegree:y}}}function ls(i,t){let e=t.byteLength/z;const n=Math.min(i.fetchLimit,i.modelSplatCount);i.dataSplatCount+e>n?(e=n-i.dataSplatCount,i.splatData.set(t.slice(0,e*z),i.dataSplatCount*z)):i.splatData.set(t,i.dataSplatCount*z);const o=new Float32Array(t.buffer);for(let s=0,r=0,c=0,p=0;s<e;s++)r=o[s*8],c=o[s*8+1],p=o[s*8+2],i.minX=Math.min(i.minX,r),i.maxX=Math.max(i.maxX,r),i.minY=Math.min(i.minY,c),i.maxY=Math.max(i.maxY,c),i.minZ=Math.min(i.minZ,p),i.maxZ=Math.max(i.maxZ,p);i.dataSplatCount+=e;const a=0;i.currentRadius=Math.sqrt(i.maxX*i.maxX+a*a+i.maxZ*i.maxZ),i.aabbCenter=new l.Vector3((i.minX+i.maxX)/2,(i.minY+i.maxY)/2,(i.minZ+i.maxZ)/2)}const H=ut?20480:51200,Pn=16;async function zo(i){try{i.status=R.Fetching;const o=i.abortController.signal,a=i.opts.fetchReload?"reload":"default",s=await fetch(i.opts.url,{mode:"cors",credentials:"omit",cache:a,signal:o});if(s.status!=200){console.warn(`fetch error: ${s.status}`),i.status===R.Fetching&&(i.status=R.FetchFailed);return}const r=s.body.getReader(),c=parseInt(s.headers.get("content-length")||"0");i.fileSize=c,i.downloadSize=0,i.downloadSplatCount=0,i.watermarkData=new Uint8Array(0);const p=new Uint8Array(c);for(;;){let{done:f,value:y}=await r.read();if(f)break;p.set(y,i.downloadSize),i.downloadSize+=y.length}const u=await cs(p);if(!u||u.length<16){console.error("Invalid spz format"),i.status=R.Invalid;return}const d=n(u);i.modelSplatCount=d.numPoints,i.dataShDegree=d.shDegree,i.splatData=new Uint8Array(Math.min(i.modelSplatCount,i.fetchLimit)*32),await t(d,i,u)}catch(o){o.name==="AbortError"?(console.log("Fetch Abort",i.opts.url),i.status===R.Fetching&&(i.status=R.FetchAborted)):(console.error(o),i.status===R.Fetching&&(i.status=R.FetchFailed))}finally{i.status===R.Fetching&&(i.status=R.FetchDone)}async function t(o,a,s){const r=o.numPoints*9,c=o.numPoints,p=o.numPoints*3,u=o.numPoints*3,d=o.numPoints*3,f=Pn,y=f+r,g=y+c,w=g+p,m=w+u,A=m+d,E=Math.min(o.numPoints,a.fetchLimit),x=Math.ceil(E/H);for(let b=0;b<x;b++){let P=b<x-1?H:E-b*H;a.dataSplatCount+P>a.fetchLimit&&(P=a.fetchLimit-a.dataSplatCount);const T=new Uint8Array(P*20+8),I=new Uint32Array(2);I[0]=P,I[1]=Do,T.set(new Uint8Array(I.buffer),0);let F=8;for(let h=0;h<P;h++)T[F++]=s[f+(b*H+h)*9+0],T[F++]=s[f+(b*H+h)*9+1],T[F++]=s[f+(b*H+h)*9+2];for(let h=0;h<P;h++)T[F++]=s[f+(b*H+h)*9+3],T[F++]=s[f+(b*H+h)*9+4],T[F++]=s[f+(b*H+h)*9+5];for(let h=0;h<P;h++)T[F++]=s[f+(b*H+h)*9+6],T[F++]=s[f+(b*H+h)*9+7],T[F++]=s[f+(b*H+h)*9+8];for(let h=0;h<P;h++)T[F++]=s[w+(b*H+h)*3];for(let h=0;h<P;h++)T[F++]=s[w+(b*H+h)*3+1];for(let h=0;h<P;h++)T[F++]=s[w+(b*H+h)*3+2];for(let h=0;h<P;h++)T[F++]=Ln(s[g+(b*H+h)*3]);for(let h=0;h<P;h++)T[F++]=Ln(s[g+(b*H+h)*3+1]);for(let h=0;h<P;h++)T[F++]=Ln(s[g+(b*H+h)*3+2]);const W=[];for(let h=0,M=0,C=0,v=0;h<P;h++)T[F++]=s[y+(b*H+h)],M=s[m+(b*H+h)*3+0],C=s[m+(b*H+h)*3+1],v=s[m+(b*H+h)*3+2],W.push(Qo(M,C,v));for(let h=0;h<P;h++)T[F++]=W[h][0];for(let h=0;h<P;h++)T[F++]=W[h][1];for(let h=0;h<P;h++)T[F++]=W[h][2];for(let h=0;h<P;h++)T[F++]=W[h][3];const G=await ft(T);if(e(o,a,G.datas),o.shDegree===1){const h=new Uint8Array(P*9+8),M=new Uint32Array(2);M[0]=P,M[1]=Tn,h.set(new Uint8Array(M.buffer),0);for(let v=0,k=8;v<P;v++)h.set(s.slice(A+(b*H+v)*9,A+(b*H+v)*9+9),k),k+=9;const C=await ft(h);a.sh12Data.push(C.datas)}else if(o.shDegree===2){const h=new Uint8Array(P*24+8),M=new Uint32Array(2);M[0]=P,M[1]=te,h.set(new Uint8Array(M.buffer),0);for(let v=0,k=8;v<P;v++)h.set(s.slice(A+(b*H+v)*24,A+(b*H+v)*24+24),k),k+=24;const C=await ft(h);a.sh12Data.push(C.datas)}else if(o.shDegree===3){const h=new Uint8Array(P*24+8),M=new Uint32Array(2);M[0]=P,M[1]=te,h.set(new Uint8Array(M.buffer),0);for(let L=0,B=8;L<P;L++)h.set(s.slice(A+(b*H+L)*45,A+(b*H+L)*45+24),B),B+=24;const C=await ft(h);a.sh12Data.push(C.datas);const v=new Uint8Array(P*21+8),k=new Uint32Array(2);k[0]=P,k[1]=kn,v.set(new Uint8Array(k.buffer),0);for(let L=0,B=8;L<P;L++)v.set(s.slice(A+(b*H+L)*45+24,A+(b*H+L)*45+45),B),B+=21;const D=await ft(v);a.sh3Data.push(D.datas)}if(a.dataSplatCount>=a.fetchLimit)break}}function e(o,a,s){let r=s.byteLength/z;const c=Math.min(a.fetchLimit,o.numPoints);a.dataSplatCount+r>c?(r=c-a.dataSplatCount,a.splatData.set(s.slice(0,r*z),a.dataSplatCount*z)):a.splatData.set(s,a.dataSplatCount*z);const p=new Float32Array(s.buffer);for(let d=0,f=0,y=0,g=0;d<r;d++)f=p[d*8],y=p[d*8+1],g=p[d*8+2],a.minX=Math.min(a.minX,f),a.maxX=Math.max(a.maxX,f),a.minY=Math.min(a.minY,y),a.maxY=Math.max(a.maxY,y),a.minZ=Math.min(a.minZ,g),a.maxZ=Math.max(a.maxZ,g);a.dataSplatCount+=r,a.downloadSplatCount+=r;const u=0;a.currentRadius=Math.sqrt(a.maxX*a.maxX+u*u+a.maxZ*a.maxZ),a.aabbCenter=new l.Vector3((a.minX+a.maxX)/2,(a.minY+a.maxY)/2,(a.minZ+a.maxZ)/2)}function n(o){const a=o.slice(0,Pn),s=new Uint32Array(a.buffer),r={};if(r.magic=s[0],r.version=s[1],r.numPoints=s[2],r.shDegree=a[12],r.fractionalBits=a[13],r.flags=a[14],r.reserved=a[15],r.magic!==1347635022)throw new Error("[SPZ ERROR] header not found");if(r.version!==2)throw new Error("[SPZ ERROR] ersion not supported:"+r.version);if(r.shDegree>3)throw new Error("[SPZ ERROR] unsupported SH degree:"+r.shDegree);if(r.fractionalBits!==12)throw new Error("[SPZ ERROR] unsupported FractionalBits:"+r.fractionalBits);let c=0;if(r.shDegree===1?c=9:r.shDegree===2?c=24:r.shDegree===3&&(c=45),o.length!==Pn+r.numPoints*(19+c))throw new Error("[SPZ ERROR] invalid spz data");return r}}function Ln(i){const t=(i-127.5)/38.25;return nt((.5+Le*t)*255)}function Qo(i,t,e){const n=i/127.5-1,o=t/127.5-1,a=e/127.5-1,s=Math.sqrt(Math.max(0,1-(n*n+o*o+a*a)));return[nt(s*128+128),nt(n*128+128),nt(o*128+128),nt(a*128+128)]}function Wo(i){const t=(E,x,b)=>i.on(E,x,b),e=(E,...x)=>i.fire(E,...x);let n,o=Date.now()+36e5,a=null,s,r={index:0,version:0},c={version:0},p=!1;const u=e(et);t(ji,()=>s?.aabbCenter||new l.Vector3);let d;const f=new Promise(E=>d=E);t(Yt,async()=>{const E=e(_);let x=ut?E.maxRenderCountOfMobile:E.maxRenderCountOfPc;if(!E.bigSceneMode){let b=await f;x=Math.min(b,x)+10240}return x}),t(jt,async E=>{const x=e(_);if(x.bigSceneMode)return 1;let b=ut?x.maxRenderCountOfMobile:x.maxRenderCountOfPc,P=await f;if(b=Math.min(P,b),!s.dataShDegree)return 1;if(E>=3){if(s.dataShDegree<3)return 1}else if(E>=1){if(s.dataShDegree<1)return 1}else return 1;const T=1024*2;return Math.ceil(b/T)}),t(Sn,async()=>e(_).bigSceneMode?0:(await f,s.dataShDegree)),t(gn,async(E,x=!0)=>{try{await f;const b=!!s.header?.Flag2;a=await e(Wi,E,x,b),s&&(s.textWatermarkVersion=Date.now())}catch{console.info("failed to generate watermark")}}),t(rn,()=>{if(u)return r.version<=c.version?r.xyz:c.xyz;if(s?.status===R.FetchDone||s?.status===R.FetchAborted){if(s.activePoints&&s.activePoints.length===void 0)return s.activePoints;const E={},x=r.xyz;for(let b=0,P=x.length/3,T=0,I=0,F=0,W="";b<P;b++)T=x[b*3],I=x[b*3+1],F=x[b*3+2],W=`${Math.floor(T/2)*2+1},${Math.floor(I/2)*2+1},${Math.floor(F/2)*2+1}`,(E[W]=E[W]||[]).push(T,I,F);return s.activePoints=E}return r.xyz});async function y(E){if(n)return;if(s&&(s.status===R.Invalid||s.status===R.FetchFailed))return e(_).viewerEvents?.fire(j),e(fe,0)||e(st,{renderSplatCount:0,visibleSplatCount:0,modelSplatCount:0});if(!s||!s.downloadSize)return;const x=s.status!==R.FetchReady&&s.status!==R.Fetching;if(x){const b=Math.min(s.fetchLimit,s.downloadSplatCount);!s.notifyFetchStopDone&&(s.notifyFetchStopDone=!0)&&e(fe,b)}else e(Di,100*s.downloadSize/s.fileSize);s.downloadSplatCount&&(p||(p=!0,setTimeout(async()=>{await g(x),p=!1})))}async function g(E){if(n)return;const x=r,b=await e(Yt),P=a;let T=s.dataSplatCount,I=E?s.watermarkCount:0,F=s.meta.showWatermark&&E?(P?.byteLength||0)/32:0;if(s.renderSplatCount=T+I+F,s.renderSplatCount>=b&&(s.renderSplatCount=b,I=0,F=0,T>b&&(T=b)),e(st,{visibleSplatCount:s.renderSplatCount,modelSplatCount:s.modelSplatCount+F}),Date.now()-x.textureReadyTime<(ut?600:300)||s.smallSceneUploadDone&&s.lastTextWatermarkVersion==s.textWatermarkVersion)return;if(!x.version){e(Ai,(s.header?.Flag2?s.header.MaxTopY:s.header?.MinTopY)||0);let D=s.opts.format;s.opts.format=="spx"&&(D="spx"+(s.header.ExclusiveId?(" "+s.header.ExclusiveId).substring(0,6):"")),e(st,{scene:`small (${D})`})}s.lastTextWatermarkVersion=s.textWatermarkVersion,x.textureReady=!1;const W=1024*2,G=Math.ceil(2*b/W),h=new Uint32Array(W*G*4),M=new Float32Array(h.buffer),C=new Uint8Array(h.buffer);C.set(s.splatData.slice(0,T*32),0),I&&C.set(s.watermarkData.slice(0,I*32),T*32),F&&C.set(P.slice(0,F*32),(T+I)*32);const v=new Float32Array(s.renderSplatCount*3);for(let D=0,L=0;D<s.renderSplatCount;D++)v[D*3]=M[D*8],v[D*3+1]=M[D*8+1],v[D*3+2]=M[D*8+2];const k=Date.now();if(x.version=k,x.txdata=h,x.xyz=v,x.renderSplatCount=s.renderSplatCount,x.visibleSplatCount=s.downloadSplatCount+F,x.modelSplatCount=s.downloadSplatCount+F,x.watermarkCount=I+F,x.minX=s.minX,x.maxX=s.maxX,x.minY=s.minY,x.maxY=s.maxY,x.minZ=s.minZ,x.maxZ=s.maxZ,e(Ni,x,s.currentRadius,s.currentRadius),o=k,E&&!s.smallSceneUploadDone){s.smallSceneUploadDone=!0,e(Ki,s.sh12Data),e($i,s.sh3Data),s.sh12Data=null,s.sh3Data=null;const D=e(_);e(Je,D.shDegree===void 0?3:D.shDegree),e(rn)}e(st,{renderSplatCount:s.renderSplatCount})}function w(){n||(n=!0,s?.abortController?.abort(),s?.map?.clear(),s=null,a=null,r=null,c=null)}function m(E){if(E.opts.format==="spx")Uo(E);else if(E.opts.format==="splat")Fo(E);else if(E.opts.format==="ply")Oo(E);else if(E.opts.format==="spz")zo(E);else return!1;return!0}async function A(E,x){if(n)return;const b=e(_),P=ut?b.maxRenderCountOfMobile:b.maxRenderCountOfPc,T=e(et);if(E.fetchReload=Ro(x.updateDate||0),s=new bo(E,x),T&&x.autoCut){const W=x.pcDownloadLimitSplatCount||Eo,G=x.mobileDownloadLimitSplatCount||ko,h=ut?G:W;s.fetchLimit=Math.min(x.autoCut*x.autoCut*P+P,h)}else s.fetchLimit=P;const I=Date.now(),F=()=>{if(!s||s.status==R.Invalid||s.status==R.FetchFailed)return d(0);if(s.modelSplatCount>=0)d(s.modelSplatCount),setTimeout(()=>e(Ei),5);else{if(Date.now()-I>=3e3)return d(0);setTimeout(F,10)}};if(F(),!m(s)){console.error("Unsupported format:",E.format),e(fe,0);return}e(Ii)}t(Mi,async(E,x)=>await A(E,x)),t(xi,(E=1e4)=>Date.now()-o<E),t(Ti,()=>w()),e(Wt,async()=>await y(),()=>!n)}function Ho(){return`
        precision highp float;

        uniform highp usampler2D splatTexture0, splatTexture1, splatShTexture12, splatShTexture3;
        uniform vec2 focal, viewport;
        uniform int usingIndex, pointMode, bigSceneMode, showWaterMark, debugEffect, shDegree;
        uniform float topY, currentVisibleRadius, currentLightRadius, performanceNow;
        uniform vec4 markPoint, waterMarkColor;

        attribute uint splatIndex;

        varying vec4 vColor;
        varying vec3 vPosition;

        const float FactorSH = 0.0625;
        const uint MaskSH = 0x1Fu;
        const float SH_C1 = 0.4886025119029199;
        const float[5] SH_C2 = float[](1.0925484305920792f, -1.0925484305920792f, 0.31539156525252005f, -1.0925484305920792f, 0.5462742152960396f);
        const float[7] SH_C3 = float[](-0.5900435899266435f, 2.890611442640554f, -0.4570457994644658f, 0.3731763325901154f, -0.4570457994644658f, 1.445305721320277f, -0.5900435899266435f);

        vec3[15] readShDatas() {
            int shCnt = 0;
            float[45] fSHs;
            uvec4 rgb12 = texelFetch(splatShTexture12, ivec2((splatIndex & 0x7ffu), (splatIndex >> 11)), 0);
            if ( rgb12.a > 0u ) {
                shCnt = 3;
                fSHs[0] = float((rgb12.r >> 27) & MaskSH) * FactorSH - 1.0;
                fSHs[1] = float((rgb12.r >> 22) & MaskSH) * FactorSH - 1.0;
                fSHs[2] = float((rgb12.r >> 17) & MaskSH) * FactorSH - 1.0;
                fSHs[3] = float((rgb12.r >> 12) & MaskSH) * FactorSH - 1.0;
                fSHs[4] = float((rgb12.r >> 7) & MaskSH) * FactorSH - 1.0;
                fSHs[5] = float((rgb12.r >> 2) & MaskSH) * FactorSH - 1.0;
                fSHs[6] = float(( (rgb12.r << 3) | (rgb12.g >> 29) ) & MaskSH) * FactorSH - 1.0;
                fSHs[7] = float((rgb12.g >> 24) & MaskSH) * FactorSH - 1.0;
                fSHs[8] = float((rgb12.g >> 19) & MaskSH) * FactorSH - 1.0;

                if (shDegree > 1) {
                    shCnt = 8;
                    fSHs[9]  = float((rgb12.g >> 14) & MaskSH) * FactorSH - 1.0;
                    fSHs[10] = float((rgb12.g >> 9) & MaskSH) * FactorSH - 1.0;
                    fSHs[11] = float((rgb12.g >> 4) & MaskSH) * FactorSH - 1.0;
                    fSHs[12] = float(( (rgb12.g << 1) | (rgb12.b >> 31) ) & MaskSH) * FactorSH - 1.0;
                    fSHs[13] = float((rgb12.b >> 26) & MaskSH) * FactorSH - 1.0;
                    fSHs[14] = float((rgb12.b >> 21) & MaskSH) * FactorSH - 1.0;
                    fSHs[15] = float((rgb12.b >> 16) & MaskSH) * FactorSH - 1.0;
                    fSHs[16] = float((rgb12.b >> 11) & MaskSH) * FactorSH - 1.0;
                    fSHs[17] = float((rgb12.b >> 6) & MaskSH) * FactorSH - 1.0;
                    fSHs[18] = float((rgb12.b >> 1) & MaskSH) * FactorSH - 1.0;
                    fSHs[19] = float(( (rgb12.b << 4) | (rgb12.a >> 28) ) & MaskSH) * FactorSH - 1.0;
                    fSHs[20] = float(((rgb12.a >> 23) & MaskSH)) * FactorSH - 1.0;
                    fSHs[21] = float((rgb12.a >> 18) & MaskSH) * FactorSH - 1.0;
                    fSHs[22] = float((rgb12.a >> 13) & MaskSH) * FactorSH - 1.0;
                    fSHs[23] = float((rgb12.a >> 8) & MaskSH) * FactorSH - 1.0;

                    if (shDegree > 2) {
                        uvec4 rgb3 = texelFetch(splatShTexture3, ivec2(splatIndex & 0x7ffu, splatIndex >> 11), 0);
                        if ( rgb3.a > 0u ) {
                            shCnt = 15;
                            fSHs[24] = float((rgb3.r >> 27) & MaskSH) * FactorSH - 1.0;
                            fSHs[25] = float((rgb3.r >> 22) & MaskSH) * FactorSH - 1.0;
                            fSHs[26] = float((rgb3.r >> 17) & MaskSH) * FactorSH - 1.0;
                            fSHs[27] = float((rgb3.r >> 12) & MaskSH) * FactorSH - 1.0;
                            fSHs[28] = float((rgb3.r >> 7) & MaskSH) * FactorSH - 1.0;
                            fSHs[29] = float((rgb3.r >> 2) & MaskSH) * FactorSH - 1.0;
                            fSHs[30] = float(( (rgb3.r << 3) | (rgb3.g >> 29) ) & MaskSH) * FactorSH - 1.0;
                            fSHs[31] = float((rgb3.g >> 24) & MaskSH) * FactorSH - 1.0;
                            fSHs[32] = float((rgb3.g >> 19) & MaskSH) * FactorSH - 1.0;
                            fSHs[33]  = float((rgb3.g >> 14) & MaskSH) * FactorSH - 1.0;
                            fSHs[34] = float((rgb3.g >> 9) & MaskSH) * FactorSH - 1.0;
                            fSHs[35] = float((rgb3.g >> 4) & MaskSH) * FactorSH - 1.0;
                            fSHs[36] = float(( (rgb3.g << 1) | (rgb3.b >> 31) ) & MaskSH) * FactorSH - 1.0;
                            fSHs[37] = float((rgb3.b >> 26) & MaskSH) * FactorSH - 1.0;
                            fSHs[38] = float((rgb3.b >> 21) & MaskSH) * FactorSH - 1.0;
                            fSHs[39] = float((rgb3.b >> 16) & MaskSH) * FactorSH - 1.0;
                            fSHs[40] = float((rgb3.b >> 11) & MaskSH) * FactorSH - 1.0;
                            fSHs[41] = float((rgb3.b >> 6) & MaskSH) * FactorSH - 1.0;
                            fSHs[42] = float((rgb3.b >> 1) & MaskSH) * FactorSH - 1.0;
                            fSHs[43] = float(( (rgb3.b << 4) | (rgb3.a >> 28) ) & MaskSH) * FactorSH - 1.0;
                            fSHs[44] = float((rgb3.a >> 23) & MaskSH) * FactorSH - 1.0;
                        }
                    }
                }
            }

            vec3[15] sh;
            for (int i = 0; i < 15; ++i) {
                sh[i] = i < shCnt ? vec3(fSHs[i*3], fSHs[i*3 + 1], fSHs[i*3 + 2]) : vec3(0.0);
            }
            return sh;
        }

        // https://github.com/graphdeco-inria/gaussian-splatting/blob/main/utils/sh_utils.py
        vec3 evalSH(in vec3 v3Cen) {
            vec3 dir = normalize(v3Cen - cameraPosition);
            float x = dir.x;
            float y = dir.y;
            float z = dir.z;

            vec3[15] sh = readShDatas();
            vec3 result = SH_C1 * (-sh[0] * y + sh[1] * z - sh[2] * x);

            if (shDegree > 1) {
                float xx = x * x;
                float yy = y * y;
                float zz = z * z;
                float xy = x * y;
                float yz = y * z;
                float xz = x * z;

                result +=
                    sh[3] * (SH_C2[0] * xy) +
                    sh[4] * (SH_C2[1] * yz) +
                    sh[5] * (SH_C2[2] * (2.0 * zz - xx - yy)) +
                    sh[6] * (SH_C2[3] * xz) +
                    sh[7] * (SH_C2[4] * (xx - yy));

                if (shDegree > 2) {
                    result +=
                        sh[8]  * (SH_C3[0] * y * (3.0 * xx - yy)) +
                        sh[9]  * (SH_C3[1] * xy * z) +
                        sh[10] * (SH_C3[2] * y * (4.0 * zz - xx - yy)) +
                        sh[11] * (SH_C3[3] * z * (2.0 * zz - 3.0 * xx - 3.0 * yy)) +
                        sh[12] * (SH_C3[4] * x * (4.0 * zz - xx - yy)) +
                        sh[13] * (SH_C3[5] * z * (xx - yy)) +
                        sh[14] * (SH_C3[6] * x * (xx - 3.0 * yy));
                }
            }
            return result;
        }

        void main () {
            uvec4 cen, cov3d;
            if (bigSceneMode == 1) {
                if (usingIndex == 0){
                    cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
                    cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
                }else{
                    cen = texelFetch(splatTexture1, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
                    cov3d = texelFetch(splatTexture1, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
                }
            } else {
                cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
                cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
            }

            int waterMarkValue = int((cen.w & 65536u) >> 16u);
            int cenState = int(cen.w & 65535u);

            vec3 v3Cen = uintBitsToFloat(cen.xyz);

            if ( waterMarkValue == 1 && debugEffect == 1) {
                // 水印动画
                v3Cen.y += sin(performanceNow*0.002 + v3Cen.x) * 0.1;
            }

            vec4 cam = modelViewMatrix * vec4(v3Cen, 1.0);
            vec4 pos2d = projectionMatrix * cam;
            float clip = 1.2 * pos2d.w;
            if (pos2d.z < -clip || pos2d.x < -clip || pos2d.x > clip || pos2d.y < -clip || pos2d.y > clip
                || waterMarkValue == 1 && (showWaterMark == 0 || pointMode == 1) ) {
                gl_Position = vec4(0.0, 0.0, 2.0, 1.0);
                return;
            }

            float currentRadius = length(vec3(0.0, topY, 0.0) - v3Cen);
            if ( currentVisibleRadius > 0.0 && currentRadius > currentVisibleRadius ) {
                gl_Position = vec4(0.0, 0.0, 2.0, 1.0);
                return;
            }

            vec2 uh1 = unpackHalf2x16(cov3d.x), uh2 = unpackHalf2x16(cov3d.y), uh3 = unpackHalf2x16(cov3d.z);
            mat3 Vrk = mat3(uh1.x, uh1.y, uh2.x, uh1.y, uh2.y, uh3.x, uh2.x, uh3.x, uh3.y);

            float ZxZ = cam.z * cam.z;
            mat3 J_m3 = mat3(
                focal.x / cam.z, 0.0, -(focal.x * cam.x) / ZxZ,
                0.0, focal.y / cam.z, -(focal.y * cam.y) / ZxZ,
                0.0, 0.0, 0.0
            );

            mat3 T_m3 = transpose(mat3(modelViewMatrix)) * J_m3;
            mat3 cov2d = transpose(T_m3) * Vrk * T_m3;

            cov2d[0][0] += 0.3;
            cov2d[1][1] += 0.3;
            vec3 cov2Dv = vec3(cov2d[0][0], cov2d[0][1], cov2d[1][1]);
            float eigenValue1 =  0.5 * (cov2Dv.x + cov2Dv.z) + sqrt((cov2Dv.x + cov2Dv.z) * (cov2Dv.x + cov2Dv.z) / 4.0 - (cov2Dv.x * cov2Dv.z - cov2Dv.y * cov2Dv.y));
            float eigenValue2 = max( 0.5 * (cov2Dv.x + cov2Dv.z) - sqrt((cov2Dv.x + cov2Dv.z) * (cov2Dv.x + cov2Dv.z) / 4.0 - (cov2Dv.x * cov2Dv.z - cov2Dv.y * cov2Dv.y)), 0.0);
            float eigenValueOrig1 = eigenValue1;
            float eigenValueOrig2 = eigenValue2;

            int lightColorFlag = 0;
            if ( waterMarkValue == 0 ) {
                if ( pointMode == 1 ) {
                    eigenValue1 = eigenValue2 = 0.5;
                }

                if ( bigSceneMode == 0 && currentLightRadius > 0.0 ) {
                    // 仅小场景支持光圈过渡效果
                    if ( currentRadius < currentLightRadius && currentRadius > currentLightRadius * 0.9 ) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                        lightColorFlag = 1;
                    }
                    if ( currentRadius < currentLightRadius * 0.9 ){
                        if ( pointMode == 1 ){
                            eigenValue1 = eigenValueOrig1;
                            eigenValue2 = eigenValueOrig2;
                        } else {
                            eigenValue1 = eigenValue2 = 0.5;
                        }
                    }
                }
            }

            int iSelectPoint = 0;
            if (markPoint.w > 0.0 && length(vec3(markPoint.xyz) - v3Cen) < 0.000001){
                iSelectPoint = 1;
            }

            vPosition = vec3(position.xy, -1.0);
            vec2 eigenVector1 = normalize(vec2(cov2Dv.y, eigenValue1 - cov2Dv.x));
            if (iSelectPoint == 1){
                vColor = vec4(1.0, 1.0, 0.0, 1.0);
                eigenValue1 = eigenValue2 = 10.0;
                eigenVector1 = normalize(vec2(10.0, eigenValue1 - 10.0));
                vPosition.z = 1.0;
            } else if ( lightColorFlag == 1 ) {
                vColor = vec4(1.0, 1.0, 1.0, 0.2);
            } else if ( waterMarkValue == 1 ) {
                vColor = waterMarkColor;
            } else {
                vColor = vec4( float(cov3d.w & 0xFFu) / 255.0, float((cov3d.w >> 8) & 0xFFu) / 255.0, float((cov3d.w >> 16) & 0xFFu) / 255.0, float(cov3d.w >> 24) / 255.0 );
                if (shDegree > 0) {
                    vColor.rgb += evalSH(v3Cen);
                    vColor.rgb = clamp(vColor.rgb, vec3(0.), vec3(1.));
                }
            }

            vec2 eigenVector2 = vec2(eigenVector1.y, -eigenVector1.x);
            vec2 majorAxis = eigenVector1 * min(sqrt(2.0 * eigenValue1), 1024.0);
            vec2 minorAxis = eigenVector2 * min(sqrt(2.0 * eigenValue2), 1024.0);

            vec2 v2Center = vec2(pos2d) / pos2d.w;  // NDC坐标
            gl_Position = vec4(
                v2Center
                + vPosition.x * majorAxis / viewport
                + vPosition.y * minorAxis / viewport
                , 1.0, 1.0);

        }
    `}function Yo(){return`
        precision highp float;

        uniform float lightFactor;

        varying vec4 vColor;
        varying vec3 vPosition;

        void main(){
            float dtPos = -dot(vPosition.xy, vPosition.xy);
            if (dtPos < -4.0) discard;

            dtPos = vPosition.z > 0.0 ? 1.0 : exp(dtPos) * vColor.a;
            gl_FragColor = vec4(lightFactor * vColor.rgb, dtPos);
        }

    `}function Go(){return`
        uniform vec2 viewport;
        uniform vec3 realFocusPosition;

        varying vec4 ndcPosition;
        varying vec4 ndcCenter;
        varying vec4 ndcFocusPosition;
        varying float vAngle;

        void main() {
            vec4 viewPosition = modelViewMatrix * vec4(position.xyz, 1.0);
            vec4 viewCenter = modelViewMatrix * vec4(0.0, 0.0, 0.0, 1.0);

            vec4 viewFocusPosition = modelViewMatrix * vec4(realFocusPosition, 1.0);

            ndcPosition = projectionMatrix * viewPosition;
            ndcPosition = ndcPosition * vec4(1.0 / ndcPosition.w);
            ndcCenter = projectionMatrix * viewCenter;
            ndcCenter = ndcCenter * vec4(1.0 / ndcCenter.w);

            ndcFocusPosition = projectionMatrix * viewFocusPosition;
            ndcFocusPosition = ndcFocusPosition * vec4(1.0 / ndcFocusPosition.w);


            // 计算角度
            vec2 screenPosition = vec2(ndcPosition) * viewport;
            vec2 screenCenter = vec2(ndcCenter) * viewport;
            vec2 screenVec = screenPosition - screenCenter;
            float angle = atan(screenVec.y, screenVec.x);

            // 将角度从弧度转换为度数
            vAngle = angle * (180.0 / 3.14159265) + 90.0;

            gl_Position = projectionMatrix * viewPosition;

        }
    `}function Xo(){return`
        uniform vec3 cycleColor;
        uniform vec2 viewport;
        uniform float opacity;

        varying vec4 ndcPosition;
        varying vec4 ndcCenter;
        varying vec4 ndcFocusPosition;
        varying float vAngle;

        void main() {
            vec2 screenPosition = vec2(ndcPosition) * viewport;
            vec2 screenCenter = vec2(ndcCenter) * viewport;

            vec2 screenVec = screenPosition - screenCenter;

            float projectedRadius = length(screenVec);

            float lineWidth = 0.0005 * viewport.y;
            float aaRange = 0.0025 * viewport.y;
            float radius = 0.06 * viewport.y;
            float radDiff = abs(projectedRadius - radius) - lineWidth;
            float alpha = 1.0 - clamp(radDiff / 5.0, 0.0, 1.0);

            // 将圆分成3段显示
            float segmentAngle = 120.0;
            if (mod(vAngle, segmentAngle) > segmentAngle * 0.8) {
                alpha = 0.0;
            }

            gl_FragColor = vec4(cycleColor.rgb, alpha * opacity);
        }
    `}const ds="currentVisibleRadius",us="currentLightRadius",ps="realFocusPosition",Fn="splatShTexture12",Rn="splatShTexture3",fs="performanceNow",No="waterMarkColor",hs="showWaterMark",Bn="splatTexture0",Un="splatTexture1",gs="bigSceneMode",ms="lightFactor",ys="debugEffect",As="usingIndex",Ko="cycleColor",$o="splatIndex",ws="pointMode",vs="markPoint",Ss="shDegree",ne="viewport",Vn="opacity",bs="focal",Cs="topY";let Y=0;Y++;const Ms=`$${Y++}`;Y++,Y++,Y++;const xs=`$${Y++}`,Zo=`$${Y++}`,Ts=`$${Y++}`,jo=`$${Y++}`,qo=`$${Y++}`,Jo=`$${Y++}`,ta=`$${Y++}`,ea=`$${Y++}`;Y++;const na=`$${Y++}`,ia=`$${Y++}`;Y++;const sa=`$${Y++}`;Y++;const oa=`$${Y++}`;Y++;const aa=`$${Y++}`,ra=`$${Y++}`,ca=`$${Y++}`,la=`$${Y++}`,da=`$${Y++}`,ua=`$${Y++}`,pa=`$${Y++}`,fa=`$${Y++}`;Y++;function ha(i){let t=!1;const e=(d,f,y)=>i.on(d,f,y),n=(d,...f)=>i.fire(d,...f);let o=0,a=0;const s=[];let r=0;e(Zi,()=>r),e(di,async()=>{const d=new l.InstancedBufferGeometry;d.setIndex([0,1,2,0,2,3]);const f=new Float32Array(4*3),y=new l.BufferAttribute(f,3);d.setAttribute("position",y),y.setXYZ(0,-2,-2,0),y.setXYZ(1,-2,2,0),y.setXYZ(2,2,2,0),y.setXYZ(3,2,-2,0),y.needsUpdate=!0;let g=new l.InstancedBufferGeometry().copy(d);const w=await n(Yt);if(t)return;const m=new Uint32Array(w),A=new l.InstancedBufferAttribute(m,1,!1);return A.setUsage(l.DynamicDrawUsage),A.needsUpdate=!0,g.setAttribute($o,A),g.instanceCount=0,e(hi,(E,x,b,P)=>{n(mi,x),m.set(E,0),A.clearUpdateRanges(),A.addUpdateRange(0,E.length),A.needsUpdate=!0,g.instanceCount=E.length,n(N),n(st,{sortTime:`${b} / ${Date.now()-P}`})}),e(to,()=>g),e(vi,()=>{A.array=null,g.dispose()}),g}),e(ui,async()=>{const d=await n(Yt);if(t)return;const f=1024*2,y=Math.ceil(2*d/f),g=n(_),w=new l.ShaderMaterial({uniforms:n(li),vertexShader:Ho(),fragmentShader:Yo(),transparent:!0,alphaTest:1,blending:l.NormalBlending,depthTest:g.depthTest!==!1,depthWrite:!1,side:l.DoubleSide}),m=new Uint32Array(f*y*4);let A=new l.DataTexture(m,f,y,l.RGBAIntegerFormat,l.UnsignedIntType);A.internalFormat="RGBA32UI",A.needsUpdate=!0,w.uniforms[Bn].value=A;const E=n(et)?y:1,x=new Uint32Array(f*E*4);let b=new l.DataTexture(x,f,E,l.RGBAIntegerFormat,l.UnsignedIntType);b.internalFormat="RGBA32UI",b.needsUpdate=!0,w.uniforms[Un].value=b;const P=await n(jt,1),T=new Uint32Array(f*P*4);let I=new l.DataTexture(T,f,P,l.RGBAIntegerFormat,l.UnsignedIntType);I.internalFormat="RGBA32UI",I.needsUpdate=!0,w.uniforms[Fn].value=I;const F=await n(jt,3),W=new Uint32Array(f*F*4);let G=new l.DataTexture(W,f,F,l.RGBAIntegerFormat,l.UnsignedIntType);G.internalFormat="RGBA32UI",G.needsUpdate=!0,w.uniforms[Rn].value=G,w.needsUpdate=!0;let h=!1;e(gi,C=>{if(!n(et)){if(h&&!C.renderSplatCount)return;h=!C.renderSplatCount}const v=C.txdata;C.txdata=null;const k=new l.DataTexture(v,f,y,l.RGBAIntegerFormat,l.UnsignedIntType);k.onUpdate=()=>{C.textureReady=!0,C.textureReadyTime=Date.now(),u(C),n(_i,C.renderSplatCount)},k.internalFormat="RGBA32UI",k.needsUpdate=!0,C.index?(w.uniforms[Un].value=k,b=k):(w.uniforms[Bn].value=k,A=k),w.needsUpdate=!0,n(N)}),e(Ki,async C=>{if(n(et)||!C||!C.length)return;const v=new Uint32Array(f*await n(jt,1)*4),k=new Uint8Array(v.buffer);for(let L=0,B=0;L<C.length;L++)k.set(C[L],B),B+=C[L].byteLength;const D=new l.DataTexture(v,f,P,l.RGBAIntegerFormat,l.UnsignedIntType);D.internalFormat="RGBA32UI",D.needsUpdate=!0,w.uniforms[Fn].value=D,w.needsUpdate=!0,n(N)}),e($i,async C=>{if(n(et)||!C||!C.length)return;const v=new Uint32Array(f*await n(jt,3)*4),k=new Uint8Array(v.buffer);for(let L=0,B=0;L<C.length;L++)k.set(C[L],B),B+=C[L].byteLength;const D=new l.DataTexture(v,f,P,l.RGBAIntegerFormat,l.UnsignedIntType);D.internalFormat="RGBA32UI",D.needsUpdate=!0,w.uniforms[Rn].value=D,w.needsUpdate=!0,n(N)}),e(fi,()=>w),e(Ze,()=>{const C=n(O),{width:v,height:k}=n(Tt),D=Math.abs(C.projectionMatrix.elements[0])*.5*v,L=Math.abs(C.projectionMatrix.elements[5])*.5*k,B=n(fi);B.uniforms[bs].value.set(D,L),B.uniformsNeedUpdate=!0,n(N)}),e(je,()=>{const{width:C,height:v}=n(Tt);w.uniforms[ne].value.set(C,v),w.uniformsNeedUpdate=!0,n(N)}),e(mi,C=>{w.uniforms[As].value=C,w.uniformsNeedUpdate=!0,n(N)}),e(Ft,C=>{const v=n(_);C===void 0&&(C=!v.pointcloudMode),w.uniforms[ws].value=C?1:0,w.uniformsNeedUpdate=!0,v.pointcloudMode=C,n(N),v.viewerEvents&&(v.viewerEvents.fire(_).pointcloudMode=C)}),e(yi,C=>{w.uniforms[gs].value=C?1:0,w.uniformsNeedUpdate=!0;const v=n(_);v.bigSceneMode=C,n(N)}),e(Gt,C=>{w.uniforms[ms].value=C,w.uniformsNeedUpdate=!0;const v=n(_);v.lightFactor=C,n(N)});let M=!1;return e(Ai,C=>{n(et)||M||(M=!0,w.uniforms[Cs].value=C,w.uniformsNeedUpdate=!0,n(N))}),e(oe,C=>{w.uniforms[ds].value=C,w.uniformsNeedUpdate=!0,n(N)}),e(ae,C=>{w.uniforms[us].value=C,w.uniformsNeedUpdate=!0,n(N)}),e(re,(C,v,k,D)=>{w.uniforms[vs].value=[C,v,k,D?1:-1],w.uniformsNeedUpdate=!0,n(N)}),e(qe,(C=!0)=>{w.uniforms[hs].value=C?1:0,w.uniformsNeedUpdate=!0,n(N)}),e(wi,C=>{w.uniforms[fs].value=C,w.uniformsNeedUpdate=!0}),e(eo,C=>{w.uniforms[ys].value=C,w.uniformsNeedUpdate=!0}),e(Je,async C=>{if(n(et))return;const v=await n(Sn);C<0&&(C=0),C>v&&(C=v),r=C,w.uniforms[Ss].value=C,w.uniformsNeedUpdate=!0,n(st,{shDegree:`${C} / max ${v}`}),n(N)}),e(Si,()=>{w.dispose(),A&&A.dispose(),b&&b.dispose(),I&&I.dispose(),G&&G.dispose()}),w}),e(pi,async()=>{const d=new l.Mesh(await n(di),await n(ui));return n(Ze),n(je),n(yi,n(et)),n(Ft,n(en)),d});function c(){n(Ze),n(je)}window.addEventListener("resize",c),e(ki,()=>{t=!0,window.removeEventListener("resize",c),n(vi),n(Si)}),e(Ei,async()=>{if(n(et))return;let d=.01,f=.01,y=!1,g=0;n(oe,f),n(Wt,()=>{if(t||a<=f)return;f+=(a-f)*d,n(oe,f);let w=n(Pi);w&&!g&&(g=Date.now());let m=f/o;w&&(m>.9||Date.now()-g>2500)?(n(en)&&n(nn,!0),n(oe,0),y=!0):w&&m>.7?d=Math.min(d*1.2,.3):w&&m>.5?d=Math.min(d*1.2,.2):m>.4&&(d=Math.min(d*1.05,.1))},()=>!t&&!y,3)}),e(nn,(d=!1)=>{if(n(et))return;const f=n(_);if(f.disableTransitionEffect){n(Ft,!f.pointcloudMode),n(ae,0),d&&n(_)?.viewerEvents?.fire(Ut),n(_)?.viewerEvents?.fire(vn);return}for(;s.length;)s.pop().stop=!0;const y=o*.001;let g={currentPointMode:f.pointcloudMode,stepRate:.0015,currentLightRadius:y,stop:!1};s.push(g),n(Wt,()=>{t||(g.currentLightRadius+=o*g.stepRate,n(ae,g.currentLightRadius),g.currentLightRadius>o?(n(Ft,!g.currentPointMode),n(ae,0),g.stop=!0,s.length===1&&s[0]===g&&s.pop(),d&&n(_)?.viewerEvents?.fire(Ut),n(_)?.viewerEvents?.fire(vn)):g.currentLightRadius/o<.4?g.stepRate=Math.min(g.stepRate*1.02,.03):g.stepRate*=1.04)},()=>!t&&!g.stop)}),e(li,()=>({[Bn]:{type:"t",value:null},[Un]:{type:"t",value:null},[Fn]:{type:"t",value:null},[Rn]:{type:"t",value:null},[bs]:{type:"v2",value:new l.Vector2},[ne]:{type:"v2",value:new l.Vector2},[As]:{type:"int",value:0},[ws]:{type:"int",value:0},[ys]:{type:"int",value:1},[gs]:{type:"int",value:0},[Ss]:{type:"int",value:0},[ms]:{type:"float",value:1},[Cs]:{type:"float",value:0},[ds]:{type:"float",value:0},[us]:{type:"float",value:0},[vs]:{type:"v4",value:new l.Vector4(0,0,0,-1)},[fs]:{type:"float",value:performance.now()},[No]:{type:"v4",value:new l.Vector4(1,1,0,.5)},[hs]:{type:"int",value:1}}));const p=n(ei);p.onmessage=d=>{const f=d.data;f[Ms]&&n(hi,f[Ms],f[xs],f[ta],f[Jo],f[Ts])},e(Ni,(d,f,y)=>{n(et)||(a=f,o=y),n(gi,d)});function u(d){const f=d.xyz.slice(0);p.postMessage({[ea]:!0,[oa]:f,[fa]:d.watermarkCount,[xs]:d.index,[Zo]:d.version,[Ts]:d.renderSplatCount,[jo]:d.visibleSplatCount,[qo]:d.modelSplatCount,[aa]:d.minX,[ra]:d.maxX,[ca]:d.minY,[la]:d.maxY,[da]:d.minZ,[ua]:d.maxZ},[f.buffer])}}function ga(i){const t=(o,...a)=>i.fire(o,...a),e=(o,a,s)=>i.on(o,a,s),n=new Set(Co.split(""));e(Wi,async(o="",a=!0,s=!0)=>{const r=o.trim().substring(0,100);let c=await t(Jn,r),p=[];for(let x=0;x<c.length;x++){let b=[],P=c[x];for(let T=0;T<P.length;T++)b.push([(P[T]%20-10)*.02,((P[T]/20|0)-10)*.02]);p.push(b)}let u=[],d=r.split("");for(let x=0;x<d.length;x++)u[x]=n.has(d[x])?.22:.4;let f=d.length/2|0,y=u[f]/2,g=!(d.length%2),w=g?0:-y;for(let x=f-1;x>=0;x--){w-=u[x]/2;for(let b of p[x])b[0]+=w;w-=u[x]/2}y=u[f]/2,w=g?0:y;for(let x=p.length-f;x<p.length;x++){w+=u[x]/2;for(let b of p[x])b[0]+=w;w+=u[x]/2}let m=0;for(let x of p)m+=x.length;const A=new Uint8Array(m*z);let E=0;for(let x of p)for(let b of x)A.set(await Lo(b[0],b[1],a,s),z*E++);return A})}function On(i){const t=(e,n,o)=>i.on(e,n,o);t(Mt,e=>{e.url;const n={...e};delete n.url;const o=JSON.stringify(n,null,2);console.info(o)}),t(Jn,(e="")=>{const n="https://reall3d.com/gsfont/api/getGaussianText",o=new FormData;return o.append("text",e.substring(0,100)),o.append("ver",Pe),new Promise(a=>{fetch(n,{method:"POST",body:o}).then(s=>s.ok?s.json():{}).then(s=>s.success?a(JSON.parse(s.data)):a([])).catch(s=>a([]))})})}function ma(i){let t=!1;const e=(r,c,p)=>i.on(r,c,p),n=(r,...c)=>i.fire(r,...c);e(bi,()=>t=!0);const o=new Map,a=new Map;e(ce,()=>n(rt)&&o.set(Date.now(),1)),e(le,()=>n(rt)&&n(Lt,o)),e(de,()=>n(rt)&&a.set(Date.now(),1)),e(ue,()=>n(rt)&&n(Lt,a)),e(Et,()=>{t||(n(de),n(rt)&&n(st,{fov:n(Ht),position:n(kt,n(xt)),lookAt:n(kt,n(vt)),lookUp:n(kt,n(Pt))}))},!0);let s=0;e(Rt,()=>{t||(n(ii),n(rt)&&(n(ce),!(s++%5)&&n(st,{fps:n(le),realFps:n(ue)})))},!0),e(Lt,r=>{let c=[],p=Date.now(),u=0;for(const d of r.keys())p-d<=1e3?u++:c.push(d);return c.forEach(d=>r.delete(d)),Math.min(u,30)}),window.addEventListener("beforeunload",()=>n(pe))}function ya(i){const t={...i};return t.bigSceneMode??(t.bigSceneMode=!1),t.pointcloudMode??(t.pointcloudMode=!t.bigSceneMode),t.lightFactor??(t.lightFactor=1),t.name??(t.name=""),t.showWatermark??(t.showWatermark=!0),t}function ks(i){const t={...i};return t.position=t.position?[...t.position]:[0,-5,15],t.lookAt=t.lookAt?[...t.lookAt]:[0,0,0],t.lookUp=t.lookUp?[...t.lookUp]:[0,-1,0],t.fov??(t.fov=45),t.near??(t.near=.001),t.far??(t.far=1e3),t.enableDamping??(t.enableDamping=!0),t.autoRotate??(t.autoRotate=!0),t.enableZoom??(t.enableZoom=!0),t.enableRotate??(t.enableRotate=!0),t.enablePan??(t.enablePan=!0),t.enableKeyboard??(t.enableKeyboard=!0),t.bigSceneMode??(t.bigSceneMode=!1),t.pointcloudMode??(t.pointcloudMode=!t.bigSceneMode),t.lightFactor??(t.lightFactor=1.1),t.maxRenderCountOfMobile??(t.maxRenderCountOfMobile=t.bigSceneMode?256*1e4:384*10240),t.maxRenderCountOfPc??(t.maxRenderCountOfPc=t.bigSceneMode?320*1e4:384*1e4),t.debugMode??(t.debugMode=location.protocol==="http:"||/^test\./.test(location.host)),t.markMode??(t.markMode=!1),t.markVisible??(t.markVisible=!0),t.meterScale??(t.meterScale=1),t.background??(t.background="#000000"),t}function Aa(i){let t;i.root?t=typeof i.root=="string"?document.querySelector(i.root)||document.querySelector("#gsviewer"):i.root:t=document.querySelector("#gsviewer"),t||(t=document.createElement("div"),t.id="gsviewer",document.body.appendChild(t));let e=null;return i.renderer?e=i.renderer:(e=new l.WebGLRenderer({antialias:!1,stencil:!0,logarithmicDepthBuffer:!0,precision:"highp"}),e.setSize(t.clientWidth,t.clientHeight),e.setPixelRatio(Math.min(devicePixelRatio,2)),i.renderer=e),e.domElement.classList.add("gsviewer-canvas"),t.appendChild(e.domElement),e}function wa(i){let t=i.camera;if(!t){const e=i.renderer.domElement,n=e.width/e.height;let o=new l.Vector3().fromArray(i.lookUp),a=new l.Vector3().fromArray(i.lookAt),s=new l.Vector3().fromArray(i.position);t=new l.PerspectiveCamera(i.fov,n,i.near,i.far),t.position.copy(s),t.up.copy(o).normalize(),t.lookAt(a),i.camera=t}return i.camera}function va(i){const{renderer:t,scene:e}=i,n={renderer:t,scene:e};return n.viewerEvents=i.viewerEvents,n.debugMode=i.debugMode,n.renderer=i.renderer,n.scene=i.scene,n.controls=i.controls,n.bigSceneMode=i.bigSceneMode,n.pointcloudMode=i.pointcloudMode,n.maxRenderCountOfMobile=i.maxRenderCountOfMobile,n.maxRenderCountOfPc=i.maxRenderCountOfPc,n.lightFactor=i.lightFactor,n.shDegree=i.shDegree,n.disableTransitionEffect=i.disableTransitionEffect,n}function Sa(i){const t=(s,r,c)=>i.on(s,r,c),e=(s,...r)=>i.fire(s,...r),n="KGZ1bmN0aW9uKCl7InVzZSBzdHJpY3QiO2xldCBpPTA7aSsrO2NvbnN0IEQ9YCQke2krK31gO2krKyxpKyssaSsrO2NvbnN0IFc9YCQke2krK31gLHA9YCQke2krK31gLFM9YCQke2krK31gLHk9YCQke2krK31gLEM9YCQke2krK31gLEE9YCQke2krK31gLFI9YCQke2krK31gLEc9YCQke2krK31gO2krKztjb25zdCBIPWAkJHtpKyt9YCxKPWAkJHtpKyt9YDtpKys7Y29uc3QgVD1gJCR7aSsrfWA7aSsrO2NvbnN0IEs9YCQke2krK31gO2krKztjb25zdCBMPWAkJHtpKyt9YCxOPWAkJHtpKyt9YCxPPWAkJHtpKyt9YCxRPWAkJHtpKyt9YCxfPWAkJHtpKyt9YCxWPWAkJHtpKyt9YCxqPWAkJHtpKyt9YCxQPWAkJHtpKyt9YDtpKys7Y29uc3Qgbm49bmF2aWdhdG9yLnVzZXJBZ2VudC5pbmNsdWRlcygiTW9iaSIpLGI9c2VsZjtsZXQgWD17aW5kZXg6MCx2ZXJzaW9uOjB9LFk9e2luZGV4OjEsdmVyc2lvbjowfSx3PSExLFo7Y29uc3QgdG49LjI7bGV0IGssZj1bXSxNLHo9MCxCO2Z1bmN0aW9uIGFuKG4pe2lmKCF3KXJldHVybjtsZXQgYT1YLnZlcnNpb24+WS52ZXJzaW9uP1g6WTtpZighYS52ZXJzaW9uKXJldHVybjtjb25zdHt4eXo6dCxyZW5kZXJTcGxhdENvdW50Om8sdmlzaWJsZVNwbGF0Q291bnQ6bSxtb2RlbFNwbGF0Q291bnQ6RSx3YXRlcm1hcmtDb3VudDp4LGluZGV4OkYsdmVyc2lvbjp1fT1hO2lmKHo9PT11JiZNYXRoLmFicyhmWzJdLW5bMl0pK01hdGguYWJzKGZbNl0tbls2XSkrTWF0aC5hYnMoZlsxMF0tblsxMF0pK01hdGguYWJzKGZbMTRdLW5bMTRdKTx0bilyZXR1cm47Zj1uLHo9dTtsZXQgST1EYXRlLm5vdygpLGw7aWYoIW8pe2w9bmV3IFVpbnQzMkFycmF5KDApLGIucG9zdE1lc3NhZ2Uoe1tEXTpsLFtTXTpvLFt5XTptLFtDXTpFLFtXXTpGLFtwXTp1LFtSXTowLFtBXTpJfSxbbC5idWZmZXJdKTtyZXR1cm59bGV0IHE9MDtjb25zdCByPW8teDtsPW5ldyBVaW50MzJBcnJheShvKTtjb25zdHttYXhEZXB0aDp2LG1pbkRlcHRoOmR9PWVuKGEsayk7aWYodi1kPD0xZS01KWZvcihsZXQgcz0wO3M8bztzKyspbFtzXT1zO2Vsc2V7bGV0IHM9TWF0aC5taW4ociw2NTUzNSksZz0ocy0xKS8odi1kKSxjPW5ldyBJbnQzMkFycmF5KHMpO2ZvcihsZXQgZT0wLGg9MDtlPHI7ZSsrKWg9KCQobix0WzMqZV0sdFszKmUrMV0sdFszKmUrMl0pLWQpKmd8MCxjW01bZV09aF0rKztmb3IobGV0IGU9MTtlPHM7ZSsrKWNbZV0rPWNbZS0xXTtmb3IobGV0IGU9MDtlPHI7ZSsrKWxbLS1jW01bZV1dXT1lO2lmKHgpe3M9TWF0aC5taW4oTWF0aC5tYXgoeC84fDAsNTEyKSw2NTUzNSksZz0ocy0xKS8odi1kKSxjPW5ldyBJbnQzMkFycmF5KHMpO2ZvcihsZXQgZT1yLGg9MDtlPG87ZSsrKWg9KCQobix0WzMqZV0sdFszKmUrMV0sdFszKmUrMl0pLWQpKmd8MCxjW01bZS1yXT1oXSsrO2ZvcihsZXQgZT0xO2U8cztlKyspY1tlXSs9Y1tlLTFdO2ZvcihsZXQgZT0wO2U8eDtlKyspbFtyKy0tY1tNW2VdXV09citlfX1xPURhdGUubm93KCktSSxiLnBvc3RNZXNzYWdlKHtbRF06bCxbU106byxbeV06bSxbQ106RSxbV106RixbcF06dSxbQV06SSxbUl06cX0sW2wuYnVmZmVyXSl9ZnVuY3Rpb24gJChuLGEsdCxvKXtyZXR1cm4tKG5bMl0qYStuWzZdKnQrblsxMF0qbyl9ZnVuY3Rpb24gZW4obixhKXtsZXQgdD0tMS8wLG89MS8wLG09MDtyZXR1cm4gbT0kKGEsbi5taW5YLG4ubWluWSxuLm1pblopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSksbT0kKGEsbi5taW5YLG4ubWluWSxuLm1heFopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSksbT0kKGEsbi5taW5YLG4ubWF4WSxuLm1pblopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSksbT0kKGEsbi5taW5YLG4ubWF4WSxuLm1heFopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSksbT0kKGEsbi5tYXhYLG4ubWluWSxuLm1pblopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSksbT0kKGEsbi5tYXhYLG4ubWluWSxuLm1heFopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSksbT0kKGEsbi5tYXhYLG4ubWF4WSxuLm1pblopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSksbT0kKGEsbi5tYXhYLG4ubWF4WSxuLm1heFopLHQ9TWF0aC5tYXgodCxtKSxvPU1hdGgubWluKG8sbSkse21heERlcHRoOnQsbWluRGVwdGg6b319Y29uc3QgVT0oKT0+e2lmKCFaKXtaPSEwO2NvbnN0IG49azthbihuKSxzZXRUaW1lb3V0KCgpPT4hKFo9ITEpJiZuIT09ayYmVSgpKX19O2Iub25tZXNzYWdlPW49Pntjb25zdCBhPW4uZGF0YTtpZihhW0ddKXtsZXQgdD0hQnx8YVtXXT09PTA/WDpZO3QubWluWD1hW0xdLHQubWF4WD1hW05dLHQubWluWT1hW09dLHQubWF4WT1hW1FdLHQubWluWj1hW19dLHQubWF4Wj1hW1ZdLHQueHl6PW5ldyBGbG9hdDMyQXJyYXkoYVtLXS5idWZmZXIpLHQud2F0ZXJtYXJrQ291bnQ9YVtQXSx0LnZlcnNpb249YVtwXSx0LnJlbmRlclNwbGF0Q291bnQ9YVtTXSx0LnZpc2libGVTcGxhdENvdW50PWFbeV0sdC5tb2RlbFNwbGF0Q291bnQ9YVtDXSx0LnRleHR1cmVSZWFkeT0hMCx0LnRleHR1cmVSZWFkeVRpbWU9RGF0ZS5ub3coKX1lbHNlIGFbVF0/KGs9YVtUXSxVKCkpOmFbal0mJihCPWFbSF0sTT1uZXcgSW50MzJBcnJheShhW0pdKSx3PSEwKX19KSgpOwo=",o=URL.createObjectURL(new Blob([atob(n)],{type:"text/javascript"})),a=new Worker(new URL(o,typeof document>"u"&&typeof location>"u"?require("url").pathToFileURL(__filename).href:typeof document>"u"?location.href:He&&He.tagName.toUpperCase()==="SCRIPT"&&He.src||new URL("pkg.umd.cjs",document.baseURI).href),{type:"module"});t(ei,()=>a),t(ai,()=>a.postMessage({[sa]:e(oi)})),t(ri,()=>a.terminate()),(async()=>a.postMessage({[pa]:!0,[ia]:await e(Yt),[na]:e(et)}))()}class ct extends l.Mesh{constructor(t){super(),this.isSplatMesh=!0,this.disposed=!1;const e=new xn,n=(r,c,p)=>e.on(r,c,p),o=(r,...c)=>e.fire(r,...c),a=ya(t),s=a.controls.object;n(_,()=>a),n(_t,()=>a.renderer.domElement),n(O,()=>s),n(Ht,()=>s.fov),n(xt,(r=!1)=>r?s.position.clone():s.position),n(vt,(r=!1)=>r?a.controls.target.clone():a.controls.target),n(oi,()=>s.projectionMatrix.clone().multiply(s.matrixWorldInverse).multiply(this.matrix).toArray()),n(js,()=>s.projectionMatrix.clone().multiply(s.matrixWorldInverse)),n(ho,()=>s.getWorldDirection(new l.Vector3).toArray()),n(Xt,()=>a.renderer),n(V,()=>a.scene),n(et,()=>a.bigSceneMode),n(en,()=>a.pointcloudMode),n(wt,()=>this),n(N,()=>a.viewerEvents?.fire(Q)),_n(e),On(e),Wo(e),Sa(e),ha(e),ga(e),this.name=`${a.name||this.id}`,this.events=e,this.opts=a,(async()=>(this.copy(await e.fire(pi)),a.matrix&&this.applyMatrix4(a.matrix),this.frustumCulled=!1,this.onBeforeRender=()=>{o(ai),o(wi,performance.now())},this.onAfterRender=()=>{o(xi,1e4)&&o(N)}))()}options(t){if(this.disposed)return;const e=(o,...a)=>this.events.fire(o,...a),n=this.opts;return t&&(t.pointcloudMode!==void 0&&e(Ft,t.pointcloudMode),t.lightFactor!==void 0&&e(Gt,t.lightFactor),t.maxRenderCountOfMobile!==void 0&&(n.maxRenderCountOfMobile=t.maxRenderCountOfMobile),t.maxRenderCountOfPc!==void 0&&(n.maxRenderCountOfPc=t.maxRenderCountOfPc),e(N)),{...n}}async addModel(t,e){this.disposed||(this.meta=e,await this.events.fire(Mi,t,e))}fire(t,...e){if(!this.disposed)return this.events.fire(t,...e)}dispose(){if(this.disposed)return;this.disposed=!0;const t=(e,...n)=>this.events.fire(e,...n);t(Ct,this),t(V).remove(this),t(tn),t(Ti),t(ri),t(ki),this.events.clear(),this.events=null,this.opts=null,this.onAfterRender=null}}class ba extends l.Object3D{constructor(t=new l.Vector3(0,0,1),e=new l.Vector3(0,0,0),n=1,o=.1,a=16776960,s=n*.2,r=s*.2){super(),this._axis=new l.Vector3,this.type="ArrowHelper";const c=new l.CylinderGeometry(o,o,n,32);c.translate(0,n/2,0);const p=new l.CylinderGeometry(0,r,s,32);p.translate(0,n,0),this.position.copy(e);const u=new l.MeshBasicMaterial({color:a,toneMapped:!1});u.side=l.DoubleSide,this.line=new l.Mesh(c,u),this.line.matrixAutoUpdate=!1,this.line.ignoreIntersect=!0,this.add(this.line);const d=new l.MeshBasicMaterial({color:a,toneMapped:!1});d.side=l.DoubleSide,this.cone=new l.Mesh(p,d),this.cone.matrixAutoUpdate=!1,this.cone.ignoreIntersect=!0,this.add(this.cone),this.setDirection(t),this.renderOrder=99999}setDirection(t){if(t.y>.99999)this.quaternion.set(0,0,0,1);else if(t.y<-.99999)this.quaternion.set(1,0,0,0);else{this._axis.set(t.z,0,-t.x).normalize();const e=Math.acos(t.y);this.quaternion.setFromAxisAngle(this._axis,e)}}setColor(t){this.line.material.color.set(t),this.cone.material.color.set(t)}copy(t){return super.copy(t,!1),this.line.copy(t.line),this.cone.copy(t.cone),this}dispose(){this.line.geometry.dispose(),this.line.material.dispose(),this.cone.geometry.dispose(),this.cone.material.dispose()}}function Ca(i){const t=(w,m,A)=>i.on(w,m,A),e=(w,...m)=>i.fire(w,...m),n=new l.PlaneGeometry(1,1);n.rotateX(-Math.PI/2);const o=new l.MeshBasicMaterial({color:16777215});o.transparent=!0,o.opacity=.6,o.depthTest=!1,o.depthWrite=!1,o.side=l.DoubleSide;const a=new l.Mesh(n,o);a.ignoreIntersect=!0;const s=new l.Vector3(0,-1,0);s.normalize();const r=new l.Vector3(0,0,0),c=.5,p=.01,u=16777062,d=.1,f=.03,y=new ba(s,r,c,p,u,d,f),g=new l.Object3D;g.add(a),g.add(y),g.renderOrder=99999,a.renderOrder=99999,g.visible=!1,e(V).add(g),t(ao,()=>g),t(me,w=>{e(ye,!0),g.visible=w===void 0?!g.visible:w,e(Q)}),t(dn,()=>g.visible),t(ye,(w=!1)=>{if(w||g.visible){const m=new l.Quaternion,A=new l.Vector3(0,-1,0);m.setFromUnitVectors(A,e(Pt)),g.position.copy(e(vt)),g.quaternion.copy(m)}})}const Es={type:"change"},zn={type:"start"},Is={type:"end"},Be=new l.Ray,Ds=new l.Plane,Ma=Math.cos(70*l.MathUtils.DEG2RAD),$=new l.Vector3,ot=2*Math.PI,X={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},Qn=1e-6;class _s extends l.Controls{constructor(t,e=null){super(t,e),this.state=X.NONE,this.enabled=!0,this.target=new l.Vector3,this.cursor=new l.Vector3,this.minDistance=0,this.maxDistance=1/0,this.minZoom=0,this.maxZoom=1/0,this.minTargetRadius=0,this.maxTargetRadius=1/0,this.minPolarAngle=0,this.maxPolarAngle=Math.PI,this.minAzimuthAngle=-1/0,this.maxAzimuthAngle=1/0,this.enableDamping=!1,this.dampingFactor=.05,this.enableZoom=!0,this.zoomSpeed=1,this.enableRotate=!0,this.rotateSpeed=1,this.enablePan=!0,this.panSpeed=1,this.screenSpacePanning=!0,this.keyPanSpeed=7,this.zoomToCursor=!1,this.autoRotate=!1,this.autoRotateSpeed=2,this.keys={LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"},this.mouseButtons={LEFT:l.MOUSE.ROTATE,MIDDLE:l.MOUSE.DOLLY,RIGHT:l.MOUSE.PAN},this.touches={ONE:l.TOUCH.ROTATE,TWO:l.TOUCH.DOLLY_PAN},this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this._domElementKeyEvents=null,this._lastPosition=new l.Vector3,this._lastQuaternion=new l.Quaternion,this._lastTargetPosition=new l.Vector3,this._quat=new l.Quaternion().setFromUnitVectors(t.up,new l.Vector3(0,1,0)),this._quatInverse=this._quat.clone().invert(),this._spherical=new l.Spherical,this._sphericalDelta=new l.Spherical,this._scale=1,this._panOffset=new l.Vector3,this._rotateStart=new l.Vector2,this._rotateEnd=new l.Vector2,this._rotateDelta=new l.Vector2,this._panStart=new l.Vector2,this._panEnd=new l.Vector2,this._panDelta=new l.Vector2,this._dollyStart=new l.Vector2,this._dollyEnd=new l.Vector2,this._dollyDelta=new l.Vector2,this._dollyDirection=new l.Vector3,this._mouse=new l.Vector2,this._performCursorZoom=!1,this._pointers=[],this._pointerPositions={},this._controlActive=!1,this._onPointerMove=Ta.bind(this),this._onPointerDown=xa.bind(this),this._onPointerUp=ka.bind(this),this._onContextMenu=Fa.bind(this),this._onMouseWheel=Da.bind(this),this._onKeyDown=_a.bind(this),this._onTouchStart=Pa.bind(this),this._onTouchMove=La.bind(this),this._onMouseDown=Ea.bind(this),this._onMouseMove=Ia.bind(this),this._interceptControlDown=Ra.bind(this),this._interceptControlUp=Ba.bind(this),this.domElement!==null&&this.connect(),this.update()}connect(){this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointercancel",this._onPointerUp),this.domElement.addEventListener("contextmenu",this._onContextMenu),this.domElement.addEventListener("wheel",this._onMouseWheel,{passive:!1}),this.domElement.getRootNode().addEventListener("keydown",this._interceptControlDown,{passive:!0,capture:!0}),this.domElement.style.touchAction="none"}disconnect(){this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.domElement.removeEventListener("pointercancel",this._onPointerUp),this.domElement.removeEventListener("wheel",this._onMouseWheel),this.domElement.removeEventListener("contextmenu",this._onContextMenu),this.stopListenToKeyEvents(),this.domElement.getRootNode().removeEventListener("keydown",this._interceptControlDown,{capture:!0}),this.domElement.style.touchAction="auto"}dispose(){this.disconnect()}getPolarAngle(){return this._spherical.phi}getAzimuthalAngle(){return this._spherical.theta}getDistance(){return this.object.position.distanceTo(this.target)}listenToKeyEvents(t){t.addEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=t}stopListenToKeyEvents(){this._domElementKeyEvents!==null&&(this._domElementKeyEvents.removeEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=null)}saveState(){this.target0.copy(this.target),this.position0.copy(this.object.position),this.zoom0=this.object.zoom}reset(){this.target.copy(this.target0),this.object.position.copy(this.position0),this.object.zoom=this.zoom0,this.object.updateProjectionMatrix(),this.dispatchEvent(Es),this.update(),this.state=X.NONE}update(t=null){const e=this.object.position;$.copy(e).sub(this.target),$.applyQuaternion(this._quat),this._spherical.setFromVector3($),this.autoRotate&&this.state===X.NONE&&this._rotateLeft(this._getAutoRotationAngle(t)),this.enableDamping?(this._spherical.theta+=this._sphericalDelta.theta*this.dampingFactor,this._spherical.phi+=this._sphericalDelta.phi*this.dampingFactor):(this._spherical.theta+=this._sphericalDelta.theta,this._spherical.phi+=this._sphericalDelta.phi);let n=this.minAzimuthAngle,o=this.maxAzimuthAngle;isFinite(n)&&isFinite(o)&&(n<-Math.PI?n+=ot:n>Math.PI&&(n-=ot),o<-Math.PI?o+=ot:o>Math.PI&&(o-=ot),n<=o?this._spherical.theta=Math.max(n,Math.min(o,this._spherical.theta)):this._spherical.theta=this._spherical.theta>(n+o)/2?Math.max(n,this._spherical.theta):Math.min(o,this._spherical.theta)),this._spherical.phi=Math.max(this.minPolarAngle,Math.min(this.maxPolarAngle,this._spherical.phi)),this._spherical.makeSafe(),this.enableDamping===!0?this.target.addScaledVector(this._panOffset,this.dampingFactor):this.target.add(this._panOffset),this.target.sub(this.cursor),this.target.clampLength(this.minTargetRadius,this.maxTargetRadius),this.target.add(this.cursor);let a=!1;if(this.zoomToCursor&&this._performCursorZoom||this.object.isOrthographicCamera)this._spherical.radius=this._clampDistance(this._spherical.radius);else{const s=this._spherical.radius;this._spherical.radius=this._clampDistance(this._spherical.radius*this._scale),a=s!=this._spherical.radius}if($.setFromSpherical(this._spherical),$.applyQuaternion(this._quatInverse),e.copy(this.target).add($),this.object.lookAt(this.target),this.enableDamping===!0?(this._sphericalDelta.theta*=1-this.dampingFactor,this._sphericalDelta.phi*=1-this.dampingFactor,this._panOffset.multiplyScalar(1-this.dampingFactor)):(this._sphericalDelta.set(0,0,0),this._panOffset.set(0,0,0)),this.zoomToCursor&&this._performCursorZoom){let s=null;if(this.object.isPerspectiveCamera){const r=$.length();s=this._clampDistance(r*this._scale);const c=r-s;this.object.position.addScaledVector(this._dollyDirection,c),this.object.updateMatrixWorld(),a=!!c}else if(this.object.isOrthographicCamera){const r=new l.Vector3(this._mouse.x,this._mouse.y,0);r.unproject(this.object);const c=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),this.object.updateProjectionMatrix(),a=c!==this.object.zoom;const p=new l.Vector3(this._mouse.x,this._mouse.y,0);p.unproject(this.object),this.object.position.sub(p).add(r),this.object.updateMatrixWorld(),s=$.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),this.zoomToCursor=!1;s!==null&&(this.screenSpacePanning?this.target.set(0,0,-1).transformDirection(this.object.matrix).multiplyScalar(s).add(this.object.position):(Be.origin.copy(this.object.position),Be.direction.set(0,0,-1).transformDirection(this.object.matrix),Math.abs(this.object.up.dot(Be.direction))<Ma?this.object.lookAt(this.target):(Ds.setFromNormalAndCoplanarPoint(this.object.up,this.target),Be.intersectPlane(Ds,this.target))))}else if(this.object.isOrthographicCamera){const s=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),s!==this.object.zoom&&(this.object.updateProjectionMatrix(),a=!0)}return this._scale=1,this._performCursorZoom=!1,a||this._lastPosition.distanceToSquared(this.object.position)>Qn||8*(1-this._lastQuaternion.dot(this.object.quaternion))>Qn||this._lastTargetPosition.distanceToSquared(this.target)>Qn?(this.dispatchEvent(Es),this._lastPosition.copy(this.object.position),this._lastQuaternion.copy(this.object.quaternion),this._lastTargetPosition.copy(this.target),!0):!1}_getAutoRotationAngle(t){return t!==null?ot/60*this.autoRotateSpeed*t:ot/60/60*this.autoRotateSpeed}_getZoomScale(t){const e=Math.abs(t*.01);return Math.pow(.95,this.zoomSpeed*e)}_rotateLeft(t){this._sphericalDelta.theta-=t}_rotateUp(t){this._sphericalDelta.phi-=t}_panLeft(t,e){$.setFromMatrixColumn(e,0),$.multiplyScalar(-t),this._panOffset.add($)}_panUp(t,e){this.screenSpacePanning===!0?$.setFromMatrixColumn(e,1):($.setFromMatrixColumn(e,0),$.crossVectors(this.object.up,$)),$.multiplyScalar(t),this._panOffset.add($)}_pan(t,e){const n=this.domElement;if(this.object.isPerspectiveCamera){const o=this.object.position;$.copy(o).sub(this.target);let a=$.length();a*=Math.tan(this.object.fov/2*Math.PI/180),this._panLeft(2*t*a/n.clientHeight,this.object.matrix),this._panUp(2*e*a/n.clientHeight,this.object.matrix)}else this.object.isOrthographicCamera?(this._panLeft(t*(this.object.right-this.object.left)/this.object.zoom/n.clientWidth,this.object.matrix),this._panUp(e*(this.object.top-this.object.bottom)/this.object.zoom/n.clientHeight,this.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),this.enablePan=!1)}_dollyOut(t){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale/=t:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_dollyIn(t){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale*=t:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_updateZoomParameters(t,e){if(!this.zoomToCursor)return;this._performCursorZoom=!0;const n=this.domElement.getBoundingClientRect(),o=t-n.left,a=e-n.top,s=n.width,r=n.height;this._mouse.x=o/s*2-1,this._mouse.y=-(a/r)*2+1,this._dollyDirection.set(this._mouse.x,this._mouse.y,1).unproject(this.object).sub(this.object.position).normalize()}_clampDistance(t){return Math.max(this.minDistance,Math.min(this.maxDistance,t))}_handleMouseDownRotate(t){this._rotateStart.set(t.clientX,t.clientY)}_handleMouseDownDolly(t){this._updateZoomParameters(t.clientX,t.clientX),this._dollyStart.set(t.clientX,t.clientY)}_handleMouseDownPan(t){this._panStart.set(t.clientX,t.clientY)}_handleMouseMoveRotate(t){this._rotateEnd.set(t.clientX,t.clientY),this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);const e=this.domElement;this._rotateLeft(ot*this._rotateDelta.x/e.clientHeight),this._rotateUp(ot*this._rotateDelta.y/e.clientHeight),this._rotateStart.copy(this._rotateEnd),this.update()}_handleMouseMoveDolly(t){this._dollyEnd.set(t.clientX,t.clientY),this._dollyDelta.subVectors(this._dollyEnd,this._dollyStart),this._dollyDelta.y>0?this._dollyOut(this._getZoomScale(this._dollyDelta.y)):this._dollyDelta.y<0&&this._dollyIn(this._getZoomScale(this._dollyDelta.y)),this._dollyStart.copy(this._dollyEnd),this.update()}_handleMouseMovePan(t){this._panEnd.set(t.clientX,t.clientY),this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd),this.update()}_handleMouseWheel(t){this._updateZoomParameters(t.clientX,t.clientY),t.deltaY<0?this._dollyIn(this._getZoomScale(t.deltaY)):t.deltaY>0&&this._dollyOut(this._getZoomScale(t.deltaY)),this.update()}_handleKeyDown(t){let e=!1;switch(t.code){case this.keys.UP:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateUp(ot*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,this.keyPanSpeed),e=!0;break;case this.keys.BOTTOM:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateUp(-ot*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,-this.keyPanSpeed),e=!0;break;case this.keys.LEFT:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateLeft(ot*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(this.keyPanSpeed,0),e=!0;break;case this.keys.RIGHT:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateLeft(-ot*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(-this.keyPanSpeed,0),e=!0;break}e&&(t.preventDefault(),this.update())}_handleTouchStartRotate(t){if(this._pointers.length===1)this._rotateStart.set(t.pageX,t.pageY);else{const e=this._getSecondPointerPosition(t),n=.5*(t.pageX+e.x),o=.5*(t.pageY+e.y);this._rotateStart.set(n,o)}}_handleTouchStartPan(t){if(this._pointers.length===1)this._panStart.set(t.pageX,t.pageY);else{const e=this._getSecondPointerPosition(t),n=.5*(t.pageX+e.x),o=.5*(t.pageY+e.y);this._panStart.set(n,o)}}_handleTouchStartDolly(t){const e=this._getSecondPointerPosition(t),n=t.pageX-e.x,o=t.pageY-e.y,a=Math.sqrt(n*n+o*o);this._dollyStart.set(0,a)}_handleTouchStartDollyPan(t){this.enableZoom&&this._handleTouchStartDolly(t),this.enablePan&&this._handleTouchStartPan(t)}_handleTouchStartDollyRotate(t){this.enableZoom&&this._handleTouchStartDolly(t),this.enableRotate&&this._handleTouchStartRotate(t)}_handleTouchMoveRotate(t){if(this._pointers.length==1)this._rotateEnd.set(t.pageX,t.pageY);else{const n=this._getSecondPointerPosition(t),o=.5*(t.pageX+n.x),a=.5*(t.pageY+n.y);this._rotateEnd.set(o,a)}this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);const e=this.domElement;this._rotateLeft(ot*this._rotateDelta.x/e.clientHeight),this._rotateUp(ot*this._rotateDelta.y/e.clientHeight),this._rotateStart.copy(this._rotateEnd)}_handleTouchMovePan(t){if(this._pointers.length===1)this._panEnd.set(t.pageX,t.pageY);else{const e=this._getSecondPointerPosition(t),n=.5*(t.pageX+e.x),o=.5*(t.pageY+e.y);this._panEnd.set(n,o)}this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd)}_handleTouchMoveDolly(t){const e=this._getSecondPointerPosition(t),n=t.pageX-e.x,o=t.pageY-e.y,a=Math.sqrt(n*n+o*o);this._dollyEnd.set(0,a),this._dollyDelta.set(0,Math.pow(this._dollyEnd.y/this._dollyStart.y,this.zoomSpeed)),this._dollyOut(this._dollyDelta.y),this._dollyStart.copy(this._dollyEnd);const s=(t.pageX+e.x)*.5,r=(t.pageY+e.y)*.5;this._updateZoomParameters(s,r)}_handleTouchMoveDollyPan(t){this.enableZoom&&this._handleTouchMoveDolly(t),this.enablePan&&this._handleTouchMovePan(t)}_handleTouchMoveDollyRotate(t){this.enableZoom&&this._handleTouchMoveDolly(t),this.enableRotate&&this._handleTouchMoveRotate(t)}_addPointer(t){this._pointers.push(t.pointerId)}_removePointer(t){delete this._pointerPositions[t.pointerId];for(let e=0;e<this._pointers.length;e++)if(this._pointers[e]==t.pointerId){this._pointers.splice(e,1);return}}_isTrackingPointer(t){for(let e=0;e<this._pointers.length;e++)if(this._pointers[e]==t.pointerId)return!0;return!1}_trackPointer(t){let e=this._pointerPositions[t.pointerId];e===void 0&&(e=new l.Vector2,this._pointerPositions[t.pointerId]=e),e.set(t.pageX,t.pageY)}_getSecondPointerPosition(t){const e=t.pointerId===this._pointers[0]?this._pointers[1]:this._pointers[0];return this._pointerPositions[e]}_customWheelEvent(t){const e=t.deltaMode,n={clientX:t.clientX,clientY:t.clientY,deltaY:t.deltaY};switch(e){case 1:n.deltaY*=16;break;case 2:n.deltaY*=100;break}return t.ctrlKey&&!this._controlActive&&(n.deltaY*=10),n}}function xa(i){this.enabled!==!1&&(this._pointers.length===0&&(this.domElement.setPointerCapture(i.pointerId),this.domElement.addEventListener("pointermove",this._onPointerMove),this.domElement.addEventListener("pointerup",this._onPointerUp)),!this._isTrackingPointer(i)&&(this._addPointer(i),i.pointerType==="touch"?this._onTouchStart(i):this._onMouseDown(i)))}function Ta(i){this.enabled!==!1&&(i.pointerType==="touch"?this._onTouchMove(i):this._onMouseMove(i))}function ka(i){switch(this._removePointer(i),this._pointers.length){case 0:this.domElement.releasePointerCapture(i.pointerId),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.dispatchEvent(Is),this.state=X.NONE;break;case 1:const t=this._pointers[0],e=this._pointerPositions[t];this._onTouchStart({pointerId:t,pageX:e.x,pageY:e.y});break}}function Ea(i){let t;switch(i.button){case 0:t=this.mouseButtons.LEFT;break;case 1:t=this.mouseButtons.MIDDLE;break;case 2:t=this.mouseButtons.RIGHT;break;default:t=-1}switch(t){case l.MOUSE.DOLLY:if(this.enableZoom===!1)return;this._handleMouseDownDolly(i),this.state=X.DOLLY;break;case l.MOUSE.ROTATE:if(i.ctrlKey||i.metaKey||i.shiftKey){if(this.enablePan===!1)return;this._handleMouseDownPan(i),this.state=X.PAN}else{if(this.enableRotate===!1)return;this._handleMouseDownRotate(i),this.state=X.ROTATE}break;case l.MOUSE.PAN:if(i.ctrlKey||i.metaKey||i.shiftKey){if(this.enableRotate===!1)return;this._handleMouseDownRotate(i),this.state=X.ROTATE}else{if(this.enablePan===!1)return;this._handleMouseDownPan(i),this.state=X.PAN}break;default:this.state=X.NONE}this.state!==X.NONE&&this.dispatchEvent(zn)}function Ia(i){switch(this.state){case X.ROTATE:if(this.enableRotate===!1)return;this._handleMouseMoveRotate(i);break;case X.DOLLY:if(this.enableZoom===!1)return;this._handleMouseMoveDolly(i);break;case X.PAN:if(this.enablePan===!1)return;this._handleMouseMovePan(i);break}}function Da(i){this.enabled===!1||this.enableZoom===!1||this.state!==X.NONE||(i.preventDefault(),this.dispatchEvent(zn),this._handleMouseWheel(this._customWheelEvent(i)),this.dispatchEvent(Is))}function _a(i){this.enabled!==!1&&this._handleKeyDown(i)}function Pa(i){switch(this._trackPointer(i),this._pointers.length){case 1:switch(this.touches.ONE){case l.TOUCH.ROTATE:if(this.enableRotate===!1)return;this._handleTouchStartRotate(i),this.state=X.TOUCH_ROTATE;break;case l.TOUCH.PAN:if(this.enablePan===!1)return;this._handleTouchStartPan(i),this.state=X.TOUCH_PAN;break;default:this.state=X.NONE}break;case 2:switch(this.touches.TWO){case l.TOUCH.DOLLY_PAN:if(this.enableZoom===!1&&this.enablePan===!1)return;this._handleTouchStartDollyPan(i),this.state=X.TOUCH_DOLLY_PAN;break;case l.TOUCH.DOLLY_ROTATE:if(this.enableZoom===!1&&this.enableRotate===!1)return;this._handleTouchStartDollyRotate(i),this.state=X.TOUCH_DOLLY_ROTATE;break;default:this.state=X.NONE}break;default:this.state=X.NONE}this.state!==X.NONE&&this.dispatchEvent(zn)}function La(i){switch(this._trackPointer(i),this.state){case X.TOUCH_ROTATE:if(this.enableRotate===!1)return;this._handleTouchMoveRotate(i),this.update();break;case X.TOUCH_PAN:if(this.enablePan===!1)return;this._handleTouchMovePan(i),this.update();break;case X.TOUCH_DOLLY_PAN:if(this.enableZoom===!1&&this.enablePan===!1)return;this._handleTouchMoveDollyPan(i),this.update();break;case X.TOUCH_DOLLY_ROTATE:if(this.enableZoom===!1&&this.enableRotate===!1)return;this._handleTouchMoveDollyRotate(i),this.update();break;default:this.state=X.NONE}}function Fa(i){this.enabled!==!1&&i.preventDefault()}function Ra(i){i.key==="Control"&&(this._controlActive=!0,this.domElement.getRootNode().addEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}function Ba(i){i.key==="Control"&&(this._controlActive=!1,this.domElement.getRootNode().removeEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}class Ua extends _s{constructor(t){const e=t.camera;super(e,t.renderer.domElement);const n=this;n.dampingFactor=.1,n.rotateSpeed=.4,n.updateByOptions(t)}updateByOptions(t={}){const e=this;t.enableDamping!==void 0&&(e.enableDamping=t.enableDamping),t.autoRotate!==void 0&&(e.autoRotate=t.autoRotate),t.enableZoom!==void 0&&(e.enableZoom=t.enableZoom),t.enableRotate!==void 0&&(e.enableRotate=t.enableRotate),t.enablePan!==void 0&&(e.enablePan=t.enablePan),t.minDistance!==void 0&&(e.minDistance=t.minDistance),t.maxDistance!==void 0&&(e.maxDistance=t.maxDistance),t.minPolarAngle!==void 0&&(e.minPolarAngle=t.minPolarAngle),t.maxPolarAngle!==void 0&&(e.maxPolarAngle=t.maxPolarAngle),t.fov!==void 0&&(e.object.fov=t.fov),t.near!==void 0&&(e.object.near=t.near),t.far!==void 0&&(e.object.far=t.far),t.position&&e.object.position.fromArray(t.position),t.lookAt&&e.target.fromArray(t.lookAt),t.lookUp&&e.object.up.fromArray(t.lookUp),e.updateControlMode(t),ut&&e._dollyOut?.(.75),e.updateRotateAxis(),e.update()}updateControlMode(t){const e=this,n=t.useCustomControl===!0;if(console.log("[updateControlMode] 当前相机位置:",e.object.position.toArray()),console.log("[updateControlMode] 当前目标位置:",e.target.toArray()),n){console.log("[updateControlMode] 启用第一人称模式"),e.enabled=!1,e.enableRotate=!1,e.enablePan=!1,e.enableZoom=!1,e.enableDamping=!1,e.autoRotate=!1,e.mouseButtons={LEFT:-1,MIDDLE:-1,RIGHT:-1},e.domElement&&(e._domElementKeyEvents&&window.removeEventListener("keydown",e._onKeyDown),e.domElement.removeEventListener("contextmenu",e._onContextMenu),e.domElement.removeEventListener("pointerdown",e._onPointerDown),e.domElement.removeEventListener("pointercancel",e._onPointerCancel),e.domElement.removeEventListener("wheel",e._onMouseWheel),window.removeEventListener("pointermove",e._onPointerMove),window.removeEventListener("pointerup",e._onPointerUp));const o=this.getDistance();e.minDistance=o,e.maxDistance=o}else console.log("[updateControlMode] 禁用第一人称模式"),e.enabled=!0,e.enableRotate=t.enableRotate!==void 0?t.enableRotate:!0,e.enablePan=t.enablePan!==void 0?t.enablePan:!0,e.enableZoom=t.enableZoom!==void 0?t.enableZoom:!0,e.enableDamping=t.enableDamping!==void 0?t.enableDamping:!0,e.autoRotate=t.autoRotate!==void 0?t.autoRotate:!1,e.minDistance=t.minDistance!==void 0?t.minDistance:0,e.maxDistance=t.maxDistance!==void 0?t.maxDistance:1/0,delete e.mouseButtons;console.log("[updateControlMode] 更新后相机位置:",e.object.position.toArray()),console.log("[updateControlMode] 更新后目标位置:",e.target.toArray())}getDistance(){return this._spherical?.radius||1}updateRotateAxis(){this._quat?.setFromUnitVectors?.(this.object.up,new l.Vector3(0,1,0)),this._quatInverse=this._quat?.clone?.()?.invert?.()}update(){return this.enabled===!1?(console.log("[CameraControls.update] 在第一人称模式下跳过update"),!1):super.update()}}class Va extends _s{constructor(t,e){super(t,e),this.screenSpacePanning=!1,this.mouseButtons={LEFT:l.MOUSE.PAN,MIDDLE:l.MOUSE.DOLLY,RIGHT:l.MOUSE.ROTATE},this.touches={ONE:l.TOUCH.PAN,TWO:l.TOUCH.DOLLY_ROTATE}}}const Ps=new l.Box3,Ue=new l.Vector3;class Ls extends l.InstancedBufferGeometry{constructor(){super(),this.isLineSegmentsGeometry=!0,this.type="LineSegmentsGeometry";const t=[-1,2,0,1,2,0,-1,1,0,1,1,0,-1,0,0,1,0,0,-1,-1,0,1,-1,0],e=[-1,2,1,2,-1,1,1,1,-1,-1,1,-1,-1,-2,1,-2],n=[0,2,1,2,3,1,2,4,3,4,5,3,4,6,5,6,7,5];this.setIndex(n),this.setAttribute("position",new l.Float32BufferAttribute(t,3)),this.setAttribute("uv",new l.Float32BufferAttribute(e,2))}applyMatrix4(t){const e=this.attributes.instanceStart,n=this.attributes.instanceEnd;return e!==void 0&&(e.applyMatrix4(t),n.applyMatrix4(t),e.needsUpdate=!0),this.boundingBox!==null&&this.computeBoundingBox(),this.boundingSphere!==null&&this.computeBoundingSphere(),this}setPositions(t){let e;t instanceof Float32Array?e=t:Array.isArray(t)&&(e=new Float32Array(t));const n=new l.InstancedInterleavedBuffer(e,6,1);return this.setAttribute("instanceStart",new l.InterleavedBufferAttribute(n,3,0)),this.setAttribute("instanceEnd",new l.InterleavedBufferAttribute(n,3,3)),this.instanceCount=this.attributes.instanceStart.count,this.computeBoundingBox(),this.computeBoundingSphere(),this}setColors(t){let e;t instanceof Float32Array?e=t:Array.isArray(t)&&(e=new Float32Array(t));const n=new l.InstancedInterleavedBuffer(e,6,1);return this.setAttribute("instanceColorStart",new l.InterleavedBufferAttribute(n,3,0)),this.setAttribute("instanceColorEnd",new l.InterleavedBufferAttribute(n,3,3)),this}fromWireframeGeometry(t){return this.setPositions(t.attributes.position.array),this}fromEdgesGeometry(t){return this.setPositions(t.attributes.position.array),this}fromMesh(t){return this.fromWireframeGeometry(new l.WireframeGeometry(t.geometry)),this}fromLineSegments(t){const e=t.geometry;return this.setPositions(e.attributes.position.array),this}computeBoundingBox(){this.boundingBox===null&&(this.boundingBox=new l.Box3);const t=this.attributes.instanceStart,e=this.attributes.instanceEnd;t!==void 0&&e!==void 0&&(this.boundingBox.setFromBufferAttribute(t),Ps.setFromBufferAttribute(e),this.boundingBox.union(Ps))}computeBoundingSphere(){this.boundingSphere===null&&(this.boundingSphere=new l.Sphere),this.boundingBox===null&&this.computeBoundingBox();const t=this.attributes.instanceStart,e=this.attributes.instanceEnd;if(t!==void 0&&e!==void 0){const n=this.boundingSphere.center;this.boundingBox.getCenter(n);let o=0;for(let a=0,s=t.count;a<s;a++)Ue.fromBufferAttribute(t,a),o=Math.max(o,n.distanceToSquared(Ue)),Ue.fromBufferAttribute(e,a),o=Math.max(o,n.distanceToSquared(Ue));this.boundingSphere.radius=Math.sqrt(o),isNaN(this.boundingSphere.radius)&&console.error("THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.",this)}}toJSON(){}applyMatrix(t){return console.warn("THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4()."),this.applyMatrix4(t)}}l.UniformsLib.line={worldUnits:{value:1},linewidth:{value:1},resolution:{value:new l.Vector2(1,1)},dashOffset:{value:0},dashScale:{value:1},dashSize:{value:1},gapSize:{value:1}},l.ShaderLib.line={uniforms:l.UniformsUtils.merge([l.UniformsLib.common,l.UniformsLib.fog,l.UniformsLib.line]),vertexShader:`
		#include <common>
		#include <color_pars_vertex>
		#include <fog_pars_vertex>
		#include <logdepthbuf_pars_vertex>
		#include <clipping_planes_pars_vertex>

		uniform float linewidth;
		uniform vec2 resolution;

		attribute vec3 instanceStart;
		attribute vec3 instanceEnd;

		attribute vec3 instanceColorStart;
		attribute vec3 instanceColorEnd;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#ifdef USE_DASH

			uniform float dashScale;
			attribute float instanceDistanceStart;
			attribute float instanceDistanceEnd;
			varying float vLineDistance;

		#endif

		void trimSegment( const in vec4 start, inout vec4 end ) {

			// trim end segment so it terminates between the camera plane and the near plane

			// conservative estimate of the near plane
			float a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column
			float b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column
			float nearEstimate = - 0.5 * b / a;

			float alpha = ( nearEstimate - start.z ) / ( end.z - start.z );

			end.xyz = mix( start.xyz, end.xyz, alpha );

		}

		void main() {

			#ifdef USE_COLOR

				vColor.xyz = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;

			#endif

			#ifdef USE_DASH

				vLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;
				vUv = uv;

			#endif

			float aspect = resolution.x / resolution.y;

			// camera space
			vec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );
			vec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );

			#ifdef WORLD_UNITS

				worldStart = start.xyz;
				worldEnd = end.xyz;

			#else

				vUv = uv;

			#endif

			// special case for perspective projection, and segments that terminate either in, or behind, the camera plane
			// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space
			// but we need to perform ndc-space calculations in the shader, so we must address this issue directly
			// perhaps there is a more elegant solution -- WestLangley

			bool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column

			if ( perspective ) {

				if ( start.z < 0.0 && end.z >= 0.0 ) {

					trimSegment( start, end );

				} else if ( end.z < 0.0 && start.z >= 0.0 ) {

					trimSegment( end, start );

				}

			}

			// clip space
			vec4 clipStart = projectionMatrix * start;
			vec4 clipEnd = projectionMatrix * end;

			// ndc space
			vec3 ndcStart = clipStart.xyz / clipStart.w;
			vec3 ndcEnd = clipEnd.xyz / clipEnd.w;

			// direction
			vec2 dir = ndcEnd.xy - ndcStart.xy;

			// account for clip-space aspect ratio
			dir.x *= aspect;
			dir = normalize( dir );

			#ifdef WORLD_UNITS

				vec3 worldDir = normalize( end.xyz - start.xyz );
				vec3 tmpFwd = normalize( mix( start.xyz, end.xyz, 0.5 ) );
				vec3 worldUp = normalize( cross( worldDir, tmpFwd ) );
				vec3 worldFwd = cross( worldDir, worldUp );
				worldPos = position.y < 0.5 ? start: end;

				// height offset
				float hw = linewidth * 0.5;
				worldPos.xyz += position.x < 0.0 ? hw * worldUp : - hw * worldUp;

				// don't extend the line if we're rendering dashes because we
				// won't be rendering the endcaps
				#ifndef USE_DASH

					// cap extension
					worldPos.xyz += position.y < 0.5 ? - hw * worldDir : hw * worldDir;

					// add width to the box
					worldPos.xyz += worldFwd * hw;

					// endcaps
					if ( position.y > 1.0 || position.y < 0.0 ) {

						worldPos.xyz -= worldFwd * 2.0 * hw;

					}

				#endif

				// project the worldpos
				vec4 clip = projectionMatrix * worldPos;

				// shift the depth of the projected points so the line
				// segments overlap neatly
				vec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;
				clip.z = clipPose.z * clip.w;

			#else

				vec2 offset = vec2( dir.y, - dir.x );
				// undo aspect ratio adjustment
				dir.x /= aspect;
				offset.x /= aspect;

				// sign flip
				if ( position.x < 0.0 ) offset *= - 1.0;

				// endcaps
				if ( position.y < 0.0 ) {

					offset += - dir;

				} else if ( position.y > 1.0 ) {

					offset += dir;

				}

				// adjust for linewidth
				offset *= linewidth;

				// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...
				offset /= resolution.y;

				// select end
				vec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;

				// back to clip space
				offset *= clip.w;

				clip.xy += offset;

			#endif

			gl_Position = clip;

			vec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation

			#include <logdepthbuf_vertex>
			#include <clipping_planes_vertex>
			#include <fog_vertex>

		}
		`,fragmentShader:`
		uniform vec3 diffuse;
		uniform float opacity;
		uniform float linewidth;

		#ifdef USE_DASH

			uniform float dashOffset;
			uniform float dashSize;
			uniform float gapSize;

		#endif

		varying float vLineDistance;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#include <common>
		#include <color_pars_fragment>
		#include <fog_pars_fragment>
		#include <logdepthbuf_pars_fragment>
		#include <clipping_planes_pars_fragment>

		vec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {

			float mua;
			float mub;

			vec3 p13 = p1 - p3;
			vec3 p43 = p4 - p3;

			vec3 p21 = p2 - p1;

			float d1343 = dot( p13, p43 );
			float d4321 = dot( p43, p21 );
			float d1321 = dot( p13, p21 );
			float d4343 = dot( p43, p43 );
			float d2121 = dot( p21, p21 );

			float denom = d2121 * d4343 - d4321 * d4321;

			float numer = d1343 * d4321 - d1321 * d4343;

			mua = numer / denom;
			mua = clamp( mua, 0.0, 1.0 );
			mub = ( d1343 + d4321 * ( mua ) ) / d4343;
			mub = clamp( mub, 0.0, 1.0 );

			return vec2( mua, mub );

		}

		void main() {

			#include <clipping_planes_fragment>

			#ifdef USE_DASH

				if ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps

				if ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX

			#endif

			float alpha = opacity;

			#ifdef WORLD_UNITS

				// Find the closest points on the view ray and the line segment
				vec3 rayEnd = normalize( worldPos.xyz ) * 1e5;
				vec3 lineDir = worldEnd - worldStart;
				vec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );

				vec3 p1 = worldStart + lineDir * params.x;
				vec3 p2 = rayEnd * params.y;
				vec3 delta = p1 - p2;
				float len = length( delta );
				float norm = len / linewidth;

				#ifndef USE_DASH

					#ifdef USE_ALPHA_TO_COVERAGE

						float dnorm = fwidth( norm );
						alpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );

					#else

						if ( norm > 0.5 ) {

							discard;

						}

					#endif

				#endif

			#else

				#ifdef USE_ALPHA_TO_COVERAGE

					// artifacts appear on some hardware if a derivative is taken within a conditional
					float a = vUv.x;
					float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
					float len2 = a * a + b * b;
					float dlen = fwidth( len2 );

					if ( abs( vUv.y ) > 1.0 ) {

						alpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );

					}

				#else

					if ( abs( vUv.y ) > 1.0 ) {

						float a = vUv.x;
						float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
						float len2 = a * a + b * b;

						if ( len2 > 1.0 ) discard;

					}

				#endif

			#endif

			vec4 diffuseColor = vec4( diffuse, alpha );

			#include <logdepthbuf_fragment>
			#include <color_fragment>

			gl_FragColor = vec4( diffuseColor.rgb, alpha );

			#include <tonemapping_fragment>
			#include <colorspace_fragment>
			#include <fog_fragment>
			#include <premultiplied_alpha_fragment>

		}
		`};class zt extends l.ShaderMaterial{constructor(t){super({type:"LineMaterial",uniforms:l.UniformsUtils.clone(l.ShaderLib.line.uniforms),vertexShader:l.ShaderLib.line.vertexShader,fragmentShader:l.ShaderLib.line.fragmentShader,clipping:!0}),this.isLineMaterial=!0,this.setValues(t)}get color(){return this.uniforms.diffuse.value}set color(t){this.uniforms.diffuse.value=t}get worldUnits(){return"WORLD_UNITS"in this.defines}set worldUnits(t){t===!0?this.defines.WORLD_UNITS="":delete this.defines.WORLD_UNITS}get linewidth(){return this.uniforms.linewidth.value}set linewidth(t){this.uniforms.linewidth&&(this.uniforms.linewidth.value=t)}get dashed(){return"USE_DASH"in this.defines}set dashed(t){t===!0!==this.dashed&&(this.needsUpdate=!0),t===!0?this.defines.USE_DASH="":delete this.defines.USE_DASH}get dashScale(){return this.uniforms.dashScale.value}set dashScale(t){this.uniforms.dashScale.value=t}get dashSize(){return this.uniforms.dashSize.value}set dashSize(t){this.uniforms.dashSize.value=t}get dashOffset(){return this.uniforms.dashOffset.value}set dashOffset(t){this.uniforms.dashOffset.value=t}get gapSize(){return this.uniforms.gapSize.value}set gapSize(t){this.uniforms.gapSize.value=t}get opacity(){return this.uniforms.opacity.value}set opacity(t){this.uniforms&&(this.uniforms.opacity.value=t)}get resolution(){return this.uniforms.resolution.value}set resolution(t){this.uniforms.resolution.value.copy(t)}get alphaToCoverage(){return"USE_ALPHA_TO_COVERAGE"in this.defines}set alphaToCoverage(t){this.defines&&(t===!0!==this.alphaToCoverage&&(this.needsUpdate=!0),t===!0?this.defines.USE_ALPHA_TO_COVERAGE="":delete this.defines.USE_ALPHA_TO_COVERAGE)}}const Wn=new l.Vector4,Fs=new l.Vector3,Rs=new l.Vector3,q=new l.Vector4,J=new l.Vector4,ht=new l.Vector4,Hn=new l.Vector3,Yn=new l.Matrix4,tt=new l.Line3,Bs=new l.Vector3,Ve=new l.Box3,Oe=new l.Sphere,gt=new l.Vector4;let mt,It;function Us(i,t,e){return gt.set(0,0,-t,1).applyMatrix4(i.projectionMatrix),gt.multiplyScalar(1/gt.w),gt.x=It/e.width,gt.y=It/e.height,gt.applyMatrix4(i.projectionMatrixInverse),gt.multiplyScalar(1/gt.w),Math.abs(Math.max(gt.x,gt.y))}function Oa(i,t){const e=i.matrixWorld,n=i.geometry,o=n.attributes.instanceStart,a=n.attributes.instanceEnd,s=Math.min(n.instanceCount,o.count);for(let r=0,c=s;r<c;r++){tt.start.fromBufferAttribute(o,r),tt.end.fromBufferAttribute(a,r),tt.applyMatrix4(e);const p=new l.Vector3,u=new l.Vector3;mt.distanceSqToSegment(tt.start,tt.end,u,p),u.distanceTo(p)<It*.5&&t.push({point:u,pointOnLine:p,distance:mt.origin.distanceTo(u),object:i,face:null,faceIndex:r,uv:null,uv1:null})}}function za(i,t,e){const n=t.projectionMatrix,a=i.material.resolution,s=i.matrixWorld,r=i.geometry,c=r.attributes.instanceStart,p=r.attributes.instanceEnd,u=Math.min(r.instanceCount,c.count),d=-t.near;mt.at(1,ht),ht.w=1,ht.applyMatrix4(t.matrixWorldInverse),ht.applyMatrix4(n),ht.multiplyScalar(1/ht.w),ht.x*=a.x/2,ht.y*=a.y/2,ht.z=0,Hn.copy(ht),Yn.multiplyMatrices(t.matrixWorldInverse,s);for(let f=0,y=u;f<y;f++){if(q.fromBufferAttribute(c,f),J.fromBufferAttribute(p,f),q.w=1,J.w=1,q.applyMatrix4(Yn),J.applyMatrix4(Yn),q.z>d&&J.z>d)continue;if(q.z>d){const x=q.z-J.z,b=(q.z-d)/x;q.lerp(J,b)}else if(J.z>d){const x=J.z-q.z,b=(J.z-d)/x;J.lerp(q,b)}q.applyMatrix4(n),J.applyMatrix4(n),q.multiplyScalar(1/q.w),J.multiplyScalar(1/J.w),q.x*=a.x/2,q.y*=a.y/2,J.x*=a.x/2,J.y*=a.y/2,tt.start.copy(q),tt.start.z=0,tt.end.copy(J),tt.end.z=0;const w=tt.closestPointToPointParameter(Hn,!0);tt.at(w,Bs);const m=l.MathUtils.lerp(q.z,J.z,w),A=m>=-1&&m<=1,E=Hn.distanceTo(Bs)<It*.5;if(A&&E){tt.start.fromBufferAttribute(c,f),tt.end.fromBufferAttribute(p,f),tt.start.applyMatrix4(s),tt.end.applyMatrix4(s);const x=new l.Vector3,b=new l.Vector3;mt.distanceSqToSegment(tt.start,tt.end,b,x),e.push({point:b,pointOnLine:x,distance:mt.origin.distanceTo(b),object:i,face:null,faceIndex:f,uv:null,uv1:null})}}}class Qa extends l.Mesh{constructor(t=new Ls,e=new zt({color:Math.random()*16777215})){super(t,e),this.isLineSegments2=!0,this.type="LineSegments2"}computeLineDistances(){const t=this.geometry,e=t.attributes.instanceStart,n=t.attributes.instanceEnd,o=new Float32Array(2*e.count);for(let s=0,r=0,c=e.count;s<c;s++,r+=2)Fs.fromBufferAttribute(e,s),Rs.fromBufferAttribute(n,s),o[r]=r===0?0:o[r-1],o[r+1]=o[r]+Fs.distanceTo(Rs);const a=new l.InstancedInterleavedBuffer(o,2,1);return t.setAttribute("instanceDistanceStart",new l.InterleavedBufferAttribute(a,1,0)),t.setAttribute("instanceDistanceEnd",new l.InterleavedBufferAttribute(a,1,1)),this}raycast(t,e){const n=this.material.worldUnits,o=t.camera;o===null&&!n&&console.error('LineSegments2: "Raycaster.camera" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.');const a=t.params.Line2!==void 0&&t.params.Line2.threshold||0;mt=t.ray;const s=this.matrixWorld,r=this.geometry,c=this.material;It=c.linewidth+a,r.boundingSphere===null&&r.computeBoundingSphere(),Oe.copy(r.boundingSphere).applyMatrix4(s);let p;if(n)p=It*.5;else{const d=Math.max(o.near,Oe.distanceToPoint(mt.origin));p=Us(o,d,c.resolution)}if(Oe.radius+=p,mt.intersectsSphere(Oe)===!1)return;r.boundingBox===null&&r.computeBoundingBox(),Ve.copy(r.boundingBox).applyMatrix4(s);let u;if(n)u=It*.5;else{const d=Math.max(o.near,Ve.distanceToPoint(mt.origin));u=Us(o,d,c.resolution)}Ve.expandByScalar(u),mt.intersectsBox(Ve)!==!1&&(n?Oa(this,e):za(this,o,e))}onBeforeRender(t){const e=this.material.uniforms;e&&e.resolution&&(t.getViewport(Wn),this.material.uniforms.resolution.value.set(Wn.z,Wn.w))}}class ie extends Ls{constructor(){super(),this.isLineGeometry=!0,this.type="LineGeometry"}setPositions(t){const e=t.length-3,n=new Float32Array(2*e);for(let o=0;o<e;o+=3)n[2*o]=t[o],n[2*o+1]=t[o+1],n[2*o+2]=t[o+2],n[2*o+3]=t[o+3],n[2*o+4]=t[o+4],n[2*o+5]=t[o+5];return super.setPositions(n),this}setColors(t){const e=t.length-3,n=new Float32Array(2*e);for(let o=0;o<e;o+=3)n[2*o]=t[o],n[2*o+1]=t[o+1],n[2*o+2]=t[o+2],n[2*o+3]=t[o+3],n[2*o+4]=t[o+4],n[2*o+5]=t[o+5];return super.setColors(n),this}setFromPoints(t){const e=t.length-1,n=new Float32Array(6*e);for(let o=0;o<e;o++)n[6*o]=t[o].x,n[6*o+1]=t[o].y,n[6*o+2]=t[o].z||0,n[6*o+3]=t[o+1].x,n[6*o+4]=t[o+1].y,n[6*o+5]=t[o+1].z||0;return super.setPositions(n),this}fromLine(t){const e=t.geometry;return this.setPositions(e.attributes.position.array),this}}class Dt extends Qa{constructor(t=new ie,e=new zt({color:Math.random()*16777215})){super(t,e),this.isLine2=!0,this.type="Line2"}}const Vs=new l.Vector3,Wa=new l.Quaternion,Os=new l.Vector3;class Ha extends l.Object3D{constructor(t=document.createElement("div")){super(),this.isCSS3DObject=!0,this.element=t,this.element.style.position="absolute",this.element.style.pointerEvents="auto",this.element.style.userSelect="none",this.element.setAttribute("draggable",!1),this.addEventListener("removed",function(){this.traverse(function(e){e.element instanceof e.element.ownerDocument.defaultView.Element&&e.element.parentNode!==null&&e.element.remove()})})}copy(t,e){return super.copy(t,e),this.element=t.element.cloneNode(!0),this}}class it extends Ha{constructor(t){super(t),this.isCSS3DSprite=!0,this.rotation2D=0}copy(t,e){return super.copy(t,e),this.rotation2D=t.rotation2D,this}}const yt=new l.Matrix4,Ya=new l.Matrix4;class Ga{constructor(t={}){const e=this;let n,o,a,s;const r={camera:{style:""},objects:new WeakMap},c=t.element!==void 0?t.element:document.createElement("div");c.style.overflow="hidden",this.domElement=c;const p=document.createElement("div");p.style.transformOrigin="0 0",p.style.pointerEvents="none",c.appendChild(p);const u=document.createElement("div");u.style.transformStyle="preserve-3d",p.appendChild(u),this.getSize=function(){return{width:n,height:o}},this.render=function(m,A){const E=A.projectionMatrix.elements[5]*s;A.view&&A.view.enabled?(p.style.transform=`translate( ${-A.view.offsetX*(n/A.view.width)}px, ${-A.view.offsetY*(o/A.view.height)}px )`,p.style.transform+=`scale( ${A.view.fullWidth/A.view.width}, ${A.view.fullHeight/A.view.height} )`):p.style.transform="",m.matrixWorldAutoUpdate===!0&&m.updateMatrixWorld(),A.parent===null&&A.matrixWorldAutoUpdate===!0&&A.updateMatrixWorld();let x,b;A.isOrthographicCamera&&(x=-(A.right+A.left)/2,b=(A.top+A.bottom)/2);const P=A.view&&A.view.enabled?A.view.height/A.view.fullHeight:1,T=A.isOrthographicCamera?`scale( ${P} )scale(`+E+")translate("+d(x)+"px,"+d(b)+"px)"+f(A.matrixWorldInverse):`scale( ${P} )translateZ(`+E+"px)"+f(A.matrixWorldInverse),F=(A.isPerspectiveCamera?"perspective("+E+"px) ":"")+T+"translate("+a+"px,"+s+"px)";r.camera.style!==F&&(u.style.transform=F,r.camera.style=F),w(m,m,A)},this.setSize=function(m,A){n=m,o=A,a=n/2,s=o/2,c.style.width=m+"px",c.style.height=A+"px",p.style.width=m+"px",p.style.height=A+"px",u.style.width=m+"px",u.style.height=A+"px"};function d(m){return Math.abs(m)<1e-10?0:m}function f(m){const A=m.elements;return"matrix3d("+d(A[0])+","+d(-A[1])+","+d(A[2])+","+d(A[3])+","+d(A[4])+","+d(-A[5])+","+d(A[6])+","+d(A[7])+","+d(A[8])+","+d(-A[9])+","+d(A[10])+","+d(A[11])+","+d(A[12])+","+d(-A[13])+","+d(A[14])+","+d(A[15])+")"}function y(m){const A=m.elements;return"translate(-50%,-50%)"+("matrix3d("+d(A[0])+","+d(A[1])+","+d(A[2])+","+d(A[3])+","+d(-A[4])+","+d(-A[5])+","+d(-A[6])+","+d(-A[7])+","+d(A[8])+","+d(A[9])+","+d(A[10])+","+d(A[11])+","+d(A[12])+","+d(A[13])+","+d(A[14])+","+d(A[15])+")")}function g(m){m.isCSS3DObject&&(m.element.style.display="none");for(let A=0,E=m.children.length;A<E;A++)g(m.children[A])}function w(m,A,E,x){if(m.visible===!1){g(m);return}if(m.isCSS3DObject){const b=m.layers.test(E.layers)===!0,P=m.element;if(P.style.display=b===!0?"":"none",b===!0){m.onBeforeRender(e,A,E);let T;m.isCSS3DSprite?(yt.copy(E.matrixWorldInverse),yt.transpose(),m.rotation2D!==0&&yt.multiply(Ya.makeRotationZ(m.rotation2D)),m.matrixWorld.decompose(Vs,Wa,Os),yt.setPosition(Vs),yt.scale(Os),yt.elements[3]=0,yt.elements[7]=0,yt.elements[11]=0,yt.elements[15]=1,T=y(yt)):T=y(m.matrixWorld);const I=r.objects.get(m);if(I===void 0||I.style!==T){P.style.transform=T;const F={style:T};r.objects.set(m,F)}P.parentNode!==u&&u.appendChild(P),m.onAfterRender(e,A,E)}}for(let b=0,P=m.children.length;b<P;b++)w(m.children[b],A,E)}}}class Gn extends Dt{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.events=t}drawStart(t,e){if(this.disposed)return;const n=this,o=document.querySelectorAll(".mark-wrap-line.main-warp").length+1,a={type:"MarkDistanceLine",name:e||"line"+Date.now(),startPoint:t.toArray(),endPoint:t.toArray(),lineColor:"#eeee00",lineWidth:3,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,distanceTagColor:"#000000",distanceTagBackground:"#e0ffff",distanceTagOpacity:.9,distanceTagVisible:!0,title:"标记距离"+o},s=new ie;s.setPositions([...a.startPoint,...a.endPoint]);const r=new zt({color:a.lineColor,linewidth:a.lineWidth});r.resolution.set(innerWidth,innerHeight),n.copy(new Dt(s,r));const c=new l.CircleGeometry(.05,32),p=new l.MeshBasicMaterial({color:16777215,side:l.DoubleSide});p.transparent=!0,p.opacity=.6;const u=new l.Mesh(c,p);u.position.copy(t),u.isMark=!0;const d=new l.Mesh(c,p);d.position.copy(t),d.isMark=!0;const f=document.createElement("div");f.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`,f.classList.add("mark-wrap-line",`${a.name}`,"main-warp"),f.style.position="absolute",f.style.borderRadius="4px",f.style.cursor="pointer",f.onclick=()=>{if(n.events.fire(_).markMode)return;const m=parent?.onActiveMark;m?.(n.getMarkData(!0)),n.events.fire(j)},f.oncontextmenu=m=>m.preventDefault();const y=new it(f);y.position.copy(t),y.element.style.pointerEvents="none",y.scale.set(.01,.01,.01),y.visible=a.mainTagVisible;const g=document.createElement("div");g.innerHTML=`<span class="${e}-distance-tag ${e}-distance-tag0" style="color:${a.distanceTagColor};background:${a.distanceTagBackground};opacity:${a.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`,g.classList.add("mark-wrap-line",`${e}`,"distance-warp"),g.style.position="absolute",g.style.borderRadius="4px",g.style.pointerEvents="none";const w=new it(g);w.position.set(t.x,t.y,t.z),w.element.style.pointerEvents="none",w.scale.set(.008,.008,.008),w.visible=!1,n.add(u,d,y,w),n.data=a,n.circleStart=u,n.circleEnd=d,n.css3dTag=w,n.css3dMainTag=y,n.events.fire(St,n)}drawUpdate(t,e=!0){if(this.disposed)return;const n=this;if(t?.endPoint){e&&(n.data.endPoint=[...t.endPoint]);const o=new l.Vector3().fromArray(n.data.startPoint),a=new l.Vector3().fromArray(t.endPoint);n.geometry.setPositions([...n.data.startPoint,...t.endPoint]);const s=new l.Vector3((o.x+a.x)/2,(o.y+a.y)/2,(o.z+a.z)/2);n.css3dTag.position.set(s.x,s.y,s.z);const r=a.clone().sub(o).normalize();n.circleStart.lookAt(n.circleStart.position.clone().add(r)),n.circleEnd.lookAt(n.circleEnd.position.clone().add(r));const c=o.distanceTo(a);n.css3dTag.visible=c>.5;const p=(c*n.events.fire(_).meterScale).toFixed(2)+" m";n.css3dTag.element.childNodes[0].innerText=p}t?.lineColor&&(e&&(n.data.lineColor=t.lineColor),n.material.color.set(t.lineColor)),t?.lineWidth&&(e&&(n.data.lineWidth=t.lineWidth),n.material.linewidth=t.lineWidth),t?.mainTagColor&&(e&&(n.data.mainTagColor=t.mainTagColor),document.querySelector(`.${n.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(n.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${n.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(n.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${n.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(n.data.mainTagVisible=t.mainTagVisible),n.css3dMainTag.visible=t.mainTagVisible),t?.distanceTagColor&&(e&&(n.data.distanceTagColor=t.distanceTagColor),document.querySelector(`.${n.data.name}-distance-tag0`).style.color=t.distanceTagColor),t?.distanceTagBackground&&(e&&(n.data.distanceTagBackground=t.distanceTagBackground),document.querySelector(`.${n.data.name}-distance-tag0`).style.background=t.distanceTagBackground),t?.distanceTagOpacity&&(e&&(n.data.distanceTagOpacity=t.distanceTagOpacity),document.querySelector(`.${n.data.name}-distance-tag0`).style.opacity=t.distanceTagOpacity.toString()),t?.distanceTagVisible!==void 0&&(e&&(n.data.distanceTagVisible=t.distanceTagVisible),n.css3dTag.visible=t.distanceTagVisible),t?.title!==void 0&&(e&&(n.data.title=t.title),this.css3dMainTag.element.querySelector(`.${n.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(n.data.note=t.note),n.events.fire(Q)}updateByMeterScale(t){const e=this,n=new l.Vector3().fromArray(e.data.startPoint),o=new l.Vector3().fromArray(e.data.endPoint),a=(n.distanceTo(o)*t).toFixed(2)+" m";e.css3dTag.element.childNodes[0].innerText=a}drawFinish(t){if(this.disposed)return;const e=this;e.data.endPoint=[...t.toArray()];const n=new l.Vector3().fromArray(e.data.startPoint),o=new l.Vector3().fromArray(e.data.endPoint),a=new l.Vector3((n.x+o.x)/2,(n.y+o.y)/2,(n.z+o.z)/2);e.geometry.setPositions([...e.data.startPoint,...e.data.endPoint]),e.css3dTag.position.set(a.x,a.y,a.z);const s=o.clone().sub(n).normalize();e.circleStart.lookAt(e.circleStart.position.clone().add(s)),e.circleEnd.lookAt(e.circleEnd.position.clone().add(s)),e.circleEnd.position.copy(o);const r=n.distanceTo(o);e.css3dTag.visible=!0;const c=(r*e.events.fire(_).meterScale).toFixed(2)+" m";e.css3dTag.element.childNodes[0].innerText=c,e.events.fire(bt);const p=parent?.onActiveMark,u=e.getMarkData(!0);u.isNew=!0,u.meterScale=e.events.fire(_).meterScale,p?.(u)}draw(t){if(this.disposed)return;const e=this,n={type:"MarkDistanceLine",name:t.name||"line"+Date.now(),startPoint:[...t.startPoint],endPoint:[...t.endPoint],lineColor:t.lineColor||"#eeee00",lineWidth:t.lineWidth||3,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,distanceTagColor:t.distanceTagColor||"#000000",distanceTagBackground:t.distanceTagBackground||"#e0ffff",distanceTagOpacity:t.distanceTagOpacity||.9,distanceTagVisible:t.distanceTagVisible===void 0?!0:t.distanceTagVisible,title:t.title||"标记距离"},o=new l.Vector3().fromArray(n.startPoint),a=new l.Vector3().fromArray(n.endPoint),s=new l.Vector3((o.x+a.x)/2,(o.y+a.y)/2,(o.z+a.z)/2),r=new ie;r.setPositions([...n.startPoint,...n.endPoint]);const c=new zt({color:n.lineColor,linewidth:n.lineWidth});c.resolution.set(innerWidth,innerHeight),e.copy(new Dt(r,c));const p=new l.CircleGeometry(.05,32),u=new l.MeshBasicMaterial({color:16777215,side:l.DoubleSide});u.transparent=!0,u.opacity=.6;const d=new l.Mesh(p,u);d.position.copy(o),d.isMark=!0;const f=new l.Mesh(p,u);f.position.copy(a),f.isMark=!0;const y=a.clone().sub(o).normalize();d.lookAt(d.position.clone().add(y)),f.lookAt(f.position.clone().add(y));const g=n.name,w=document.createElement("div");w.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${g}-main-tag" style="color:${n.mainTagColor};background:${n.mainTagBackground};opacity:${n.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${n.title}</span>
                             </div>`,w.classList.add("mark-wrap-line",`${n.name}`,"main-warp"),w.style.position="absolute",w.style.borderRadius="4px",w.style.cursor="pointer",w.onclick=()=>{if(e.events.fire(_).markMode)return;const P=parent?.onActiveMark;P?.(e.getMarkData(!0)),e.events.fire(j)},w.oncontextmenu=P=>P.preventDefault();const m=new it(w);m.position.copy(o),m.element.style.pointerEvents="none",m.scale.set(.01,.01,.01),m.visible=n.mainTagVisible;const E=(o.distanceTo(a)*e.events.fire(_).meterScale).toFixed(2)+" m",x=document.createElement("div");x.innerHTML=`<span class="${g}-distance-tag ${g}-distance-tag0" style="color:${n.distanceTagColor};background:${n.distanceTagBackground};opacity:${n.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${E}</span>`,x.classList.add("mark-wrap-line",`${g}`,"distance-warp"),x.style.position="absolute",x.style.borderRadius="4px",x.style.pointerEvents="none";const b=new it(x);b.position.copy(s),b.element.style.pointerEvents="none",b.scale.set(.008,.008,.008),b.visible=n.distanceTagVisible,e.add(d,f,m,b),e.data=n,e.circleStart=d,e.circleEnd=f,e.css3dTag=b,e.css3dMainTag=m,e.events.fire(St,e)}getMarkData(t=!1){const e={...this.data};return t?(delete e.startPoint,delete e.endPoint):(e.startPoint=[...e.startPoint],e.endPoint=[...e.endPoint]),e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Ct,t),t.events.fire(V).remove(t),t.events.fire(Bt,t),document.querySelectorAll(`.${t.data.name}`).forEach(n=>n.parentElement?.removeChild(n)),t.events=null,t.data=null,t.circleStart=null,t.circleEnd=null,t.css3dTag=null,t.css3dMainTag=null}}class Xn extends Dt{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.css3dTags=[],this.group=new l.Group,this.add(this.group),this.events=t}drawStart(t,e){if(this.disposed)return;const n=document.querySelectorAll(".mark-wrap-lines.main-warp").length+1,o={type:"MarkMultiLines",name:e||"lines"+Date.now(),points:[...t.toArray(),...t.toArray()],lineColor:"#eeee00",lineWidth:3,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,distanceTagColor:"#000000",distanceTagBackground:"#e0ffff",distanceTagOpacity:.9,distanceTagVisible:!0,title:"标记线"+n,note:""};this.draw(o)}drawUpdate(t,e=!0,n,o=!1){if(this.disposed)return;const a=this;if(n)if(o){const s=a.data.points.length,r=a.css3dTags.length-1,c=new l.Vector3().fromArray(a.data.points.slice(s-6,s-3)),p=n,u=new l.Vector3((c.x+p.x)/2,(c.y+p.y)/2,(c.z+p.z)/2),f=(c.distanceTo(p)*a.events.fire(_).meterScale).toFixed(2)+" m";a.css3dTags[r].element.childNodes[0].innerText=f,a.css3dTags[r].position.set(u.x,u.y,u.z),a.css3dTags[r].visible=!0,a.data.points.pop(),a.data.points.pop(),a.data.points.pop(),a.data.points=[...a.data.points,...n.toArray(),...n.toArray()],a.css3dTags[a.css3dTags.length-1].visible=!0,this.draw(this.data)}else{const s=a.data.points.length;a.data.points[s-3]=n.x,a.data.points[s-2]=n.y,a.data.points[s-1]=n.z;const r=a.css3dTags.length-1,c=new l.Vector3().fromArray(a.data.points.slice(s-6,s-3)),p=new l.Vector3().fromArray(a.data.points.slice(s-3)),u=new l.Vector3((c.x+p.x)/2,(c.y+p.y)/2,(c.z+p.z)/2),d=c.distanceTo(p),f=(d*a.events.fire(_).meterScale).toFixed(2)+" m";a.css3dTags[r].element.childNodes[0].innerText=f,a.geometry.setPositions([...a.data.points]),a.css3dTags[r].position.set(u.x,u.y,u.z),a.css3dTags[r].visible=o?!0:d>.5}t?.lineColor&&(e&&(a.data.lineColor=t.lineColor),a.material.color.set(t.lineColor)),t?.lineWidth&&(e&&(a.data.lineWidth=t.lineWidth),a.material.linewidth=t.lineWidth),t?.mainTagColor&&(e&&(a.data.mainTagColor=t.mainTagColor),document.querySelector(`.${a.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(a.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${a.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(a.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${a.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(a.data.mainTagVisible=t.mainTagVisible),a.css3dMainTag.visible=t.mainTagVisible),t?.distanceTagColor&&(e&&(a.data.distanceTagColor=t.distanceTagColor),document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach(r=>r.style.color=t.distanceTagColor)),t?.distanceTagBackground&&(e&&(a.data.distanceTagBackground=t.distanceTagBackground),document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach(r=>r.style.background=t.distanceTagBackground)),t?.distanceTagOpacity&&(e&&(a.data.distanceTagOpacity=t.distanceTagOpacity),document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach(r=>r.style.opacity=t.distanceTagOpacity.toString())),t?.distanceTagVisible!==void 0&&(e&&(a.data.distanceTagVisible=t.distanceTagVisible),a.css3dTags.forEach(s=>s.visible=t.distanceTagVisible)),t?.title!==void 0&&(e&&(a.data.title=t.title),this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(a.data.note=t.note),a.events.fire(Q)}updateByMeterScale(t){const e=this,n=[];for(let s=0,r=e.data.points.length/3;s<r;s++)n.push(new l.Vector3(e.data.points[s*3],e.data.points[s*3+1],e.data.points[s*3+2]));let o,a;for(let s=1;s<n.length;s++)o=n[s-1],a=n[s],e.css3dTags[s-1].element.childNodes[0].innerText=(o.distanceTo(a)*t).toFixed(2)+" m"}drawFinish(t){if(this.disposed)return;const e=this,n=e.data.points.length,o=new l.Vector3().fromArray(e.data.points.slice(n-6,n-3)),a=new l.Vector3().fromArray(e.data.points.slice(n-3));if((!t||o.distanceTo(a)<.001)&&(e.data.points.pop(),e.data.points.pop(),e.data.points.pop(),e.draw(e.data)),e.events.fire(bt),e.data.points.length<6){e.dispose();return}else{for(;e.css3dTags.length>e.data.points.length/3-1;){const c=e.css3dTags.pop();e.group.remove(c),c.element.parentElement?.removeChild(c.element)}e.css3dTags[e.css3dTags.length-1].visible=!0}const s=parent?.onActiveMark,r=e.getMarkData(!0);r.isNew=!0,r.meterScale=e.events.fire(_).meterScale,s?.(r)}draw(t,e=!1){if(this.disposed)return;const n=this;this.css3dTags=this.css3dTags||[];const o={type:"MarkMultiLines",name:t.name||"lines"+Date.now(),points:[...t.points],lineColor:t.lineColor||"#eeee00",lineWidth:t.lineWidth||3,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,distanceTagColor:t.distanceTagColor||"#000000",distanceTagBackground:t.distanceTagBackground||"#e0ffff",distanceTagOpacity:t.distanceTagOpacity||.9,distanceTagVisible:t.distanceTagVisible===void 0?!0:t.distanceTagVisible,title:t.title||"标记线"+(document.querySelectorAll(".mark-wrap-lines.main-warp").length+1),note:t.note||""},a=n.geometry,s=n.material,r=new ie;r.setPositions([...o.points]);const c=new zt({color:o.lineColor,linewidth:o.lineWidth});c.resolution.set(innerWidth,innerHeight),n.copy(new Dt(r,c)),a?.dispose(),s?.dispose();const p=o.name,u=o.points.length/3-1;if(!n.css3dMainTag){const d=document.createElement("div");d.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${p}-main-tag" style="color:${o.mainTagColor};background:${o.mainTagBackground};opacity:${o.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${o.title}</span>
                                     </div>`,d.classList.add("mark-wrap-lines",`${o.name}`,"main-warp"),d.style.position="absolute",d.style.borderRadius="4px",d.style.cursor="pointer",d.onclick=()=>{if(n.events.fire(_).markMode)return;const y=parent?.onActiveMark,g=n.getMarkData(!0);g.meterScale=n.events.fire(_).meterScale,y?.(g),n.events.fire(j)},d.oncontextmenu=y=>y.preventDefault();const f=new it(d);f.position.set(o.points[0],o.points[1],o.points[2]),f.element.style.pointerEvents="none",f.scale.set(.01,.01,.01),f.visible=o.mainTagVisible,n.group.add(f),n.css3dMainTag=f}for(let d=n.css3dTags.length;d<u;d++){const f=new l.Vector3().fromArray(o.points.slice(d*3,d*3+3)),y=new l.Vector3().fromArray(o.points.slice(d*3+3,d*3+6)),g=new l.Vector3((f.x+y.x)/2,(f.y+y.y)/2,(f.z+y.z)/2),m=(f.distanceTo(y)*n.events.fire(_).meterScale).toFixed(2)+" m",A=document.createElement("div");A.innerHTML=`<span class="${p}-distance-tag ${p}-distance-tag${d}" style="color:${o.distanceTagColor};background:${o.distanceTagBackground};opacity:${o.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${m}</span>`,A.classList.add("mark-wrap-lines",`${p}`,"distance-warp"),A.style.position="absolute",A.style.borderRadius="4px",A.style.display="none";const E=new it(A);E.position.copy(g),E.element.style.pointerEvents="none",E.scale.set(.008,.008,.008),E.visible=o.distanceTagVisible,n.css3dTags.push(E),n.group.add(E)}e||(n.css3dTags[n.css3dTags.length-1].visible=!1),n.data=o,n.events.fire(St,n)}getMarkData(t=!1){const e={...this.data};return t?delete e.points:e.points=[...e.points],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Ct,t),t.events.fire(V).remove(t),t.events.fire(Bt,t),t.geometry.dispose(),t.material.dispose(),document.querySelectorAll(`.${t.data.name}`).forEach(n=>n.parentElement?.removeChild(n)),t.events=null,t.data=null,t.css3dTags=null,t.group=null}}class zs extends l.Group{constructor(t,e,n){super(),this.isMark=!0,this.disposed=!1,this.events=t;const o=this;let a;if(e instanceof l.Vector3){const c=document.querySelectorAll(".mark-wrap-point").length+1;a={type:"MarkSinglePoint",name:n||"point"+Date.now(),point:e.toArray(),iconName:"#svgicon-point2",iconColor:"#eeee00",iconOpacity:.8,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,title:"标记点"+c,note:""}}else a={type:"MarkSinglePoint",name:e.name||"point"+Date.now(),point:[...e.point],iconName:e.iconName||"#svgicon-point2",iconColor:e.iconColor||"#eeee00",iconOpacity:e.iconOpacity||.8,mainTagColor:e.mainTagColor||"#c4c4c4",mainTagBackground:e.mainTagBackground||"#2E2E30",mainTagOpacity:e.mainTagOpacity||.8,title:e.title||"标记点",note:e.note||""};const s=document.createElement("div");s.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${a.name}" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                <svg height="20" width="20" style="color:${a.iconColor};opacity:${a.iconOpacity};"><use href="${a.iconName}" fill="currentColor" /></svg>
                             </div>`,s.classList.add("mark-wrap-point",`mark-wrap-${a.name}`),s.style.position="absolute",s.style.borderRadius="4px",s.style.cursor="pointer",s.onclick=()=>{if(o.events.fire(_).markMode)return;const c=parent?.onActiveMark;c?.(o.getMarkData(!0)),o.events.fire(j)},s.oncontextmenu=c=>c.preventDefault();const r=new it(s);r.position.set(a.point[0],a.point[1],a.point[2]),r.element.style.pointerEvents="none",r.scale.set(.01,.01,.01),o.data=a,o.css3dTag=r,o.add(r),t.fire(St,o)}drawUpdate(t,e=!0){if(this.disposed)return;const n=this;if(t?.iconName){e&&(n.data.iconName=t.iconName);const o=this.css3dTag.element.querySelector(`.mark-wrap-${n.data.name} svg`);o.innerHTML=`<use href="${t.iconName}" fill="currentColor" />`}if(t?.iconColor){e&&(n.data.iconColor=t.iconColor);const o=this.css3dTag.element.querySelector(`.mark-wrap-${n.data.name} svg`);o.style.color=t.iconColor}if(t?.iconOpacity){e&&(n.data.iconOpacity=t.iconOpacity);const o=this.css3dTag.element.querySelector(`.mark-wrap-${n.data.name} svg`);o.style.opacity=t.iconOpacity.toString()}t?.mainTagColor&&(e&&(n.data.mainTagColor=t.mainTagColor),this.css3dTag.element.querySelector(`.${n.data.name}`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(n.data.mainTagBackground=t.mainTagBackground),this.css3dTag.element.querySelector(`.${n.data.name}`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(n.data.mainTagOpacity=t.mainTagOpacity),this.css3dTag.element.querySelector(`.${n.data.name}`).style.opacity=t.mainTagOpacity.toString()),t?.title!==void 0&&(e&&(n.data.title=t.title),this.css3dTag.element.querySelector(`.${n.data.name}`).innerText=t.title),t?.note!==void 0&&e&&(n.data.note=t.note),n.events.fire(Q)}resetMeterScale(t){t?.meterScale!==void 0&&(this.events.fire(_).meterScale=t.meterScale)}drawFinish(){if(this.disposed)return;const t=this;t.events.fire(bt);const e=parent?.onActiveMark,n=t.getMarkData(!0);n.isNew=!0,n.meterScale=t.events.fire(_).meterScale,e?.(n)}getMarkData(t=!1){const e={...this.data};return t?delete e.point:e.point=[...e.point],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Ct,t),t.events.fire(V).remove(t),t.events.fire(Bt,t);const e=document.querySelector(`.mark-wrap-${t.data.name}`);e?.parentElement?.removeChild?.(e),t.events=null,t.data=null,t.css3dTag=null}}class Nn extends Dt{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.css3dTags=[],this.group=new l.Group,this.add(this.group),this.events=t}drawStart(t,e){if(this.disposed)return;const n=document.querySelectorAll(".mark-wrap-plans.main-warp").length+1,o={type:"MarkMultiPlans",name:e||"plans"+Date.now(),points:[...t.toArray(),...t.toArray()],lineColor:"#eeee00",lineWidth:3,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,areaTagColor:"#000000",areaTagBackground:"#e0ffff",areaTagOpacity:.8,distanceTagVisible:!0,areaTagVisible:!0,planOpacity:.5,title:"标记面"+n,note:""};this.draw(o)}drawUpdate(t,e=!0,n,o=!1){if(this.disposed)return;const a=this;if(n)if(o){const s=a.data.points.length,r=a.css3dTags.length-1,c=new l.Vector3().fromArray(a.data.points.slice(s-6,s-3)),p=n,u=new l.Vector3((c.x+p.x)/2,(c.y+p.y)/2,(c.z+p.z)/2),f=(c.distanceTo(p)*a.events.fire(_).meterScale).toFixed(2)+" m";a.css3dTags[r].element.innerText=f,a.css3dTags[r].position.set(u.x,u.y,u.z),a.css3dTags[r].visible=!0,a.data.points.pop(),a.data.points.pop(),a.data.points.pop(),a.data.points=[...a.data.points,...n.toArray(),...n.toArray()],this.draw(this.data)}else{const s=a.data.points.length;a.data.points[s-3]=n.x,a.data.points[s-2]=n.y,a.data.points[s-1]=n.z;const r=a.css3dTags.length-1,c=new l.Vector3().fromArray(a.data.points.slice(s-6,s-3)),p=new l.Vector3().fromArray(a.data.points.slice(s-3)),u=new l.Vector3((c.x+p.x)/2,(c.y+p.y)/2,(c.z+p.z)/2),d=c.distanceTo(p),f=(d*a.events.fire(_).meterScale).toFixed(2)+" m";a.css3dTags[r].element.innerText=f,a.geometry.setPositions([...a.data.points]),a.css3dTags[r].position.set(u.x,u.y,u.z),a.css3dTags[r].visible=o?!0:d>.5,a.meshPlans.geometry.setAttribute("position",new l.BufferAttribute(new Float32Array(a.data.points),3)),a.meshPlans.geometry.attributes.position.needsUpdate=!0;const y=a.events.fire(Ge,a.data.points);a.css3dAreaTag.position.copy(y);const g=a.events.fire(Xe,a.data.points);a.css3dAreaTag.element.childNodes[0].innerText=g.toFixed(2)+" m²"}if(t?.lineColor&&(e&&(a.data.lineColor=t.lineColor),a.material.color.set(t.lineColor),a.meshPlans.material.color.set(t.lineColor)),t?.lineWidth&&(e&&(a.data.lineWidth=t.lineWidth),a.material.linewidth=t.lineWidth),t?.mainTagColor&&(e&&(a.data.mainTagColor=t.mainTagColor),document.querySelector(`.${a.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(a.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${a.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(a.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${a.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(a.data.mainTagVisible=t.mainTagVisible),a.css3dMainTag.visible=t.mainTagVisible),t?.areaTagVisible!==void 0&&(e&&(a.data.areaTagVisible=t.areaTagVisible),a.css3dAreaTag.visible=t.areaTagVisible),t?.areaTagColor){e&&(a.data.areaTagColor=t.areaTagColor);const s=document.querySelector(`.${a.data.name}-area-tag`);s&&(s.style.color=t.areaTagColor)}if(t?.areaTagBackground){e&&(a.data.areaTagBackground=t.areaTagBackground);const s=document.querySelector(`.${a.data.name}-area-tag`);s&&(s.style.background=t.areaTagBackground)}if(t?.areaTagOpacity){e&&(a.data.areaTagOpacity=t.areaTagOpacity);const s=document.querySelector(`.${a.data.name}-area-tag`);s&&(s.style.opacity=t.areaTagOpacity.toString())}t?.distanceTagVisible!==void 0&&(e&&(a.data.distanceTagVisible=t.distanceTagVisible),a.css3dTags.forEach(s=>s.visible=t.distanceTagVisible)),t?.planOpacity&&(e&&(a.data.planOpacity=t.planOpacity),this.meshPlans.material.opacity=t.planOpacity),t?.title!==void 0&&(e&&(a.data.title=t.title),this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(a.data.note=t.note),a.events.fire(Q)}updateByMeterScale(t){const e=this,n=[];for(let r=0,c=e.data.points.length/3;r<c;r++)n.push(new l.Vector3(e.data.points[r*3],e.data.points[r*3+1],e.data.points[r*3+2]));let o,a;for(let r=1;r<n.length;r++)o=n[r-1],a=n[r],e.css3dTags[r-1].element.innerText=(o.distanceTo(a)*t).toFixed(2)+" m";const s=e.events.fire(ti,n,t);e.css3dAreaTag.element.childNodes[0].innerText=s.toFixed(2)+" m²"}drawFinish(t){if(this.disposed)return;const e=this,n=e.data.points.length,o=new l.Vector3().fromArray(e.data.points.slice(n-6,n-3)),a=new l.Vector3().fromArray(e.data.points.slice(n-3));if((!t||o.distanceTo(a)<1e-4)&&(e.data.points.pop(),e.data.points.pop(),e.data.points.pop()),e.events.fire(bt),e.data.points.length<9){e.dispose();return}for(;e.css3dTags.length>e.data.points.length/3-1;){const c=e.css3dTags.pop();e.group.remove(c),c.element.parentElement?.removeChild(c.element)}e.data.points.push(e.data.points[0],e.data.points[1],e.data.points[2]),e.draw(e.data,!0),e.css3dTags[e.css3dTags.length-1].visible=!0;const s=parent?.onActiveMark,r=e.getMarkData(!0);r.isNew=!0,r.meterScale=e.events.fire(_).meterScale,s?.(r)}draw(t,e=!1){if(this.disposed)return;const n=this;this.css3dTags=this.css3dTags||[];const o={type:"MarkMultiPlans",name:t.name||"plans"+Date.now(),points:[...t.points],lineColor:t.lineColor||"#eeee00",lineWidth:t.lineWidth||3,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,areaTagColor:t.areaTagColor||"#000000",areaTagBackground:t.areaTagBackground||"#e0ffff",areaTagOpacity:t.areaTagOpacity||.9,areaTagVisible:t.areaTagVisible===void 0?!0:t.areaTagVisible,distanceTagVisible:t.distanceTagVisible===void 0?!0:t.distanceTagVisible,planOpacity:t.planOpacity||.5,title:t.title||"标记面"+(document.querySelectorAll(".mark-wrap-plans.main-warp").length+1),note:t.note||""},a=n.geometry,s=n.material,r=new ie;r.setPositions([...o.points]);const c=new zt({color:o.lineColor,linewidth:o.lineWidth});c.resolution.set(innerWidth,innerHeight),n.copy(new Dt(r,c)),a?.dispose(),s?.dispose();const p=o.name,u=o.points.length/3-1;if(!n.css3dMainTag){const y=document.createElement("div");y.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${p}-main-tag" style="color:${o.mainTagColor};background:${o.mainTagBackground};opacity:${o.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${o.title}</span>
                                     </div>`,y.classList.add("mark-wrap-plans",`${o.name}`,"main-warp"),y.style.position="absolute",y.style.borderRadius="4px",y.style.cursor="pointer",y.onclick=()=>{if(n.events.fire(_).markMode)return;const w=parent?.onActiveMark,m=n.getMarkData(!0);m.meterScale=n.events.fire(_).meterScale,w?.(m),n.events.fire(j)},y.oncontextmenu=w=>w.preventDefault();const g=new it(y);g.position.set(o.points[0],o.points[1],o.points[2]),g.element.style.pointerEvents="none",g.scale.set(.01,.01,.01),g.visible=o.mainTagVisible,n.group.add(g),n.css3dMainTag=g}const d=n.events.fire(Xe,o.points).toFixed(2)+" m²",f=n.events.fire(Ge,o.points);if(n.css3dAreaTag)n.css3dAreaTag.position.copy(f),n.css3dAreaTag.element.childNodes[0].innerText=d;else{const y=document.createElement("div");y.innerHTML=`<span class="${p}-area-tag" style="color:${o.areaTagColor};background:${o.areaTagBackground};opacity:${o.areaTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: none;">${d}</span>`,y.classList.add("mark-wrap-plans",`${o.name}`,"area-warp"),y.style.position="absolute",y.style.borderRadius="4px";const g=new it(y);g.position.copy(f),g.element.style.pointerEvents="none",g.scale.set(.01,.01,.01),n.group.add(g),n.css3dAreaTag=g}n.css3dAreaTag.visible=o.points.length>6&&o.areaTagVisible;for(let y=n.css3dTags.length;y<u;y++){const g=new l.Vector3().fromArray(o.points.slice(y*3,y*3+3)),w=new l.Vector3().fromArray(o.points.slice(y*3+3,y*3+6)),m=new l.Vector3((g.x+w.x)/2,(g.y+w.y)/2,(g.z+w.z)/2),E=(g.distanceTo(w)*n.events.fire(_).meterScale).toFixed(2)+" m",x=document.createElement("div");x.innerText=E,x.style.color="white",x.classList.add("mark-wrap-plans",`${p}`,"distance-warp"),x.style.position="absolute",x.style.borderRadius="4px",x.style.display="none";const b=new it(x);b.position.copy(m),b.element.style.pointerEvents="none",b.scale.set(.008,.008,.008),b.visible=o.distanceTagVisible,n.css3dTags.push(b),n.group.add(b)}n.drawPlans(o),e||(n.css3dTags[n.css3dTags.length-1].visible=!1),n.data=o,n.events.fire(St,n)}drawPlans(t){const e=this;if(!e.meshPlans){const n=new l.BufferGeometry;n.setAttribute("position",new l.BufferAttribute(new Float32Array,3)),n.attributes.position.needsUpdate=!0;const o=new l.BufferAttribute(new Uint16Array([]),1);n.setIndex(o);const a=new l.MeshBasicMaterial({color:t.lineColor,transparent:!0,opacity:t.planOpacity,side:l.DoubleSide});e.meshPlans=new l.Mesh(n,a),e.meshPlans.renderOrder=1,e.meshPlans.isMark=!0,e.group.add(e.meshPlans)}if(t.points.length>6){e.meshPlans.geometry.setAttribute("position",new l.BufferAttribute(new Float32Array(t.points),3)),e.meshPlans.geometry.attributes.position.needsUpdate=!0;let n=t.points.length/3-2;const o=new l.Vector3().fromArray(t.points.slice(0,3)),a=new l.Vector3().fromArray(t.points.slice(-3));o.distanceTo(a)<1e-4&&n--;const s=new Uint16Array(n*3);for(let r=0;r<n;r++)s[r*3]=0,s[r*3+1]=r+1,s[r*3+2]=r+2;e.meshPlans.geometry.setIndex(new l.BufferAttribute(s,1))}}getMarkData(t=!1){const e={...this.data};return t?delete e.points:e.points=[...e.points],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Ct,t),t.events.fire(V).remove(t),t.events.fire(Bt,t),t.geometry.dispose(),t.material.dispose(),document.querySelectorAll(`.${t.data.name}`).forEach(n=>n.parentElement?.removeChild(n)),t.events=null,t.data=null,t.css3dTags=null,t.group=null,t.meshPlans=null,t.css3dMainTag=null,t.css3dAreaTag=null}}class Qs extends l.Group{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.events=t}drawStart(t,e){if(this.disposed)return;const n=this,o=document.querySelectorAll(".mark-wrap-circle.main-warp").length+1,a={type:"MarkCirclePlan",name:e||"circle"+Date.now(),startPoint:t.toArray(),radius:.05,circleColor:"#eeee00",circleOpacity:.5,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,circleTagColor:"#000000",circleTagBackground:"#e0ffff",circleTagOpacity:.9,circleTagVisible:!0,title:"标记圆面"+o},s=new l.CircleGeometry(a.radius,32),r=new l.MeshBasicMaterial({color:a.circleColor,side:l.DoubleSide,transparent:!0});r.opacity=a.circleOpacity;const c=new l.Mesh(s,r);c.position.copy(t),c.isMark=!0,c.renderOrder=1;const p=new l.Vector3(0,1,0).normalize();c.lookAt(c.position.clone().add(p));const u=document.createElement("div");u.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`,u.classList.add("mark-wrap-circle",`${a.name}`,"main-warp"),u.style.position="absolute",u.style.borderRadius="4px",u.style.cursor="pointer",u.onclick=()=>{if(n.events.fire(_).markMode)return;const g=parent?.onActiveMark;g?.(n.getMarkData(!0)),n.events.fire(j)},u.oncontextmenu=g=>g.preventDefault();const d=new it(u);d.position.copy(t),d.element.style.pointerEvents="none",d.scale.set(.01,.01,.01),d.visible=a.mainTagVisible;const f=document.createElement("div");f.innerHTML=`<span class="${e}-circle-tag" style="color:${a.circleTagColor};background:${a.circleTagBackground};opacity:${a.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`,f.classList.add("mark-wrap-circle",`${e}`,"circle-warp"),f.style.position="absolute",f.style.borderRadius="4px",f.style.pointerEvents="none";const y=new it(f);y.position.set(t.x+Math.min(a.radius/2,.5),t.y,t.z),y.element.style.pointerEvents="none",y.scale.set(.008,.008,.008),y.visible=!1,n.add(c,d,y),n.data=a,n.circleMesh=c,n.css3dTag=y,n.css3dMainTag=d,n.events.fire(St,n)}drawUpdate(t,e=!0,n){if(this.disposed)return;const o=this;if(n){const a=new l.Vector3().fromArray(o.data.startPoint),s=a.distanceTo(new l.Vector3(n.x,a.y,n.z));e&&(o.data.radius=s),o.circleMesh.geometry.copy(new l.CircleGeometry(s,128)),o.css3dTag.visible=s>.3;const r=s*o.events.fire(_).meterScale,c=(Math.PI*r*r).toFixed(2)+" m²";o.css3dTag.element.childNodes[0].innerText=c,o.css3dTag.position.set(a.x+Math.min(o.data.radius/2,.5),a.y,a.z)}if(t?.radius){const a=new l.Vector3().fromArray(o.data.startPoint),s=t?.radius;e&&(o.data.radius=s),o.circleMesh.geometry.copy(new l.CircleGeometry(s,128)),o.css3dTag.visible=s>.3;const r=s*o.events.fire(_).meterScale,c=(Math.PI*r*r).toFixed(2)+" m²";o.css3dTag.element.childNodes[0].innerText=c,o.css3dTag.position.set(a.x+Math.min(s/2,.5),a.y,a.z)}t?.circleColor&&(e&&(o.data.circleColor=t.circleColor),o.circleMesh.material.color.set(t.circleColor)),t?.circleOpacity&&(e&&(o.data.circleOpacity=t.circleOpacity),o.circleMesh.material.opacity=t.circleOpacity),t?.mainTagColor&&(e&&(o.data.mainTagColor=t.mainTagColor),document.querySelector(`.${o.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(o.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${o.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(o.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${o.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(o.data.mainTagVisible=t.mainTagVisible),o.css3dMainTag.visible=t.mainTagVisible),t?.circleTagColor&&(e&&(o.data.circleTagColor=t.circleTagColor),document.querySelector(`.${o.data.name}-circle-tag`).style.color=t.circleTagColor),t?.circleTagBackground&&(e&&(o.data.circleTagBackground=t.circleTagBackground),document.querySelector(`.${o.data.name}-circle-tag`).style.background=t.circleTagBackground),t?.circleTagOpacity&&(e&&(o.data.circleTagOpacity=t.circleTagOpacity),document.querySelector(`.${o.data.name}-circle-tag`).style.opacity=t.circleTagOpacity.toString()),t?.circleTagVisible!==void 0&&(e&&(o.data.circleTagVisible=t.circleTagVisible),o.css3dTag.visible=t.circleTagVisible),t?.title!==void 0&&(e&&(o.data.title=t.title),this.css3dMainTag.element.querySelector(`.${o.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(o.data.note=t.note),o.events.fire(Q)}updateByMeterScale(t){const e=this,n=e.data.radius*t,o=(Math.PI*n*n).toFixed(2)+" m²";e.css3dTag.element.childNodes[0].innerText=o}drawFinish(t){if(this.disposed)return;const e=this,n=new l.Vector3().fromArray(e.data.startPoint),o=n.distanceTo(new l.Vector3(t.x,n.y,t.z));e.data.radius=o,e.circleMesh.geometry.copy(new l.CircleGeometry(o,128));const a=e.data.radius*e.events.fire(_).meterScale,s=(Math.PI*a*a).toFixed(2)+" m²";e.css3dTag.element.childNodes[0].innerText=s,e.events.fire(bt);const r=parent?.onActiveMark,c=e.getMarkData(!0);c.isNew=!0,c.meterScale=e.events.fire(_).meterScale,r?.(c)}draw(t){if(this.disposed)return;const e=this,n={type:"MarkCirclePlan",name:t.name||"circle"+Date.now(),startPoint:[...t.startPoint],radius:t.radius,circleColor:t.circleColor||"#eeee00",circleOpacity:t.circleOpacity||.5,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,circleTagColor:t.circleTagColor||"#000000",circleTagBackground:t.circleTagBackground||"#e0ffff",circleTagOpacity:t.circleTagOpacity||.9,circleTagVisible:t.circleTagVisible===void 0?!0:t.circleTagVisible,title:t.title||"标记圆面"},o=new l.CircleGeometry(n.radius,128),a=new l.MeshBasicMaterial({color:n.circleColor,side:l.DoubleSide,transparent:!0});a.opacity=n.circleOpacity;const s=new l.Mesh(o,a);s.position.fromArray(n.startPoint),s.isMark=!0,s.renderOrder=1;const r=new l.Vector3(0,1,0).normalize();s.lookAt(s.position.clone().add(r));const c=n.name,p=document.createElement("div");p.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${c}-main-tag" style="color:${n.mainTagColor};background:${n.mainTagBackground};opacity:${n.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${n.title}</span>
                             </div>`,p.classList.add("mark-wrap-circle",`${n.name}`,"main-warp"),p.style.position="absolute",p.style.borderRadius="4px",p.style.cursor="pointer",p.onclick=()=>{if(e.events.fire(_).markMode)return;const m=parent?.onActiveMark;m?.(e.getMarkData(!0)),e.events.fire(j)},p.oncontextmenu=m=>m.preventDefault();const u=new l.Vector3().fromArray(n.startPoint),d=n.radius*e.events.fire(_).meterScale,f=(Math.PI*d*d).toFixed(2)+" m²",y=new it(p);y.position.copy(u),y.element.style.pointerEvents="none",y.scale.set(.01,.01,.01),y.visible=n.mainTagVisible;const g=document.createElement("div");g.innerHTML=`<span class="${c}-distance-tag ${c}-circle-tag" style="color:${n.circleTagColor};background:${n.circleTagBackground};opacity:${n.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${f}</span>`,g.classList.add("mark-wrap-circle",`${c}`,"circle-warp"),g.style.position="absolute",g.style.borderRadius="4px",g.style.pointerEvents="none";const w=new it(g);w.position.set(u.x+Math.min(n.radius/2,.5),u.y,u.z),w.element.style.pointerEvents="none",w.scale.set(.008,.008,.008),w.visible=n.circleTagVisible,e.add(s,y,w),e.data=n,e.circleMesh=s,e.css3dTag=w,e.css3dMainTag=y,e.events.fire(St,e)}getMarkData(t=!1){const e={...this.data};return t?(delete e.startPoint,delete e.radius):e.startPoint=[...e.startPoint],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Ct,t),t.events.fire(V).remove(t),t.events.fire(Bt,t),document.querySelectorAll(`.${t.data.name}`).forEach(n=>n.parentElement?.removeChild(n)),t.events=null,t.data=null,t.circleMesh=null,t.css3dTag=null,t.css3dMainTag=null}}let Xa=class{constructor(){this.down=0,this.move=!1,this.downTime=0,this.isDbClick=!1,this.x=0,this.y=0,this.lastClickX=0,this.lastClickY=0,this.lastClickPointTime=0,this.lastMovePoint=null,this.lastMovePointTime=0,this.touchStartDistance=0,this.touchPrevDistance=0,this.touchStartX1=0,this.touchStartY1=0,this.touchStartX2=0,this.touchStartY2=0,this.touchPrevX1=0,this.touchPrevY1=0,this.touchPrevX2=0,this.touchPrevY2=0}};function Na(i){const t=(h,M,C)=>i.on(h,M,C),e=(h,...M)=>i.fire(h,...M),n=e(_t);let o=new Set,a,s=new Xa,r,c=0,p=0;function u(){const h=e(O),M=new l.Vector3;h.getWorldDirection(M),c=Math.asin(M.y),p=Math.atan2(M.z,M.x)}t(ci,()=>{e(U).autoRotate=e(_).autoRotate=!0}),t(j,(h=!0)=>{e(U).autoRotate=e(_).autoRotate=!1,h&&e(Ce)}),t(sn,(h=!0)=>{if(a)return;const M=e(U),C=h?Math.PI/128:-(Math.PI/128),v=new l.Matrix4().makeRotationAxis(new l.Vector3(0,0,-1).transformDirection(M.object.matrixWorld),C);M.object.up.transformDirection(v),e(Q)}),t(on,()=>{if(a)return;if(e(_).useCustomControl===!0){const C=e(O),v=e(V);let k;if(v.traverse(D=>{D instanceof ct&&(k=D)}),k){const D=k.position.clone(),L=C.position.clone(),B=D.sub(L).normalize(),Z=-(Math.PI/128),lt=new l.Quaternion().setFromAxisAngle(B,Z);k.applyQuaternion(lt)}e(Q)}else i.fire(sn,!0)}),t(an,()=>{if(a)return;if(e(_).useCustomControl===!0){const C=e(O),v=e(V);let k;if(v.traverse(D=>{D instanceof ct&&(k=D)}),k){const D=k.position.clone(),L=C.position.clone(),B=D.sub(L).normalize(),Z=Math.PI/128,lt=new l.Quaternion().setFromAxisAngle(B,Z);k.applyQuaternion(lt)}e(Q)}else i.fire(sn,!1)}),t(we,h=>{const M=e(_);h??(h=!M.pointcloudMode),M.pointcloudMode=h,e(V).traverse(v=>v instanceof ct&&v.fire(Ft,h))}),t(ve,()=>{e(V).traverse(M=>M instanceof ct&&M.fire(nn))}),t(Gt,h=>{e(V).traverse(C=>C instanceof ct&&C.fire(Gt,h))}),t(qi,()=>{const h=e(V);let M;h.traverse(C=>C instanceof ct&&(M=C)),M&&e(Ke,M.fire(ji))}),t(Me,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=e(_),k=v.useCustomControl===!0,D=h!==void 0?h:v.cameraMoveSpeed!==void 0?v.cameraMoveSpeed:.005,L=new l.Vector3;if(M.getWorldDirection(L),M.position.addScaledVector(L,D),k){const B=new l.Vector3().copy(M.position).add(L);C.target.copy(B)}else C.target.addScaledVector(L,D);e(Q)}),t(xe,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=e(_),k=h!==void 0?h:v.cameraMoveSpeed!==void 0?v.cameraMoveSpeed:.005,D=new l.Vector3;M.getWorldDirection(D),M.position.addScaledVector(D,-k),C.target.addScaledVector(D,-k),e(Q)}),t(qt,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=e(_),k=h!==void 0?h:v.cameraMoveSpeed!==void 0?v.cameraMoveSpeed:.005,D=new l.Vector3,L=new l.Vector3(0,1,0),B=new l.Vector3;M.getWorldDirection(B),D.crossVectors(L,B).normalize(),M.position.addScaledVector(D,-k),C.target.addScaledVector(D,-k),e(Q)}),t(Jt,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=e(_),k=h!==void 0?h:v.cameraMoveSpeed!==void 0?v.cameraMoveSpeed:.005,D=new l.Vector3,L=new l.Vector3(0,1,0),B=new l.Vector3;M.getWorldDirection(B),D.crossVectors(L,B).normalize(),M.position.addScaledVector(D,k),C.target.addScaledVector(D,k),e(Q)}),t(Te,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=e(_),k=v.useCustomControl===!0,D=h!==void 0?h:v.cameraMoveSpeed!==void 0?v.cameraMoveSpeed:.005;if(k){const L=new l.Vector3(0,1,0).applyQuaternion(M.quaternion);console.log("[CameraMoveUp] 第一人称模式下使用屏幕上方向:",L.toArray()),M.position.addScaledVector(L,D);const B=new l.Vector3;M.getWorldDirection(B);const Z=new l.Vector3().copy(M.position).add(B);C.target.copy(Z)}else{const L=new l.Vector3(0,1,0);M.position.addScaledVector(L,-D),C.target.addScaledVector(L,-D)}e(Q)}),t(ke,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=e(_),k=v.useCustomControl===!0,D=h!==void 0?h:v.cameraMoveSpeed!==void 0?v.cameraMoveSpeed:.005;if(k){const L=new l.Vector3(0,1,0).applyQuaternion(M.quaternion);console.log("[CameraMoveDown] 第一人称模式下使用屏幕下方向:",L.toArray()),M.position.addScaledVector(L,-D);const B=new l.Vector3;M.getWorldDirection(B);const Z=new l.Vector3().copy(M.position).add(B);C.target.copy(Z)}else{const L=new l.Vector3(0,1,0);M.position.addScaledVector(L,D),C.target.addScaledVector(L,D)}e(Q)}),t(Ee,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=M.position.clone();c+=h!==void 0?h:.05,c=Math.min(Math.PI/2-.1,Math.max(-Math.PI/2+.1,c));const D=new l.Vector3(Math.cos(c)*Math.cos(p),Math.sin(c),Math.cos(c)*Math.sin(p)).normalize(),L=new l.Vector3().copy(v).add(D);C.target.copy(L),M.lookAt(L),M.position.copy(v),e(Q)}),t(Ie,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=M.position.clone();c-=h!==void 0?h:.05,c=Math.min(Math.PI/2-.1,Math.max(-Math.PI/2+.1,c));const D=new l.Vector3(Math.cos(c)*Math.cos(p),Math.sin(c),Math.cos(c)*Math.sin(p)).normalize(),L=new l.Vector3().copy(v).add(D);C.target.copy(L),M.lookAt(L),M.position.copy(v),e(Q)}),t(De,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=M.position.clone();console.log("[CameraRotateLeftFPS] 旋转前相机位置:",v.toArray()),p+=h!==void 0?h:.05;const D=new l.Vector3(Math.cos(c)*Math.cos(p),Math.sin(c),Math.cos(c)*Math.sin(p)).normalize(),L=new l.Vector3().copy(v).add(D);C.target.copy(L),M.lookAt(L),M.position.copy(v),console.log("[CameraRotateLeftFPS] 旋转后相机位置:",M.position.toArray()),C.minDistance=C.maxDistance=C.getDistance(),e(Q)}),t(_e,(h=void 0)=>{if(a)return;const M=e(O),C=e(U),v=M.position.clone();p-=h!==void 0?h:.05;const D=new l.Vector3(Math.cos(c)*Math.cos(p),Math.sin(c),Math.cos(c)*Math.sin(p)).normalize(),L=new l.Vector3().copy(v).add(D);C.target.copy(L),M.lookAt(L),M.position.copy(v),C.minDistance=C.maxDistance=C.getDistance(),e(Q)}),t(he,()=>{if(!o.size)return;const h=e(_);if(!h.enableKeyboard)return o.clear();if(h.markMode&&o.has("Escape")){e(Zt),o.clear();return}const M=h.useCustomControl===!0,C=o.has("Shift");C&&M?(o.has("KeyW")&&e(Ie,.01),o.has("KeyS")&&e(Ee,.01),o.has("KeyA")&&e(De,.01),o.has("KeyD")&&e(_e,.01)):M&&!C&&(o.has("KeyW")&&e(Me),o.has("KeyS")&&e(xe),o.has("KeyA")&&e(qt),o.has("KeyD")&&e(Jt),o.has("ArrowLeft")&&e(qt),o.has("ArrowRight")&&e(Jt),o.has("ArrowUp")&&e(Te),o.has("ArrowDown")&&e(ke)),o.has("Space")?(h.bigSceneMode?e(we):e(ve),o.clear()):o.has("KeyR")?(h.autoRotate?e(j):e(ci),o.clear()):o.has("KeyM")?(e(Ut,!h.markVisible),o.clear()):M&&o.has("KeyQ")?(e(on),o.clear()):M&&o.has("KeyE")?(e(an),o.clear()):!M&&o.has("ArrowLeft")?(e(on),e(me,!0),o.clear()):!M&&o.has("ArrowRight")?(e(an),e(me,!0),o.clear()):o.has("KeyP")?(e(be,!0),o.clear()):o.has("Equal")?(e(mn),o.clear()):o.has("Minus")?(e(yn),o.clear()):o.has("KeyY")?(e(fn),o.clear()):o.has("KeyI")?(e(Xi),o.clear()):o.has("KeyF")?(e(qi),o.clear()):o.has("F2")&&(!h.bigSceneMode&&window.open("/editor/index.html?url="+encodeURIComponent(e(wt).meta.url)),o.clear())}),t(Nt,async(h,M)=>{if(s.move)return;if(e(_).useCustomControl===!0){console.log("[SelectPointAndLookAt] 第一人称模式下忽略点击选择");return}const k=await e(ge,h,M);k.length&&(console.log("[SelectPointAndLookAt] 设置相机目标点:",k[0].toArray()),e(Ke,k[0],!0))}),t(dt,async(h,M)=>{const C=e(V),v=[];C.traverse(D=>D instanceof ct&&v.push(D));const k=await e(ge,h,M);return k.length?(v.length&&v[0].fire(re,k[0].x,k[0].y,k[0].z,!0),new l.Vector3(k[0].x,k[0].y,k[0].z)):(v.length&&v[0].fire(re,0,0,0,!1),null)}),t(ln,async()=>{const h=e(V),M=[];h.traverse(C=>C instanceof ct&&M.push(C));for(let C=0;C<M.length;C++)M[C].fire(re,0,0,0,!1)});const d=h=>{h.target.type!=="text"&&(a||h.code==="F5"||(h.preventDefault(),h.code!=="KeyR"&&(o.add(h.code),(h.code==="ShiftLeft"||h.code==="ShiftRight")&&o.add("Shift"),e(j)),r=Date.now()))},f=h=>{h.target.type!=="text"&&(a||(h.code==="KeyR"&&o.add(h.code),(h.code==="ArrowLeft"||h.code==="ArrowRight")&&e(si),o.delete(h.code),(h.code==="ShiftLeft"||h.code==="ShiftRight")&&o.delete("Shift"),r=Date.now()))};t(Et,()=>{e(dn)&&Date.now()-r>2e3&&(e(me,!1),e(fn))},!0);const y=()=>{o.clear()},g=h=>{if(parent&&setTimeout(()=>window.focus()),h.preventDefault(),a)return;if(e(j),e(_).useCustomControl===!0){const v=e(O),k=e(U),D=h.deltaY>0?1.1:.9;console.log("[Wheel] useCustomControl模式下缩放系数:",D);const L=v.position.clone().sub(k.target).normalize(),B=v.position.distanceTo(k.target),Z=Math.max(.1,Math.min(1e3,B*D)),lt=k.target.clone().add(L.multiplyScalar(Z));v.position.copy(lt),e(Q),console.log("[Wheel] useCustomControl模式下缩放，距离:",Z.toFixed(2))}r=Date.now()},w=h=>{h.preventDefault(),!a&&(e(j),r=Date.now())};let m,A,E,x;t(Zt,()=>{m?.dispose(),A?.dispose(),E?.dispose(),x?.dispose(),m=null,A=null,E=null,x=null,s.lastMovePoint=null,e(bt)});const b=async h=>{if(parent&&setTimeout(()=>window.focus()),h.preventDefault(),a)return;e(j),h.button===1?s.down=3:s.down=h.button===2?2:1,s.move=!1,s.isDbClick=Date.now()-s.downTime<300,s.x=h.clientX,s.y=h.clientY;const M=e(_);if(M.useCustomControl===!0){const v=e(O),k=v.position.clone();console.log("[MouseDown] FPS模式下相机位置:",k.toArray());const D=e(U);D.enabled=!1,D.minDistance=D.maxDistance=D.getDistance(),D.updateControlMode(M),console.log("[MouseDown] FPS模式下已禁用控制器，按钮:",s.down),s.down===1&&(console.log("[MouseDown] FPS模式下左键点击，位置:",h.clientX,h.clientY),Math.abs(c)<.001&&Math.abs(p)<.001&&(console.log("[MouseDown] 初始化欧拉角"),u()),v.position.copy(k),h.stopPropagation())}r=Date.now(),s.downTime=Date.now()},P=async h=>{if(h.preventDefault(),a)return;const M=e(_),C=M.useCustomControl===!0;if(s.down){const v=h.clientX-s.x,k=h.clientY-s.y;if(s.down===3){h.stopPropagation();const D=e(U);D.enabled=!1;const L=M.cameraMoveSpeed!==void 0?M.cameraMoveSpeed:.005,B=.5;Math.abs(k)>2&&(k>0?e(xe,Math.abs(k)*B*L):e(Me,Math.abs(k)*B*L)),s.x=h.clientX,s.y=h.clientY,e(Q),s.move=!0,r=Date.now();return}if(s.down===2&&C){h.stopPropagation();const D=e(U);D.enabled=!1;const L=M.cameraMoveSpeed!==void 0?M.cameraMoveSpeed:.005,B=.5;Math.abs(v)>2&&(v>0?e(qt,Math.abs(v)*B*L):e(Jt,Math.abs(v)*B*L)),Math.abs(k)>2&&(k>0?e(Te,Math.abs(k)*B*L):e(ke,Math.abs(k)*B*L)),s.x=h.clientX,s.y=h.clientY,e(Q),s.move=!0,r=Date.now();return}if(C&&s.down===1){h.stopPropagation();const D=e(U);D.enabled=!1;const L=e(O),B=L.position.clone();console.log("[MouseMove] FPS模式下左键拖动，移动距离:",v,k);const Z=.001,lt=.001;Math.abs(c)<.001&&Math.abs(p)<.001&&u(),Math.abs(v)>0&&(v>0?e(_e,Math.abs(v)*Z):e(De,Math.abs(v)*Z)),Math.abs(k)>0&&(k>0?e(Ee,Math.abs(k)*lt):e(Ie,Math.abs(k)*lt)),L.position.copy(B),D.minDistance=D.maxDistance=D.getDistance(),s.x=h.clientX,s.y=h.clientY,e(Q),s.move=!0,r=Date.now();return}s.move=!0,r=Date.now()}if(!C&&M.markMode){const v=await e(dt,h.clientX,h.clientY);v&&!s.down&&M.markType==="distance"&&m?m.drawUpdate({endPoint:v.toArray()}):!s.down&&M.markType==="circle"&&x?v?(x.drawUpdate(null,!0,v),s.lastMovePoint=v,s.lastMovePointTime=Date.now()):(s.lastMovePoint=null,s.lastMovePointTime=0):!s.down&&M.markType==="lines"&&A?v?(A.drawUpdate(null,!0,v),s.lastMovePoint=v,s.lastMovePointTime=Date.now()):(s.lastMovePoint=null,s.lastMovePointTime=0):!s.down&&M.markType==="plans"&&E&&(v?(E.drawUpdate(null,!0,v),s.lastMovePoint=v,s.lastMovePointTime=Date.now()):(s.lastMovePoint=null,s.lastMovePointTime=0))}},T=async h=>{if(h.preventDefault(),a)return;const M=e(_),C=M.useCustomControl===!0;if(C){const v=e(O),k=e(U),D=v.position.clone();console.log("[MouseUp] FPS模式下松开鼠标，按钮:",s.down,"是否移动:",s.move),k.enabled=!1,k.updateControlMode(M),k.minDistance=k.maxDistance=k.getDistance(),v.position.copy(D),console.log("[MouseUp] FPS模式下相机位置:",v.position.toArray()),h.stopPropagation()}else if(C&&s.down===2&&s.move){const v=e(U);v.enabled=!0,v.update(),h.stopPropagation()}else if(s.down===3&&s.move){const v=e(U);v.enabled=!0,v.update(),h.stopPropagation()}if(s.isDbClick&&(A?Math.abs(h.clientX-s.lastClickX)<2&&Math.abs(h.clientY-s.lastClickY)<2&&(A.drawFinish(s.lastClickPointTime>0),A=null,s.lastMovePoint=null):E&&Math.abs(h.clientX-s.lastClickX)<2&&Math.abs(h.clientY-s.lastClickY)<2&&(E.drawFinish(s.lastClickPointTime>0),E=null,s.lastMovePoint=null)),M.markMode&&s.down===1&&!s.move&&Date.now()-s.downTime<500){if(M.markType==="point"){if(await e(dt,h.clientX,h.clientY)){const k=new zs(i,await e(dt,h.clientX,h.clientY));e(V).add(k),k.drawFinish()}}else if(M.markType==="distance")if(m){const v=await e(dt,h.clientX,h.clientY);v?(m.drawFinish(v),m=null):s.isDbClick&&e(Zt)}else{const v=await e(dt,h.clientX,h.clientY);v&&(m=new Gn(i),m.drawStart(v),e(V).add(m))}else if(M.markType==="lines")if(A)if(s.lastMovePoint&&e(cn,h.clientX,h.clientY,s.lastMovePoint)<.03)A.drawUpdate(null,!0,s.lastMovePoint,!0),s.lastClickPointTime=Date.now();else{const v=await e(dt,h.clientX,h.clientY);v?(A.drawUpdate(null,!0,v,!0),s.lastClickPointTime=Date.now()):s.lastClickPointTime=0}else{const v=await e(dt,h.clientX,h.clientY);v&&(A=new Xn(i),A.drawStart(v),e(V).add(A))}else if(M.markType==="plans")if(E)if(s.lastMovePoint&&e(cn,h.clientX,h.clientY,s.lastMovePoint)<.03)E.drawUpdate(null,!0,s.lastMovePoint,!0),s.lastClickPointTime=Date.now();else{const v=await e(dt,h.clientX,h.clientY);v?(E.drawUpdate(null,!0,v,!0),s.lastClickPointTime=Date.now()):s.lastClickPointTime=0}else{const v=await e(dt,h.clientX,h.clientY);v&&(E=new Nn(i),E.drawStart(v),e(V).add(E))}else if(M.markType==="circle")if(x){const v=await e(dt,h.clientX,h.clientY);v?(x.drawFinish(v),x=null):s.isDbClick&&e(Zt)}else{const v=await e(dt,h.clientX,h.clientY);v&&(x=new Qs(i),x.drawStart(v),e(V).add(x))}s.lastClickX=h.clientX,s.lastClickY=h.clientY}s.down===2&&!s.move&&(M.useCustomControl===!0?console.log("[MouseUp] FPS模式下忽略右键点击"):(console.log("[MouseUp] 非FPS模式下右键点击，调整相机目标点"),e(Nt,h.clientX,h.clientY))),s.down=0,s.move=!1,r=Date.now()};function I(h){if(h.preventDefault(),a)return;e(j),s.down=h.touches.length;const C=e(_).useCustomControl===!0;if(s.down===1){if(s.move=!1,s.x=h.touches[0].clientX,s.y=h.touches[0].clientY,C){Math.abs(c)<.001&&Math.abs(p)<.001&&(console.log("[TouchStart] 初始化欧拉角"),u());const v=e(U);v.enabled=!1}}else if(s.down===2&&C){const v=h.touches[0],k=h.touches[1];s.touchStartX1=v.clientX,s.touchStartY1=v.clientY,s.touchStartX2=k.clientX,s.touchStartY2=k.clientY,s.touchPrevX1=v.clientX,s.touchPrevY1=v.clientY,s.touchPrevX2=k.clientX,s.touchPrevY2=k.clientY;const D=v.clientX-k.clientX,L=v.clientY-k.clientY;s.touchStartDistance=Math.sqrt(D*D+L*L),s.touchPrevDistance=s.touchStartDistance,console.log("[TouchStart] 双指触摸开始, 初始距离:",s.touchStartDistance);const B=e(U);B.enabled=!1}r=Date.now()}function F(h){if(h.preventDefault(),a)return;const M=e(_),C=M.useCustomControl===!0;if(console.log("[TouchMove] 触摸移动",h.touches.length,C),h.touches.length===1){if(s.move=!0,C){const v=h.touches[0],k=v.clientX-s.x,D=v.clientY-s.y,L=e(O),B=e(U),Z=L.position.clone(),lt=.001,ze=.001;console.log("[TouchMove] 单指模式视角控制，移动距离:",k,D),Math.abs(k)>0&&(k<0?e(De,Math.abs(k)*lt):e(_e,Math.abs(k)*lt)),Math.abs(D)>0&&(D<0?e(Ie,Math.abs(D)*ze):e(Ee,Math.abs(D)*ze)),L.position.copy(Z),B.minDistance=B.maxDistance=B.getDistance(),s.x=v.clientX,s.y=v.clientY,e(Q)}}else if(h.touches.length===2&&C){console.warn("[TouchMove] 双指移动 - 在自定义控制模式下处理");const v=h.touches[0],k=h.touches[1],D=v.clientX-k.clientX,L=v.clientY-k.clientY,B=Math.sqrt(D*D+L*L),Z=B-s.touchPrevDistance,lt=(v.clientX+k.clientX)/2,ze=(v.clientY+k.clientY)/2,lr=(s.touchPrevX1+s.touchPrevX2)/2,dr=(s.touchPrevY1+s.touchPrevY2)/2,Xs=lt-lr,Ns=ze-dr,Zn=M.cameraMoveSpeed!==void 0?M.cameraMoveSpeed:.05,jn=.1,Ks=Math.abs(Z),Qe=Math.abs(Xs),We=Math.abs(Ns);if(Ks>1){const at=Ks*jn*Zn;Z>0?(e(Me,at),console.log("[TouchMove] 双指分开 - 向前移动, 速度:",at)):(e(xe,at),console.log("[TouchMove] 双指靠拢 - 向后移动, 速度:",at))}if(Qe>1||We>1){if(Qe>We&&Qe>1){const at=Qe*jn*Zn;Xs<0?(e(Jt,at),console.log("[TouchMove] 双指右移 - 向右移动, 速度:",at)):(e(qt,at),console.log("[TouchMove] 双指左移 - 向左移动, 速度:",at))}else if(We>1){const at=We*jn*Zn;Ns<0?(e(ke,at),console.log("[TouchMove] 双指下移 - 向下移动, 速度:",at)):(e(Te,at),console.log("[TouchMove] 双指上移 - 向上移动, 速度:",at))}}s.touchPrevX1=v.clientX,s.touchPrevY1=v.clientY,s.touchPrevX2=k.clientX,s.touchPrevY2=k.clientY,s.touchPrevDistance=B,e(Q)}r=Date.now()}function W(h){if(a)return;const C=e(_).useCustomControl===!0;if(C){const v=e(U);v.enabled=!1}s.down===1&&!s.move&&(C?console.log("[TouchEnd] FPS模式下忽略点击选择"):e(Nt,s.x,s.y)),h.touches.length===0?(s.down=0,s.move=!1):s.down=h.touches.length,r=Date.now()}window.addEventListener("keydown",d),window.addEventListener("keyup",f),window.addEventListener("blur",y),window.addEventListener("wheel",g,{passive:!1}),n.addEventListener("contextmenu",w),n.addEventListener("mousedown",b),n.addEventListener("mousemove",P),n.addEventListener("mouseup",T),n.addEventListener("touchstart",I,{passive:!1}),n.addEventListener("touchmove",F,{passive:!1}),n.addEventListener("touchend",W,{passive:!1}),window.addEventListener("resize",G),G();function G(){const{width:h,height:M,top:C,left:v}=e(Tt),k=e(O);k.aspect=h/M,k.updateProjectionMatrix();const D=e(un);D.setSize(h,M),D.domElement.style.position="absolute",D.domElement.style.left=`${v}px`,D.domElement.style.top=`${C}px`;const L=e(Xt);L.setPixelRatio(Math.min(devicePixelRatio,2)),L.setSize(h,M)}t($e,()=>{a=!0,window.removeEventListener("keydown",d),window.removeEventListener("keyup",f),window.removeEventListener("blur",y),window.removeEventListener("wheel",g),n.removeEventListener("contextmenu",w),n.removeEventListener("mousedown",b),n.removeEventListener("mousemove",P),n.removeEventListener("mouseup",T),n.removeEventListener("touchstart",I),n.removeEventListener("touchmove",F),n.removeEventListener("touchend",W),window.removeEventListener("resize",G)})}function Ws(i){const t=new l.Raycaster,e=.02,n=(a,s,r)=>i.on(a,s,r),o=(a,...s)=>i.fire(a,...s);n(ge,async(a,s)=>{const{width:r,height:c,left:p,top:u}=o(Tt),d=new l.Vector2;d.x=(a-p)/r*2-1,d.y=(u-s)/c*2+1;const f=o(O);t.setFromCamera(d,f);const y=[],g=o(V),w=[],m=[];g.traverse(function(b){b instanceof ct?m.push(b):b.isMesh&&!b.ignoreIntersect&&!b.isMark&&w.push(b)});const A=t.intersectObjects(w,!0);for(let b=0;b<A.length;b++)y.push(new l.Sphere(A[b].point,t.ray.distanceToPoint(A[b].point)));const E=f.projectionMatrix.clone().multiply(f.matrixWorldInverse);for(let b=0;b<m.length;b++){const P=m[b].fire(rn);if(P)if(P.length!==void 0){const T=P,I=T.length/3;for(let F=0;F<I;F++){const W=new l.Vector3(T[3*F+0],T[3*F+1],T[3*F+2]),G=new l.Vector4(W.x,W.y,W.z,1).applyMatrix4(E),h=G.x/G.w,M=G.y/G.w;Math.sqrt((h-d.x)**2+(M-d.y)**2)<=e&&y.push(new l.Sphere(W,t.ray.distanceToPoint(W)))}}else for(let T of Object.keys(P)){const I=T.split(","),F=new l.Vector3(Number(I[0]),Number(I[1]),Number(I[2]));if(t.ray.distanceToPoint(F)<=1.4143){const W=P[T];for(let G=0,h=W.length/3;G<h;G++){const M=new l.Vector3(W[3*G+0],W[3*G+1],W[3*G+2]),C=new l.Vector4(M.x,M.y,M.z,1).applyMatrix4(E),v=C.x/C.w,k=C.y/C.w;Math.sqrt((v-d.x)**2+(k-d.y)**2)<=e&&y.push(new l.Sphere(M,t.ray.distanceToPoint(M)))}}}}y.sort((b,P)=>b.radius-P.radius);const x=[];for(let b=0;b<y.length;b++)x.push(y[b].center);return x}),n(cn,(a,s,r)=>{const{width:c,height:p,left:u,top:d}=o(Tt),f=new l.Vector2;f.x=(a-u)/c*2-1,f.y=(d-s)/p*2+1;const y=o(O);return t.setFromCamera(f,y),t.ray.distanceToPoint(r)})}function Ka(i){const t=(r,c,p)=>i.on(r,c,p),e=(r,...c)=>i.fire(r,...c);t(Ht,()=>e(O).fov),t(xt,(r=!1)=>r?e(O).position.clone():e(O).position),t(vt,(r=!1)=>r?e(U).target.clone():e(U).target),t(Pt,(r=!1)=>r?e(O).up.clone():e(O).up),t(Ke,(r,c=!1)=>{if(e(Fi,r),!c){e(U).target.copy(r),e(ye);return}const p=e(U).target.clone();let u=0;e(Wt,()=>{u+=.03,e(U).target.copy(p.clone().lerp(r,u)),e(ye)},()=>u<1)}),t(ni,()=>{let r=e(xt).toArray(),c=e(Pt).toArray(),p=e(vt).toArray();return{position:r,lookUp:c,lookAt:p}}),t(ii,()=>e(U).update()),t(si,()=>e(U).updateRotateAxis());const n=.01;let o=new l.Vector3,a=new l.Vector3,s=0;t(Ci,()=>{const r=e(U).object,c=r.fov,p=r.position.clone(),u=r.getWorldDirection(new l.Vector3);return Math.abs(s-c)<n&&Math.abs(p.x-o.x)<n&&Math.abs(p.y-o.y)<n&&Math.abs(p.z-o.z)<n&&Math.abs(u.x-a.x)<n&&Math.abs(u.y-a.y)<n&&Math.abs(u.z-a.z)<n?!1:(s=c,o=p,a=u,!0)})}class $a extends l.Mesh{constructor(){super(...arguments),this.ignoreIntersect=!0}}function Za(i){let t=!1;const e=(r,c,p)=>i.on(r,c,p),n=(r,...c)=>i.fire(r,...c),o=[],a=new l.Vector3,s=new l.Matrix4;e(Li,()=>{const r=new l.SphereGeometry(.5,32,32),c=ja();c.depthTest=!1,c.depthWrite=!1,c.transparent=!0;const p=new $a;return p.copy(new l.Mesh(r,c)),e(so,()=>c),e(Ri,u=>{t||(c.uniforms[Vn].value=u,u<=.01?p.visible=!1:p.visible=!0,c.uniformsNeedUpdate=!0,n(Q))}),e(Fi,u=>{if(t)return;const d=n(O);s.copy(d.matrixWorld).invert(),a.copy(u).applyMatrix4(s),a.normalize().multiplyScalar(10),a.applyMatrix4(d.matrixWorld);const{width:f,height:y}=n(Tt);c.uniforms[ne].value.set(f,y),p.position.copy(a),c.uniforms[ps].value.copy(u),c.uniforms[ne].value.set(f,y),c.uniforms[Vn].value=1,c.uniformsNeedUpdate=!0,n(Bi),n(Q)}),e(oo,()=>{t||(t=!0,c.dispose(),r.dispose())}),p.renderOrder=99999,p}),e(Bi,()=>{for(;o.length;)o.pop().opacity=0;let r={opacity:1};o.push(r),n(Ye,()=>{!t&&r.opacity>0&&(r.opacity<.2?r.opacity=0:r.opacity>.6?r.opacity=Math.max(r.opacity-=.01,0):r.opacity=Math.max(r.opacity-=.03,0),n(Ri,r.opacity))},()=>!t&&r.opacity>0)})}function ja(){const i={[Ko]:{type:"v3",value:new l.Color},[ps]:{type:"v3",value:new l.Vector3},[ne]:{type:"v2",value:new l.Vector2},[Vn]:{value:0}};return new l.ShaderMaterial({uniforms:i,vertexShader:Go(),fragmentShader:Xo(),transparent:!0,depthTest:!1,depthWrite:!1,side:l.FrontSide})}function Hs(i){const t=(s,r,c)=>i.on(s,r,c),e=(s,...r)=>i.fire(s,...r),n=new Map,o=document.createElement("div");o.classList.add("mark-warp"),document.body.appendChild(o);const a=new Ga;a.setSize(innerWidth,innerHeight),a.domElement.style.position="absolute",a.domElement.style.top="0px",a.domElement.style.pointerEvents="none",o.appendChild(a.domElement),t(co,()=>o),t(un,()=>a),t(pn,()=>document.body.removeChild(o)),t(Et,()=>a.render(e(V),e(O)),!0),t(St,s=>{const r=s?.getMarkData?.()?.name||s?.name;r&&n.set(r,new WeakRef(s))}),t(Bt,s=>{const r=s?.getMarkData?.()?.name||s?.name;n.delete(r)}),t($t,s=>n.get(s)?.deref()),t(lo,(s,r)=>{const c=e($t,s);!c||!r||c.drawUpdate?.(r)}),t(uo,s=>{const r=e($t,s);return r?r.getMarkData?.():{}}),t(Ut,s=>{s!==void 0&&(e(_).markVisible=s),e(V).traverse(r=>r.isMark&&(r.visible=e(_).markVisible)),e(Q)}),t(fn,async()=>{const s=[];e(V).traverse(c=>{if(c.isMark){const p=c.getMarkData?.();p&&s.push(p)}});const r=e(wt).meta||{};return s.length?r.marks=s:delete r.marks,r.cameraInfo=e(ni),await e(Mt,r)}),t(Se,async()=>{const s=[];e(V).traverse(c=>{if(c.isMark){const p=c.getMarkData?.();p&&s.push(p)}});const r=e(wt).meta||{};return s.length?r.marks=s:delete r.marks,await e(Mt,r)}),t(zi,async()=>{const s=e(wt).meta||{};delete s.marks;const r=await e(Mt,s),c=[];return n.forEach(p=>c.push(p)),c.forEach(p=>p.deref()?.dispose?.()),e(Q),r}),t(Qi,async()=>{e(V).traverse(r=>{r.isMark&&r.getMarkData?.()});const s=e(wt).meta||{};return s.watermark=e(Ui)||"",await e(Mt,s)}),t(hn,s=>{s.meterScale&&(e(_).meterScale=s.meterScale,e(st,{scale:`1 : ${e(_).meterScale} m`})),e(U).updateByOptions({...s,...s.cameraInfo||{}}),(s.marks||[]).forEach(c=>{if(c.type==="MarkSinglePoint"){const p=new zs(i,c);p.visible=!1,e(V).add(p)}else if(c.type==="MarkDistanceLine"){const p=new Gn(i);p.draw(c),p.visible=!1,e(V).add(p)}else if(c.type==="MarkMultiLines"){const p=new Xn(i);p.draw(c,!0),p.visible=!1,e(V).add(p)}else if(c.type==="MarkMultiPlans"){const p=new Nn(i);p.draw(c,!0),p.visible=!1,e(V).add(p)}else if(c.type==="MarkCirclePlan"){const p=new Qs(i);p.draw(c),p.visible=!1,e(V).add(p)}}),e(An,s.flyPositions||[]),e(wn,s.flyTargets||[])}),t(bt,()=>{const s=e(_);s.markMode=!1,e(ln),e(Q)}),t(Oi,(s,r=!0)=>{const c=s?.meterScale;if(c){if(typeof c!="number"||c<=0){console.warn("meterScale is not a number or <= 0",s);return}r&&(e(_).meterScale=s.meterScale),e(st,{scale:`1 : ${c} m`});for(const p of n.values()){const u=p.deref();u&&(u instanceof Gn||u instanceof Xn||u instanceof Nn)&&u.updateByMeterScale(c)}}}),t(Ge,s=>{const r=new l.Vector3().fromArray(s.slice(0,3)),c=new l.Vector3().fromArray(s.slice(-6,-3)),p=new l.Vector3().fromArray(s.slice(-3)),u=r.distanceTo(p)<1e-4,d=c.distanceTo(p)<1e-4,f=new l.Vector3,y=u||d?s.length/3-1:s.length/3;for(let g=0;g<y;g++)f.add(new l.Vector3(s[g*3],s[g*3+1],s[g*3+2]));return f.divideScalar(y),f}),t(Xe,s=>{const r=[];for(let d=0,f=s.length/3;d<f;d++)r.push(new l.Vector3(s[d*3],s[d*3+1],s[d*3+2]));const c=r[0].distanceTo(r[r.length-1])<1e-4,p=r[r.length-2].distanceTo(r[r.length-1])<1e-4;if((c||p)&&r.pop(),r.length<3)return 0;let u=0;for(let d=0,f=r.length-2;d<f;d++)u+=e(Ne,r[0],r[d+1],r[d+2],e(_).meterScale);return u}),t(ti,(s,r)=>{let c=0;for(let p=0,u=s.length-2;p<u;p++)c+=e(Ne,s[0],s[p+1],s[p+2],r);return c}),t(Ne,(s,r,c,p)=>{const u=s.distanceTo(r)*p,d=r.distanceTo(c)*p,f=c.distanceTo(s)*p,y=(u+d+f)/2;return Math.sqrt(y*(y-u)*(y-d)*(y-f))})}function qa(i){const t=(f,...y)=>i.fire(f,...y),e=(f,y,g)=>i.on(f,y,g),n=[],o=[];let a=!1,s=!1;e(Ce,()=>a=!1),e(Gi,()=>a=!0),e(Hi,()=>n),e(po,()=>{const f=[];for(let y=0,g=n.length;y<g;y++)f.push(...n[y].toArray());return f}),e(fo,()=>{const f=[];for(let y=0,g=o.length;y<g;y++)f.push(...o[y].toArray());return f}),e(An,f=>{for(let y=0,g=f.length/3|0;y<g;y++)n[y]=new l.Vector3(f[y*3+0],f[y*3+1],f[y*3+2])}),e(wn,f=>{for(let y=0,g=f.length/3|0;y<g;y++)o[y]=new l.Vector3(f[y*3+0],f[y*3+1],f[y*3+2])}),e(mn,()=>{const f=t(U);n.push(f.object.position.clone()),o.push(f.target.clone())}),e(yn,()=>{n.length=0,o.length=0}),e(Yi,async()=>{const f=t(wt).meta||{};if(n.length){const y=[],g=[];for(let w=0,m=n.length;w<m;w++)y.push(...n[w].toArray()),g.push(...o[w].toArray());f.flyPositions=y,f.flyTargets=g}else delete f.flyPositions,delete f.flyTargets;return await t(Mt,f)}),e(vn,()=>{s||(s=!0)&&t(be)});let r=0;const c=120*1e3;let p=0,u,d;e(be,f=>{if(r=0,p=Date.now(),u=null,d=null,!n.length||!f&&!t(U).autoRotate)return;const y=t(U),g=[y.object.position.clone()],w=[y.target.clone()],m=t(Hi)||[];for(let A=0,E=Math.min(m.length,100);A<E;A++)m[A]&&g.push(m[A]),o[A]&&w.push(o[A]);u=new l.CatmullRomCurve3(g),u.closed=!0,d=new l.CatmullRomCurve3(w),d.closed=!0,t(Gi),t(j,!1)}),e(Kt,()=>{if(Date.now()-p>c&&t(Ce),!a||!u||!d)return;const f=t(U);r=(Date.now()-p)/c;const y=u.getPoint(r),g=d.getPoint(r);f.object.position.set(y.x,y.y,y.z),f.target.set(g.x,g.y,g.z)},!0)}class Ja{constructor(t={}){this.disposed=!1,this.updateTime=0,this.needUpdate=!0,console.info("Reall3dViewer",Pe),this.init(ks(t)),!t.disableDropLocalFile&&this.enableDropLocalFile()}init(t){const e=this;t.position=t.position?[...t.position]:[0,-5,15],t.lookAt=t.lookAt?[...t.lookAt]:[0,0,0],t.lookUp=t.lookUp?[...t.lookUp]:[0,-1,0];const n=Aa(t),o=t.scene=t.scene||new l.Scene;o.background=new l.Color(t.background),wa(t);const a=t.controls=new Ua(t);a.updateByOptions(t);const s=new xn;t.viewerEvents=s,e.events=s;const r=(d,f,y)=>s.on(d,f,y),c=(d,...f)=>s.fire(d,...f);r(_,()=>t),r(_t,()=>n.domElement),r(Xt,()=>n),r(V,()=>o),r(U,()=>a),r(O,()=>a.object),r(et,()=>t.bigSceneMode),r(ro,d=>t.pointcloudMode=d);const p=[];r(Q,()=>{for(e.needUpdate=!0;p.length;)p.pop().stop=!0;let d={count:0,stop:!1};p.push(d),c(Ye,()=>{!e.disposed&&(e.needUpdate=!0),d.count++>=600&&(d.stop=!0)},()=>!e.disposed&&(c(dn)||!d.stop),10)}),_n(s),ma(s),On(s),Ka(s),Hs(s),Na(s),Ws(s),Za(s),qa(s),e.splatMesh=new ct(va(t)),r(wt,()=>e.splatMesh),o.add(e.splatMesh),Ca(s),o.add(new l.AmbientLight("#ffffff",2)),o.add(c(Li)),n.setAnimationLoop(e.update.bind(e)),r(Vi,()=>{c(_).useCustomControl===!0||a.update(),!e.needUpdate&&c(Ci)&&c(Q)}),r(Rt,()=>c(he),!0),r(Rt,()=>c(Vi),!0),r(Et,()=>{try{!(e.needUpdate=!1)&&n.render(o,c(O))}catch(d){console.warn(d.message)}},!0),r(Kt,()=>{},!0),r(pe,()=>e.dispose()),r(Xi,()=>console.info(JSON.stringify(c(wt).meta||{},null,2)));let u="";r(Ae,(d="")=>{u=d,e.splatMesh.fire(gn,u,!0)}),r(Ui,()=>u),c(st,{scale:`1 : ${c(_).meterScale} m`}),e.initGsApi()}enableDropLocalFile(){const t=this;document.addEventListener("dragover",function(e){e.preventDefault(),e.stopPropagation()}),document.addEventListener("drop",async function(e){e.preventDefault(),e.stopPropagation();let n=e.dataTransfer.files[0];if(!n)return;let o;if(n.name.endsWith(".spx"))o="spx";else if(n.name.endsWith(".splat"))o="splat";else if(n.name.endsWith(".ply"))o="ply";else if(n.name.endsWith(".spz"))o="spz";else return console.error("unsupported format:",n.name);const a=URL.createObjectURL(n),s=t.events.fire(_);s.bigSceneMode=!1,s.pointcloudMode=!0,s.autoRotate=!0,s.debugMode=!0,t.reset(s),setTimeout(async()=>{await t.addModel({url:a,format:o}),URL.revokeObjectURL(a)})})}update(){const t=this;if(t.disposed)return;const e=(n,...o)=>t.events.fire(n,...o);Date.now()-t.updateTime>30&&(e(Rt),t.needUpdate&&e(Et),e(Kt))}fire(t,e,n){const o=this;t===1&&o.splatMesh.fire(qe,e),t===2&&o.events.fire(mn),t===3&&o.events.fire(be,!0),t===4&&o.events.fire(yn),t===5&&o.events.fire(Yi),t===6&&o.events.fire(Se),t===7&&o.events.fire(zi),t===8&&(async()=>{let a=await o.splatMesh.fire(Sn);e&&(a=o.splatMesh.fire(Zi)+e),o.splatMesh.fire(Je,a)})()}options(t){const e=this;if(e.disposed)return{};const n=(r,...c)=>e.events.fire(r,...c);let o;return n(V).traverse(r=>!o&&r instanceof ct&&(o=r.options())),t&&(t.autoRotate!==void 0&&(n(U).autoRotate=t.autoRotate,n(_).autoRotate=t.autoRotate),t.pointcloudMode!==void 0&&n(we,t.pointcloudMode),t.lightFactor!==void 0&&n(Gt,t.lightFactor),t.maxRenderCountOfMobile&&(n(_).maxRenderCountOfMobile=t.maxRenderCountOfMobile),t.maxRenderCountOfPc&&(n(_).maxRenderCountOfPc=t.maxRenderCountOfPc),t.debugMode!==void 0&&(n(_).debugMode=t.debugMode),t.cameraMoveSpeed!==void 0&&(n(_).cameraMoveSpeed=t.cameraMoveSpeed),t.useCustomControl!==void 0&&(n(_).useCustomControl=t.useCustomControl),t.useCustomControl!==void 0&&n(U).updateControlMode(n(_)),t.markType!==void 0&&(n(_).markType=t.markType),t.markVisible!==void 0&&n(Ut,t.markVisible),t.meterScale!==void 0&&(n(_).meterScale=t.meterScale),t.markMode!==void 0&&(n(_).markMode=t.markMode,!t.markMode&&n(ln),n(U).autoRotate=n(_).autoRotate=!1),t.disableTransitionEffect!==void 0&&(n(_).disableTransitionEffect=t.disableTransitionEffect)),n(U).updateByOptions(t),n(st,{scale:`1 : ${n(_).meterScale} m`}),Object.assign({...n(_)},o)}reset(t={}){const e=this;e.dispose(),e.disposed=!1,e.init(ks(t))}switchDeiplayMode(){const t=this;t.disposed||t.events.fire(ve)}async addScene(t){const e=this;if(e.disposed)return;const n=(s,...r)=>e.events.fire(s,...r);let o={};try{const s=await fetch(t,{mode:"cors",credentials:"omit",cache:"reload"});if(s.status===200)o=await s.json();else return console.error("scene file fetch failed, status:",s.status)}catch(s){return console.error("scene file fetch failed",s.message)}if(!o.url)return console.error("missing model file url");if(!o.url.endsWith(".bin")&&!o.url.endsWith(".spx"))return console.error("The format is unsupported in the large scene mode",o.url);const a={...o,...o.cameraInfo||{}};o.autoCut=Math.min(Math.max(o.autoCut||0,0),50),a.bigSceneMode=o.autoCut>1,e.reset({...a}),!a.bigSceneMode&&delete o.autoCut,e.splatMesh.meta=o,ut&&(o.cameraInfo?.position||o.cameraInfo?.lookAt)&&e.events.fire(U)._dollyOut(.75),a.bigSceneMode?(n(An,o.flyPositions||[]),n(wn,o.flyTargets||[])):n(hn,o),await e.splatMesh.addModel({url:o.url},o),await n(Ae,o.watermark),n(U).updateRotateAxis()}async addModel(t){const e=this;if(e.disposed)return;const n=(c,...p)=>e.events.fire(c,...p);let o="",a={url:""};if(Object.prototype.toString.call(t)==="[object String]"?t.endsWith(".meta.json")?o=t:a.url=t:a=t,!a.url&&!o)return console.error("model url is empty");const s=n(_);s.bigSceneMode=!1;let r={};if(!a.url.startsWith("blob:"))try{o=o||a.url.substring(0,a.url.lastIndexOf("."))+".meta.json";const c=await fetch(o,{mode:"cors",credentials:"omit",cache:"reload"});c.status===200?r=await c.json():console.warn("meta file fetch failed, status:",c.status)}catch(c){console.warn("meta file fetch failed",c.message,a.url)}if(r.showWatermark=r.showWatermark!==!1,r.url=r.url||a.url,delete r.autoCut,!a.format)if(a.url=a.url||r.url,a.url.endsWith(".spx"))a.format="spx";else if(a.url.endsWith(".splat"))a.format="splat";else if(a.url.endsWith(".ply"))a.format="ply";else if(a.url.endsWith(".spz"))a.format="spz";else{console.error("unknow format!",a.url);return}n(hn,r),await e.splatMesh.addModel(a,r),await n(Ae,r.watermark)}initGsApi(){const t=(u,...d)=>this.events.fire(u,...d),e=()=>{setTimeout(()=>window.focus()),t(Ce),t(U).autoRotate=t(_).autoRotate=!t(_).autoRotate},n=(u=!0)=>{setTimeout(()=>window.focus()),u=!!u;const d=t(_);d.pointcloudMode!==u&&(d.bigSceneMode?t(we,u):t(ve))},o=(u=!0)=>{setTimeout(()=>window.focus()),t(Ut,!!u)},a=u=>{setTimeout(()=>window.focus());const d=t(_);if(d.markMode)return!1;if(u===1)d.markType="point";else if(u===2)d.markType="lines";else if(u===3)d.markType="plans";else if(u===4)d.markType="distance";else return!1;return d.markMode=!0,t(j),!0},s=async u=>(setTimeout(()=>window.focus()),u?(t($t,u)?.dispose(),t(Q),await t(Se)):!1),r=async(u,d=!0)=>(t(Oi,u,d),t($t,u?.name)?.drawUpdate?.(u,d),t(Q),d?(setTimeout(()=>window.focus()),await t(Se)):!0),c=(u=!0)=>{setTimeout(()=>window.focus()),t(qe,!!u)},p=(u,d=!0)=>{t(Ae,u),d&&t(Qi,u)};window.$api={switchAutoRotate:e,changePointCloudMode:n,showMark:o,startMark:a,deleteMark:s,updateMark:r,showWaterMark:c,setWaterMark:p}}dispose(){const t=this;if(t.disposed)return;t.disposed=!0;const e=(a,...s)=>t.events.fire(a,...s),n=e(Xt),o=n.domElement;e(tn),e(bi),e(pn),e($e),e(U).dispose(),e(Ct,e(V)),n.clear(),n.dispose(),o.parentElement.removeChild(o),t.splatMesh=null,t.events.clear(),t.events=null}enableFirstPersonMode(t=!0,e){console.log("[enableFirstPersonMode] 开始设置第一人称模式, enable:",t);const n=this,o=(d,...f)=>n.events.fire(d,...f),a=o(O),s=o(U),r=a.position.clone();console.log("[enableFirstPersonMode] 启用前相机位置:",r.toArray());const c=new l.Vector3;a.getWorldDirection(c);const p={useCustomControl:t};e!==void 0&&(p.cameraMoveSpeed=e);const u=this.options(p);if(t){a.position.copy(r);const d=r.clone().add(c);s.target.copy(d);const f=s.getDistance();s.minDistance=f,s.maxDistance=f,s.updateControlMode(o(_)),o(Q),console.log("[enableFirstPersonMode] 重置后相机位置:",a.position.toArray())}return console.log("[enableFirstPersonMode] 启用后相机位置:",a.position.toArray()),u}}function tr(i){const t=(s,r,c)=>i.on(s,r,c),e=(s,...r)=>i.fire(s,...r),n=ut?1:20,o=new Map,a=new Map;t(ce,()=>e(rt)&&o.set(Date.now(),1)),t(le,()=>e(rt)&&e(Lt,o)),t(de,()=>e(rt)&&a.set(Date.now(),1)),t(ue,()=>e(rt)&&e(Lt,a)),t(Lt,s=>{let r=[],c=Date.now(),p=0;for(const u of s.keys())c-u<=1e3?p++:r.push(u);return r.forEach(u=>s.delete(u)),Math.min(p,30)}),t(K,()=>{const s=e(V),r=e(O),c=[];return s?.traverse(function(p){p.isWarpSplatMesh&&p.splatMesh?.visible&&c.push(p)}),c.sort((p,u)=>r.position.distanceTo(p.position)-r.position.distanceTo(u.position)),window.splat=c[0]?.splatMesh,c[0]?.splatMesh}),t(ss,()=>{const s=e(V),r=e(O),c=[];s?.traverse(function(p){p.isWarpSplatMesh&&c.push(p)}),c.sort((p,u)=>r.position.distanceTo(p.position)-r.position.distanceTo(u.position));for(let p=0;p<c.length;p++)c[p].active=p<n,c[p].splatMesh&&(c[p].splatMesh.renderOrder=1e3-p)}),t(os,()=>{const s=e(V),r=[];s?.traverse(c=>r.push(c)),r.forEach(c=>{c.dispose?c.dispose():(c.geometry?.dispose?.(),c.material&&c.material instanceof Array?c.material.forEach(p=>p?.dispose?.()):c.material?.dispose?.())}),s?.clear()}),t(bn,(s=.1)=>{const r=e(K);r&&r.visible&&r.rotateOnAxis(new l.Vector3(1,0,0),l.MathUtils.degToRad(s))}),t(Cn,(s=.1)=>{const r=e(K);r&&r.visible&&r.rotateOnAxis(new l.Vector3(0,1,0),l.MathUtils.degToRad(s))}),t(Mn,(s=.1)=>{const r=e(K);r&&r.visible&&r.rotateOnAxis(new l.Vector3(0,0,1),l.MathUtils.degToRad(s))}),t(go,(s=.01)=>{const r=e(K);r&&r.visible&&(r.position.x+=s)}),t(is,(s=.01)=>{const r=e(K);r&&r.visible&&(r.position.y+=s)}),t(mo,(s=.01)=>{const r=e(K);r&&r.visible&&(r.position.z+=s)}),t(yo,s=>{const r=e(K);r&&r.visible&&s&&r.position.copy(s),console.info(r,s)}),t(Ao,(s=.01)=>{const r=e(K);r&&r.visible&&r.scale.set(r.scale.x+s,r.scale.y+s,r.scale.z+s)}),t(wo,()=>{const s=e(K);s&&(s.visible=!s.visible)}),t(vo,async s=>{if(!s&&(s=e(K)),!s)return;const r=s.meta||{};return r.transform=s.matrix.toArray(),await e(Mt,JSON.stringify(r),s.meta.url)}),t(Ji,()=>{const s=e(_).root,r=new l.WebGLRenderer({antialias:!1,logarithmicDepthBuffer:!0,stencil:!0,alpha:!0,precision:"highp"});return r.setSize(s.clientWidth,s.clientHeight),r.setPixelRatio(Math.min(devicePixelRatio,2)),t(Xt,()=>r),t(_t,()=>r.domElement),r}),t(ts,()=>{const s=new l.Scene,r=14414079;return s.background=new l.Color(r),s.fog=new l.FogExp2(r,0),t(V,()=>s),s}),t(es,()=>{const r=e(O),c=e(V),p=e(_),u=new Va(e(O),p.root);return u.screenSpacePanning=!1,u.minDistance=.1,u.maxDistance=1e5,u.maxPolarAngle=1.2,u.enableDamping=!0,u.dampingFactor=.07,u.zoomToCursor=!0,u.addEventListener("change",()=>{const d=Math.max(u.getPolarAngle(),.1),f=Math.max(u.getDistance(),.1);u.zoomSpeed=Math.max(Math.log(f),0)+.5,r.far=l.MathUtils.clamp(f/d*8,100,2e5),r.near=r.far/1e3,r.updateProjectionMatrix(),c.fog instanceof l.FogExp2&&(c.fog.density=d/(f+5)*1*.25),u.maxPolarAngle=Math.min(Math.pow(1e4/f,4),1.2)}),t(U,()=>u),t(Ht,()=>r.fov),t(xt,(d=!1)=>d?r.position.clone():r.position),t(vt,(d=!1)=>d?u.target.clone():u.target),t(Pt,(d=!1)=>d?r.up.clone():r.up),u}),t(ns,()=>{const s=new l.DirectionalLight(16777215,1);return s.position.set(0,2e3,1e3),s}),window.addEventListener("beforeunload",()=>e(pe))}function er(i){let{root:t="#map",debugMode:e}=i;t?t=typeof t=="string"?document.querySelector(t)||document.querySelector("#map"):t:t=document.querySelector("#map"),t||(t=document.createElement("div"),t.id="map",(document.querySelector("#gsviewer")||document.querySelector("body")).appendChild(t));const n={...i};return n.root=t,n}function nr(){const i=qn.TileSource.create({dataType:"image",attribution:"ArcGIS",url:"https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"}),t=new qn.TileMap({imgSource:i,lon0:90,minLevel:2,maxLevel:16});return t.scale.set(10,10,10),t.rotateX(-Math.PI/2),t.autoUpdate=!1,t}class ir{constructor(){this.down=0,this.move=!1,this.downTime=0,this.isDbClick=!1,this.x=0,this.y=0,this.lastClickX=0,this.lastClickY=0,this.lastClickPointTime=0,this.lastMovePoint=null,this.lastMovePointTime=0}}function sr(i){const t=(T,I,F)=>i.on(T,I,F),e=(T,...I)=>i.fire(T,...I),n=e(_t);let o=new Set,a,s=new ir;t(he,()=>{if(!o.size)return;if(!e(_).enableKeyboard)return o.clear();if(o.has("KeyH")){const I=e(K);I&&(I.visible=!I.visible),o.clear()}else if(o.has("Equal")){const I=e(K);if(!I||!I.visible)return o.clear();if(o.has("KeyX")){const F=l.MathUtils.degToRad(.1);I.rotateOnAxis(new l.Vector3(1,0,0),F)}else I.scale.set(I.scale.x+.01,I.scale.y+.01,I.scale.z+.01)}else if(o.has("Minus")){const I=e(K);if(!I||!I.visible)return o.clear();if(o.has("KeyX")){const F=l.MathUtils.degToRad(-.1);I.rotateOnAxis(new l.Vector3(1,0,0),F)}else I.scale.set(I.scale.x-.01,I.scale.y-.01,I.scale.z-.01)}else if(o.has("ArrowUp")){const I=e(K);I&&I.visible&&(I.position.z+=.1)}else if(o.has("ArrowDown")){const I=e(K);I&&I.visible&&(I.position.z-=.1)}else if(o.has("ArrowRight")){const I=e(K);I&&I.visible&&(I.position.x+=.1)}else if(o.has("ArrowLeft")){const I=e(K);I&&I.visible&&(I.position.x-=.1)}else if(o.has("KeyU")){const I=e(K);if(I){const F=I.meta||{};F.transform=I.matrix.toArray()}}else o.has("KeyQ")?e(bn,.1):o.has("KeyW")?e(Cn,.1):o.has("KeyE")?e(Mn,.1):o.has("KeyA")?e(bn,-.1):o.has("KeyS")?e(Cn,-.1):o.has("KeyD")?e(Mn,-.1):o.has("KeyY")?e(is,o.has("ShiftLeft")||o.has("ShiftRight")?-.1:.1):o.has("KeyC")&&console.info("position=",e(xt).toArray(),"lookat=",e(vt).toArray())});const r=T=>{console.info(T.code),T.target.type!=="text"&&(a||T.code==="F5"||(T.preventDefault(),o.add(T.code)))},c=T=>{T.target.type!=="text"&&(a||o.clear())},p=()=>{o.clear()},u=T=>{parent&&setTimeout(()=>window.focus()),T.preventDefault()},d=async T=>{T.preventDefault()};let f,y,g,w;t(Zt,()=>{f?.dispose(),y?.dispose(),g?.dispose(),w?.dispose(),f=null,y=null,g=null,w=null,s.lastMovePoint=null,e(bt)});const m=async T=>{parent&&setTimeout(()=>window.focus()),T.preventDefault(),!a&&(s.down=T.button===2?2:1,s.move=!1,s.isDbClick=Date.now()-s.downTime<300,s.downTime=Date.now())},A=async T=>{T.preventDefault(),!a&&s.down&&(s.move=!0)},E=async T=>{T.preventDefault(),!a&&(s.down=0,s.move=!1)};function x(T){T.preventDefault(),!a&&(s.down=T.touches.length,s.down===1&&(s.move=!1,s.x=T.touches[0].clientX,s.y=T.touches[0].clientY))}function b(T){T.touches.length===1&&(s.move=!0)}function P(T){s.down===1&&!s.move&&e(Nt,s.x,s.y)}window.addEventListener("keydown",r),window.addEventListener("keyup",c),window.addEventListener("blur",p),window.addEventListener("wheel",u,{passive:!1}),n.addEventListener("contextmenu",d),n.addEventListener("mousedown",m),n.addEventListener("mousemove",A),n.addEventListener("mouseup",E),n.addEventListener("touchstart",x,{passive:!1}),n.addEventListener("touchmove",b,{passive:!1}),n.addEventListener("touchend",P,{passive:!1}),t($e,()=>{a=!0,window.removeEventListener("keydown",r),window.removeEventListener("keyup",c),window.removeEventListener("blur",p),window.removeEventListener("wheel",u),n.removeEventListener("contextmenu",d),n.removeEventListener("mousedown",m),n.removeEventListener("mousemove",A),n.removeEventListener("mouseup",E),n.removeEventListener("touchstart",x),n.removeEventListener("touchmove",b),n.removeEventListener("touchend",P)}),t(Nt,async(T,I)=>await e(ge,T,I))}var Qt=Object.freeze({Linear:Object.freeze({None:function(i){return i},In:function(i){return i},Out:function(i){return i},InOut:function(i){return i}}),Quadratic:Object.freeze({In:function(i){return i*i},Out:function(i){return i*(2-i)},InOut:function(i){return(i*=2)<1?.5*i*i:-.5*(--i*(i-2)-1)}}),Cubic:Object.freeze({In:function(i){return i*i*i},Out:function(i){return--i*i*i+1},InOut:function(i){return(i*=2)<1?.5*i*i*i:.5*((i-=2)*i*i+2)}}),Quartic:Object.freeze({In:function(i){return i*i*i*i},Out:function(i){return 1- --i*i*i*i},InOut:function(i){return(i*=2)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2)}}),Quintic:Object.freeze({In:function(i){return i*i*i*i*i},Out:function(i){return--i*i*i*i*i+1},InOut:function(i){return(i*=2)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2)}}),Sinusoidal:Object.freeze({In:function(i){return 1-Math.sin((1-i)*Math.PI/2)},Out:function(i){return Math.sin(i*Math.PI/2)},InOut:function(i){return .5*(1-Math.sin(Math.PI*(.5-i)))}}),Exponential:Object.freeze({In:function(i){return i===0?0:Math.pow(1024,i-1)},Out:function(i){return i===1?1:1-Math.pow(2,-10*i)},InOut:function(i){return i===0?0:i===1?1:(i*=2)<1?.5*Math.pow(1024,i-1):.5*(-Math.pow(2,-10*(i-1))+2)}}),Circular:Object.freeze({In:function(i){return 1-Math.sqrt(1-i*i)},Out:function(i){return Math.sqrt(1- --i*i)},InOut:function(i){return(i*=2)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1)}}),Elastic:Object.freeze({In:function(i){return i===0?0:i===1?1:-Math.pow(2,10*(i-1))*Math.sin((i-1.1)*5*Math.PI)},Out:function(i){return i===0?0:i===1?1:Math.pow(2,-10*i)*Math.sin((i-.1)*5*Math.PI)+1},InOut:function(i){return i===0?0:i===1?1:(i*=2,i<1?-.5*Math.pow(2,10*(i-1))*Math.sin((i-1.1)*5*Math.PI):.5*Math.pow(2,-10*(i-1))*Math.sin((i-1.1)*5*Math.PI)+1)}}),Back:Object.freeze({In:function(i){var t=1.70158;return i===1?1:i*i*((t+1)*i-t)},Out:function(i){var t=1.70158;return i===0?0:--i*i*((t+1)*i+t)+1},InOut:function(i){var t=2.5949095;return(i*=2)<1?.5*(i*i*((t+1)*i-t)):.5*((i-=2)*i*((t+1)*i+t)+2)}}),Bounce:Object.freeze({In:function(i){return 1-Qt.Bounce.Out(1-i)},Out:function(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},InOut:function(i){return i<.5?Qt.Bounce.In(i*2)*.5:Qt.Bounce.Out(i*2-1)*.5+.5}}),generatePow:function(i){return i===void 0&&(i=4),i=i<Number.EPSILON?Number.EPSILON:i,i=i>1e4?1e4:i,{In:function(t){return Math.pow(t,i)},Out:function(t){return 1-Math.pow(1-t,i)},InOut:function(t){return t<.5?Math.pow(t*2,i)/2:(1-Math.pow(2-t*2,i))/2+.5}}}}),se=function(){return performance.now()},or=function(){function i(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._tweens={},this._tweensAddedDuringUpdate={},this.add.apply(this,t)}return i.prototype.getAll=function(){var t=this;return Object.keys(this._tweens).map(function(e){return t._tweens[e]})},i.prototype.removeAll=function(){this._tweens={}},i.prototype.add=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var o=0,a=e;o<a.length;o++){var s=a[o];(t=s._group)===null||t===void 0||t.remove(s),s._group=this,this._tweens[s.getId()]=s,this._tweensAddedDuringUpdate[s.getId()]=s}},i.prototype.remove=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,o=t;n<o.length;n++){var a=o[n];a._group=void 0,delete this._tweens[a.getId()],delete this._tweensAddedDuringUpdate[a.getId()]}},i.prototype.allStopped=function(){return this.getAll().every(function(t){return!t.isPlaying()})},i.prototype.update=function(t,e){t===void 0&&(t=se()),e===void 0&&(e=!0);var n=Object.keys(this._tweens);if(n.length!==0)for(;n.length>0;){this._tweensAddedDuringUpdate={};for(var o=0;o<n.length;o++){var a=this._tweens[n[o]],s=!e;a&&a.update(t,s)===!1&&!e&&this.remove(a)}n=Object.keys(this._tweensAddedDuringUpdate)}},i}(),Kn={Linear:function(i,t){var e=i.length-1,n=e*t,o=Math.floor(n),a=Kn.Utils.Linear;return t<0?a(i[0],i[1],n):t>1?a(i[e],i[e-1],e-n):a(i[o],i[o+1>e?e:o+1],n-o)},Utils:{Linear:function(i,t,e){return(t-i)*e+i}}},Ys=function(){function i(){}return i.nextId=function(){return i._nextId++},i._nextId=0,i}(),$n=new or,ar=function(){function i(t,e){this._isPaused=!1,this._pauseStart=0,this._valuesStart={},this._valuesEnd={},this._valuesStartRepeat={},this._duration=1e3,this._isDynamic=!1,this._initialRepeat=0,this._repeat=0,this._yoyo=!1,this._isPlaying=!1,this._reversed=!1,this._delayTime=0,this._startTime=0,this._easingFunction=Qt.Linear.None,this._interpolationFunction=Kn.Linear,this._chainedTweens=[],this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._id=Ys.nextId(),this._isChainStopped=!1,this._propertiesAreSetUp=!1,this._goToEnd=!1,this._object=t,typeof e=="object"?(this._group=e,e.add(this)):e===!0&&(this._group=$n,$n.add(this))}return i.prototype.getId=function(){return this._id},i.prototype.isPlaying=function(){return this._isPlaying},i.prototype.isPaused=function(){return this._isPaused},i.prototype.getDuration=function(){return this._duration},i.prototype.to=function(t,e){if(e===void 0&&(e=1e3),this._isPlaying)throw new Error("Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.");return this._valuesEnd=t,this._propertiesAreSetUp=!1,this._duration=e<0?0:e,this},i.prototype.duration=function(t){return t===void 0&&(t=1e3),this._duration=t<0?0:t,this},i.prototype.dynamic=function(t){return t===void 0&&(t=!1),this._isDynamic=t,this},i.prototype.start=function(t,e){if(t===void 0&&(t=se()),e===void 0&&(e=!1),this._isPlaying)return this;if(this._repeat=this._initialRepeat,this._reversed){this._reversed=!1;for(var n in this._valuesStartRepeat)this._swapEndStartRepeatValues(n),this._valuesStart[n]=this._valuesStartRepeat[n]}if(this._isPlaying=!0,this._isPaused=!1,this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._isChainStopped=!1,this._startTime=t,this._startTime+=this._delayTime,!this._propertiesAreSetUp||e){if(this._propertiesAreSetUp=!0,!this._isDynamic){var o={};for(var a in this._valuesEnd)o[a]=this._valuesEnd[a];this._valuesEnd=o}this._setupProperties(this._object,this._valuesStart,this._valuesEnd,this._valuesStartRepeat,e)}return this},i.prototype.startFromCurrentValues=function(t){return this.start(t,!0)},i.prototype._setupProperties=function(t,e,n,o,a){for(var s in n){var r=t[s],c=Array.isArray(r),p=c?"array":typeof r,u=!c&&Array.isArray(n[s]);if(!(p==="undefined"||p==="function")){if(u){var d=n[s];if(d.length===0)continue;for(var f=[r],y=0,g=d.length;y<g;y+=1){var w=this._handleRelativeValue(r,d[y]);if(isNaN(w)){u=!1,console.warn("Found invalid interpolation list. Skipping.");break}f.push(w)}u&&(n[s]=f)}if((p==="object"||c)&&r&&!u){e[s]=c?[]:{};var m=r;for(var A in m)e[s][A]=m[A];o[s]=c?[]:{};var d=n[s];if(!this._isDynamic){var E={};for(var A in d)E[A]=d[A];n[s]=d=E}this._setupProperties(m,e[s],d,o[s],a)}else(typeof e[s]>"u"||a)&&(e[s]=r),c||(e[s]*=1),u?o[s]=n[s].slice().reverse():o[s]=e[s]||0}}},i.prototype.stop=function(){return this._isChainStopped||(this._isChainStopped=!0,this.stopChainedTweens()),this._isPlaying?(this._isPlaying=!1,this._isPaused=!1,this._onStopCallback&&this._onStopCallback(this._object),this):this},i.prototype.end=function(){return this._goToEnd=!0,this.update(this._startTime+this._duration),this},i.prototype.pause=function(t){return t===void 0&&(t=se()),this._isPaused||!this._isPlaying?this:(this._isPaused=!0,this._pauseStart=t,this)},i.prototype.resume=function(t){return t===void 0&&(t=se()),!this._isPaused||!this._isPlaying?this:(this._isPaused=!1,this._startTime+=t-this._pauseStart,this._pauseStart=0,this)},i.prototype.stopChainedTweens=function(){for(var t=0,e=this._chainedTweens.length;t<e;t++)this._chainedTweens[t].stop();return this},i.prototype.group=function(t){return t?(t.add(this),this):(console.warn("tween.group() without args has been removed, use group.add(tween) instead."),this)},i.prototype.remove=function(){var t;return(t=this._group)===null||t===void 0||t.remove(this),this},i.prototype.delay=function(t){return t===void 0&&(t=0),this._delayTime=t,this},i.prototype.repeat=function(t){return t===void 0&&(t=0),this._initialRepeat=t,this._repeat=t,this},i.prototype.repeatDelay=function(t){return this._repeatDelayTime=t,this},i.prototype.yoyo=function(t){return t===void 0&&(t=!1),this._yoyo=t,this},i.prototype.easing=function(t){return t===void 0&&(t=Qt.Linear.None),this._easingFunction=t,this},i.prototype.interpolation=function(t){return t===void 0&&(t=Kn.Linear),this._interpolationFunction=t,this},i.prototype.chain=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return this._chainedTweens=t,this},i.prototype.onStart=function(t){return this._onStartCallback=t,this},i.prototype.onEveryStart=function(t){return this._onEveryStartCallback=t,this},i.prototype.onUpdate=function(t){return this._onUpdateCallback=t,this},i.prototype.onRepeat=function(t){return this._onRepeatCallback=t,this},i.prototype.onComplete=function(t){return this._onCompleteCallback=t,this},i.prototype.onStop=function(t){return this._onStopCallback=t,this},i.prototype.update=function(t,e){var n=this,o;if(t===void 0&&(t=se()),e===void 0&&(e=i.autoStartOnUpdate),this._isPaused)return!0;var a;if(!this._goToEnd&&!this._isPlaying)if(e)this.start(t,!0);else return!1;if(this._goToEnd=!1,t<this._startTime)return!0;this._onStartCallbackFired===!1&&(this._onStartCallback&&this._onStartCallback(this._object),this._onStartCallbackFired=!0),this._onEveryStartCallbackFired===!1&&(this._onEveryStartCallback&&this._onEveryStartCallback(this._object),this._onEveryStartCallbackFired=!0);var s=t-this._startTime,r=this._duration+((o=this._repeatDelayTime)!==null&&o!==void 0?o:this._delayTime),c=this._duration+this._repeat*r,p=function(){if(n._duration===0||s>c)return 1;var w=Math.trunc(s/r),m=s-w*r,A=Math.min(m/n._duration,1);return A===0&&s===n._duration?1:A},u=p(),d=this._easingFunction(u);if(this._updateProperties(this._object,this._valuesStart,this._valuesEnd,d),this._onUpdateCallback&&this._onUpdateCallback(this._object,u),this._duration===0||s>=this._duration)if(this._repeat>0){var f=Math.min(Math.trunc((s-this._duration)/r)+1,this._repeat);isFinite(this._repeat)&&(this._repeat-=f);for(a in this._valuesStartRepeat)!this._yoyo&&typeof this._valuesEnd[a]=="string"&&(this._valuesStartRepeat[a]=this._valuesStartRepeat[a]+parseFloat(this._valuesEnd[a])),this._yoyo&&this._swapEndStartRepeatValues(a),this._valuesStart[a]=this._valuesStartRepeat[a];return this._yoyo&&(this._reversed=!this._reversed),this._startTime+=r*f,this._onRepeatCallback&&this._onRepeatCallback(this._object),this._onEveryStartCallbackFired=!1,!0}else{this._onCompleteCallback&&this._onCompleteCallback(this._object);for(var y=0,g=this._chainedTweens.length;y<g;y++)this._chainedTweens[y].start(this._startTime+this._duration,!1);return this._isPlaying=!1,!1}return!0},i.prototype._updateProperties=function(t,e,n,o){for(var a in n)if(e[a]!==void 0){var s=e[a]||0,r=n[a],c=Array.isArray(t[a]),p=Array.isArray(r),u=!c&&p;u?t[a]=this._interpolationFunction(r,o):typeof r=="object"&&r?this._updateProperties(t[a],s,r,o):(r=this._handleRelativeValue(s,r),typeof r=="number"&&(t[a]=s+(r-s)*o))}},i.prototype._handleRelativeValue=function(t,e){return typeof e!="string"?e:e.charAt(0)==="+"||e.charAt(0)==="-"?t+parseFloat(e):parseFloat(e)},i.prototype._swapEndStartRepeatValues=function(t){var e=this._valuesStartRepeat[t],n=this._valuesEnd[t];typeof n=="string"?this._valuesStartRepeat[t]=this._valuesStartRepeat[t]+parseFloat(n):this._valuesStartRepeat[t]=this._valuesEnd[t],this._valuesEnd[t]=e},i.autoStartOnUpdate=!1,i}();Ys.nextId;var At=$n;At.getAll.bind(At),At.removeAll.bind(At),At.add.bind(At),At.remove.bind(At),At.update.bind(At);const Gs=navigator.userAgent.includes("Mobi");class rr extends l.Mesh{constructor(t,e){super(),this.isWarpSplatMesh=!0,this.lastActiveTime=Date.now(),this.active=!1,this.disposed=!1,this.mapViewer=e,this.addScene(t),this.frustumCulled=!1}async addScene(t){const e=this,{renderer:n,scene:o,controls:a,tileMap:s}=e.mapViewer;fetch(t,{mode:"cors",credentials:"omit",cache:"reload"}).then(r=>r.ok?r.json():{}).then(r=>{const c=new l.Matrix4;if(r.transform)c.fromArray(r.transform);else if(r.WGS84){const g=s.geo2world(new l.Vector3().fromArray(r.WGS84));c.makeTranslation(g.x,g.y,g.z)}r.autoCut&&(r.autoCut=Math.min(Math.max(r.autoCut,1),50));const p=r.autoCut&&r.autoCut>1,u=!1,d=!1,f=r.showWatermark!==!1,y={renderer:n,scene:o,controls:a,pointcloudMode:u,bigSceneMode:p,matrix:c,showWatermark:f,depthTest:d};y.maxRenderCountOfMobile??(y.maxRenderCountOfMobile=y.bigSceneMode?128*10240:400*1e4),y.maxRenderCountOfPc??(y.maxRenderCountOfPc=y.bigSceneMode?320*1e4:400*1e4),y.debugMode=this.mapViewer.events.fire(_).debugMode,e.opts=y,e.meta=r,o.add(e),e.initCSS3DSprite(y),e.applyMatrix4(c)}).catch(r=>{console.error(r.message)})}async initCSS3DSprite(t){const e=this,n=document.createElement("div");n.innerHTML=`<div title="${e.meta.name}" style='flex-direction: column;align-items: center;display: flex;pointer-events: auto;margin-bottom: 20px;'>
                               <svg height="20" width="20" style="color:#eeee00;opacity:0.9;"><use href="#svgicon-point3" fill="currentColor" /></svg>
                            </div>`,n.classList.add("splatmesh-point"),n.style.position="absolute",n.style.borderRadius="4px",n.style.cursor="pointer";let o=null;n.onclick=()=>{if(o)return;const s=e.position,r=t.controls,c=r.object.position.clone(),p=Gs?6:2,u=s.clone().sub(c).normalize(),d=s.clone().sub(u.multiplyScalar(p)),f={x:c.x,y:c.y,z:c.z,tx:r.target.x,ty:r.target.y,tz:r.target.z},y={x:d.x,y:d.y,z:d.z,tx:s.x,ty:s.y,tz:s.z};o=new ar(f).to(y,3e3),o.easing(Qt.Sinusoidal.InOut).start().onUpdate(()=>{const g=f.y<.1?.1:f.y;r.object.position.set(f.x,g,f.z),r.target.set(f.tx,f.ty,f.tz)}).onComplete(()=>{o=null})},n.oncontextmenu=s=>s.preventDefault();const a=new it(n);a.element.style.pointerEvents="none",a.visible=!1,a.applyMatrix4(t.matrix),e.css3dTag=a,t.scene.add(a),e.onBeforeRender=()=>{o?.update();const s=Gs?50:30,r=e.position.distanceTo(e.mapViewer.controls.object.position);if(r>s){e.css3dTag.visible=e.opts.controls.object.position.y>2;let c=.002*r;a.scale.set(c,c,c),e.css3dTag.visible=!0,e.splatMesh&&(e.splatMesh.visible=!1)}else{if(!e.active){e.splatMesh&&(e.splatMesh.visible=!1);let c=.002*r;a.scale.set(c,c,c),e.css3dTag.visible=!0;return}if(e.lastActiveTime=Date.now(),e.css3dTag.visible=!1,e.splatMesh)e.splatMesh.visible=!0;else{const c=e.meta,p={...e.opts};c.autoCut&&(p.bigSceneMode=!0);const u=new ct(p);e.splatMesh=u,e.opts.scene.add(u),u.applyMatrix4(e.opts.matrix),u.meta=c;const d=c.watermark||c.name||"";c.showWatermark=c.showWatermark!==!1,u.fire(gn,d,!0,!1),u.addModel({url:e.meta.url},e.meta)}}},e.onAfterRender=()=>{e.splatMesh&&(!e.active||Date.now()-e.lastActiveTime>1*60*1e3)&&setTimeout(()=>{e.splatMesh?.dispose(),e.splatMesh=null},5)}}dispose(){const t=this;t.disposed||(t.disposed=!0,t.opts.scene.remove(t.css3dTag),t.splatMesh?.dispose(),t.meta=null,t.splatMesh=null,t.opts=null,t.css3dTag=null,t.mapViewer=null)}}class cr extends l.EventDispatcher{constructor(t={}){console.info("Reall3dMapViewer",Pe),super(),this.clock=new l.Clock,this.updateTime=0,this.disposed=!1;const e=this,n=new xn;e.events=n;const o=(r,c,p)=>n.on(r,c,p),a=(r,...c)=>n.fire(r,...c);e.tileMap=nr();const s=er(t);o(_,()=>s),_n(n),On(n),tr(n),Ws(n),e.camera=new l.PerspectiveCamera(60,1,.01,1e4),o(O,()=>e.camera),e.container=s.root,e.renderer=a(Ji),e.scene=a(ts),e.controls=a(es),e.ambLight=new l.AmbientLight(16777215,1),e.scene.add(e.ambLight),e.dirLight=a(ns),e.scene.add(e.dirLight),e.scene.add(e.tileMap),e.container.appendChild(e.renderer.domElement),Hs(n),sr(n),window.addEventListener("resize",e.resize.bind(e)),e.resize(),e.renderer.setAnimationLoop(e.animate.bind(e)),o(pe,()=>e.dispose()),o(Rt,()=>{a(de),e.controls.update(),a(ss),a(he)},!0),o(Kt,()=>{e.dispatchEvent({type:"update",delta:e.clock.getDelta()}),e.updateTime=Date.now(),a(rt)&&a(st,{fps:a(le),realFps:a(ue),fov:a(Ht),position:a(kt,a(xt)),lookAt:a(kt,a(vt)),lookUp:a(kt,a(Pt))})},!0),o(Et,()=>{e.tileMap.update(e.camera);try{e.renderer.render(e.scene,e.camera)}catch(r){console.warn(r.message)}},!0)}addScenes(t){const e=this;fetch(t,{mode:"cors",credentials:"omit",cache:"reload"}).then(n=>n.ok?n.json():{}).then(n=>{const o=new l.Vector3().fromArray(n.position||[17e3,3e4,-35e3]),a=new l.Vector3().fromArray(n.lookAt||[17e3,0,-35e3]);e.controls.object.position.copy(o),e.controls.target.copy(a),e.dirLight.target.position.copy(a);const s=new Set;for(let r of n.scenes)s.has(r)||(new rr(r,e),s.add(r))}).catch(n=>{console.error(n.message)})}resize(){const t=this;if(t.disposed)return;const{width:e,height:n,top:o,left:a}=t.container.getBoundingClientRect();t.renderer.setPixelRatio(Math.min(devicePixelRatio,2)),t.renderer.setSize(e,n),t.camera.aspect=e/n,t.camera.updateProjectionMatrix();const s=t.events.fire(un);s.setSize(e,n),s.domElement.style.position="absolute",s.domElement.style.left=`${a}px`,s.domElement.style.top=`${o}px`}animate(){const t=this,e=(n,...o)=>t.events.fire(n,...o);e(ce),Date.now()-t.updateTime>30&&(e(Rt),e(Et),e(Kt))}dispose(){const t=this;if(t.disposed)return;t.disposed=!0;const e=t.renderer.domElement;t.events.fire(pn),t.events.fire(os),t.renderer.clear(),t.renderer.dispose(),t.events.clear(),t.scene=null,t.renderer=null,t.camera=null,t.controls=null,t.ambLight=null,t.dirLight=null,t.container.removeChild(e),t.container.classList.add("hidden"),t.container=null,t.clock=null,t.events=null,t.tileMap=null}}pt.Reall3dMapViewer=cr,pt.Reall3dViewer=Ja,pt.SplatMesh=ct,Object.defineProperty(pt,Symbol.toStringTag,{value:"Module"})});
