import { AmbientLight } from 'three'
import { DirectionalLight } from 'three'
import { EventDispatcher } from 'three'
import { MapControls } from 'three/examples/jsm/controls/MapControls.js'
import { Matrix4 } from 'three'
import { Mesh } from 'three'
import { OrbitControls } from 'three/examples/jsm/Addons.js'
import { PerspectiveCamera } from 'three'
import { Renderer } from 'three'
import { Scene } from 'three'
import * as tt from '@gotoeasy/three-tile'
import { WebGLRenderer } from 'three'

/**
 * 相机参数信息
 */
export declare interface CameraInfo {
  /**
   * 相机视场
   */
  fov?: number
  /**
   * 相机近截面距离
   */
  near?: number
  /**
   * 相机远截面距离
   */
  far?: number
  /**
   * 相机宽高比
   */
  aspect?: number
  /**
   * 相机位置
   */
  position: number[]
  /**
   * 相机注视点
   */
  lookAt?: number[]
  /**
   * 相机上向量
   */
  lookUp?: number[]
}

declare class Events {
  private map
  constructor()
  on(key: number, fn?: Function, multiFn?: boolean): Function | Function[]
  fire(key: number, ...args: any): any
  tryFire(key: number, ...args: any): any
  off(key: number): void
  clear(): void
}

/**
 * 元数据
 */
export declare interface MetaData {
  /** 名称 */
  name?: string
  /** 版本 */
  version?: string
  /** 更新日期（YYYYMMDD） */
  updateDate?: number
  /** 是否自动旋转 */
  autoRotate?: boolean
  /** 是否调试模式 */
  debugMode?: boolean
  /** 是否点云模式 */
  pointcloudMode?: boolean
  /** 移动端最大渲染数量 */
  maxRenderCountOfMobile?: number
  /** PC端最大渲染数量 */
  maxRenderCountOfPc?: number
  /** 移动端最大下载数量 */
  mobileDownloadLimitSplatCount?: number
  /** PC端最大下载数量 */
  pcDownloadLimitSplatCount?: number
  /** 米比例尺 */
  meterScale?: number
  /** 文字水印 */
  watermark?: string
  /** 是否显示水印 */
  showWatermark?: boolean
  /** 相机参数 */
  cameraInfo?: CameraInfo
  /** 标注 */
  marks?: any[]
  /** 飞翔相机位置点 */
  flyPositions?: number[]
  /** 飞翔相机注视点 */
  flyTargets?: number[]
  /** 自动切割数量 */
  autoCut?: number
  /** 变换矩阵 */
  transform?: number[]
  /** 定位坐标(EPSG:4326 WGS 84) */
  WGS84?: number[]
  /** 模型地址 */
  url?: string
}

/**
 * 高斯模型选项
 */
export declare interface ModelOptions {
  /**
   *  模型地址
   */
  url: string
  /**
   *  模型格式（ply | splat | spx | spz），默认自动识别
   */
  format?: 'ply' | 'splat' | 'spx' | 'spz'
  /**
   *  是否重新下载
   */
  fetchReload?: boolean
}

/**
 * 地图渲染器
 */
export declare class Reall3dMapViewer extends EventDispatcher<tt.plugin.GLViewerEventMap> {
  scene: Scene
  renderer: WebGLRenderer
  camera: PerspectiveCamera
  controls: MapControls
  ambLight: AmbientLight
  dirLight: DirectionalLight
  container: HTMLElement
  tileMap: tt.TileMap
  events: Events
  private clock
  private updateTime
  private disposed
  constructor(options?: Reall3dMapViewerOptions)
  /**
   * 打开地图场景
   * @param 场景索引文件地址
   */
  addScenes(urlScenesJson: string): void
  private resize
  private animate
  /**
   * 销毁
   */
  dispose(): void
}

/**
 * 地图配置项
 */
export declare interface Reall3dMapViewerOptions {
  /**
   *  容器元素或其选择器，默认选择器为'#map'，找不到时将自动创建
   */
  root?: HTMLElement | string
  /**
   * 是否允许键盘操作，默认false
   */
  enableKeyboard?: boolean
  /**
   *  是否调试模式，生产环境默认false
   */
  debugMode?: boolean
}

/**
 * 高斯渲染器
 */
export declare class Reall3dViewer {
  private disposed
  private splatMesh
  private events
  private updateTime
  needUpdate: boolean
  constructor(opts?: Reall3dViewerOptions)
  private init
  /**
   * 允许拖拽本地文件进行渲染
   */
  private enableDropLocalFile
  /**
   * 刷新
   */
  update(): void
  fire(n: number, p1?: any, p2?: any): void
  /**
   * 设定或者获取最新配置项
   * @param opts 配置项
   * @returns 最新配置项
   */
  options(opts?: Reall3dViewerOptions): Reall3dViewerOptions
  /**
   * 重置
   */
  reset(opts?: Reall3dViewerOptions): void
  /**
   * 光圈过渡切换显示
   * @returns
   */
  switchDeiplayMode(): void
  /**
   * 添加场景
   * @param sceneUrl 场景地址
   */
  addScene(sceneUrl: string): Promise<void>
  /**
   * 添加要渲染的高斯模型（小场景模式）
   * @param urlOpts 高斯模型链接或元数据文件链接或选项
   */
  addModel(urlOpts: string | ModelOptions): Promise<void>
  /**
   * 根据需要暴露的接口
   */
  private initGsApi
  /**
   * 销毁渲染器不再使用
   */
  dispose(): void
  /**
   * 启用或禁用第一人称模式
   *
   * 第一人称模式提供类似FPS游戏的控制体验：
   * - 使用WASD键控制相机前后左右移动
   * - 使用箭头键控制相机上下移动
   * - 使用鼠标左键拖动来旋转视角
   *
   * @example
   * // 启用第一人称模式
   * viewer.enableFirstPersonMode(true);
   *
   * // 启用第一人称模式并设置较快的移动速度
   * viewer.enableFirstPersonMode(true, 0.01);
   *
   * // 禁用第一人称模式，恢复默认控制
   * viewer.enableFirstPersonMode(false);
   *
   * @param enable 是否启用第一人称模式，默认为true
   * @param cameraMoveSpeed 相机移动速度，值越大移动越快，默认为0.005
   * @returns 当前的ViewerOptions
   */
  enableFirstPersonMode(enable?: boolean, cameraMoveSpeed?: number): Reall3dViewerOptions
}

/**
 * 高斯网格配置项
 */
export declare interface Reall3dViewerOptions {
  /**
   *  指定渲染器对象传入使用，未定义时自动生成
   */
  renderer?: Renderer | undefined
  /**
   *  指定场景对象传入使用，未定义时自动生成
   */
  scene?: Scene | undefined
  /**
   *  指定相机对象传入使用，未定义时自动生成
   */
  camera?: PerspectiveCamera | undefined
  /**
   *  控制器
   */
  controls?: OrbitControls
  /**
   *  渲染器事件管理器
   */
  viewerEvents?: Events | undefined
  /**
   *  是否调试模式，生产环境默认false
   */
  debugMode?: boolean | undefined
  /**
   * 是否大场景模式，初始化后不可修改
   */
  bigSceneMode?: boolean
  /**
   * 是否点云模式渲染，默认为true
   * 支持通过viewer.options()动态更新
   */
  pointcloudMode?: boolean | undefined
  /**
   * 移动端可渲染的高斯点数量限制
   * 支持通过viewer.options()动态更新
   */
  maxRenderCountOfMobile?: number | undefined
  /**
   * PC端可渲染的高斯点数量限制
   * 支持通过viewer.options()动态更新
   */
  maxRenderCountOfPc?: number | undefined
  /**
   * 颜色亮度系数，默认1.1
   */
  lightFactor?: number | undefined
  /**
   *  容器元素或其选择器，默认选择器为'#gsviewer'，自动创建画布时若找不到容器节点，将在body下自动创建容器
   */
  root?: HTMLElement | string | undefined
  /**
   * 相机视场，默认 45
   */
  fov?: number | undefined
  /**
   * 相机近截面距离，默认 0.1
   */
  near?: number | undefined
  /**
   * 相机远截面距离，默认 1000
   */
  far?: number | undefined
  /**
   * 相机位置，默认 [0, -5, 15]
   */
  position?: number[] | undefined
  /**
   * 相机注视点，默认 [0, 0, 0]
   */
  lookAt?: number[] | undefined
  /**
   * 相机上向量，默认 [0, -1, 0]
   */
  lookUp?: number[] | undefined
  /**
   * 是否自动旋转，默认true
   * 支持通过viewer.options()动态更新
   */
  autoRotate?: boolean | undefined
  /**
   * 是否启用阻尼效果，默认true
   */
  enableDamping?: boolean | undefined
  /**
   * 是否允许操作缩放，默认true
   */
  enableZoom?: boolean | undefined
  /**
   * 是否允许操作旋转，默认true
   */
  enableRotate?: boolean | undefined
  /**
   * 是否允许操作拖动，默认true
   */
  enablePan?: boolean | undefined
  /**
   * 最小视距
   */
  minDistance?: number | undefined
  /**
   * 最大视距
   */
  maxDistance?: number | undefined
  /**
   * 最小倾斜角度
   */
  minPolarAngle?: number | undefined
  /**
   * 最大倾斜角度
   */
  maxPolarAngle?: number | undefined
  /**
   * 是否允许键盘操作，默认true
   */
  enableKeyboard?: boolean | undefined
  /**
   * 标注模式，默认false
   */
  markMode?: boolean | undefined
  /**
   * 标注类型（点、线、面、距离、面积、圆），默认undefined
   */
  markType?: 'point' | 'lines' | 'plans' | 'distance' | 'area' | 'circle' | undefined
  /**
   * 标注是否显示，默认true
   */
  markVisible?: boolean | undefined
  /**
   * 米单位比例尺（1单位长度等于多少米），默认1
   */
  meterScale?: number | undefined
  /**
   * 是否禁止直接拖拽本地文件进行查看，默认false
   */
  disableDropLocalFile?: boolean | undefined
  /**
   * 球谐系数的渲染级别，默认为模型数据的最大可渲染级别
   */
  shDegree?: number | undefined
  /**
   * 背景色（默认 '#000000'）
   */
  background?: string
  /**
   * 相机移动速度，默认0.005
   */
  cameraMoveSpeed?: number | undefined
  /**
   * 是否启用自定义控制（WASD移动等），默认false
   */
  useCustomControl?: boolean | undefined
  /**
   * 是否禁用模式切换时的过渡效果，默认false
   */
  disableTransitionEffect?: boolean | undefined
}

/**
 * Gaussian splatting mesh
 */
export declare class SplatMesh extends Mesh {
  readonly isSplatMesh: boolean
  meta: MetaData
  private disposed
  private events
  private opts
  /**
   * 构造函数
   * @param options 渲染器、场景、相机都应该传入
   */
  constructor(options: SplatMeshOptions)
  /**
   * 设定或者获取最新配置项
   * @param opts 配置项
   * @returns 最新配置项
   */
  options(opts?: SplatMeshOptions): SplatMeshOptions
  /**
   * 添加渲染指定高斯模型
   * @param opts 高斯模型选项
   * @param meta 元数据
   */
  addModel(opts: ModelOptions, meta: MetaData): Promise<void>
  fire(key: number, ...args: any): any
  /**
   * 销毁
   */
  dispose(): void
}

/**
 * 高斯网格配置项
 */
export declare interface SplatMeshOptions {
  /**
   * 名称
   */
  name?: string
  /**
   * 指定渲染器对象传入使用
   */
  renderer: Renderer
  /**
   * 指定场景对象传入使用
   */
  scene: Scene
  /**
   * 控制器
   */
  controls?: OrbitControls
  /**
   * 模型矩阵
   */
  matrix?: Matrix4 | undefined
  /**
   * 渲染器事件管理器
   */
  viewerEvents?: Events | undefined
  /**
   * 是否调试模式，生产环境默认false
   */
  debugMode?: boolean | undefined
  /**
   * 是否大场景模式，初始化后不可修改
   */
  bigSceneMode?: boolean
  /**
   * 是否点云模式渲染，默认为true
   * 支持通过viewer.options()动态更新
   */
  pointcloudMode?: boolean | undefined
  /**
   * 移动端可渲染的高斯点数量限制，默认200万
   * 支持通过viewer.options()动态更新
   */
  maxRenderCountOfMobile?: number | undefined
  /**
   * PC端可渲染的高斯点数量限制，默认500万
   * 支持通过viewer.options()动态更新
   */
  maxRenderCountOfPc?: number | undefined
  /**
   * 颜色亮度系数，默认1.1
   */
  lightFactor?: number | undefined
  /**
   * 是否显示水印
   */
  showWatermark?: boolean | undefined
  /**
   * 球谐系数的渲染级别，默认为模型数据的最大可渲染级别
   */
  shDegree?: number | undefined
  /**
   * 是否开启深度测试，默认true
   */
  depthTest?: boolean
  /**
   * 是否禁用模式切换时的过渡效果，默认false
   */
  disableTransitionEffect?: boolean | undefined
}

export {}
