(function(Ct,A){typeof exports=="object"&&typeof module<"u"?A(exports,require("three"),require("@gotoeasy/three-tile")):typeof define=="function"&&define.amd?define(["exports","three","@gotoeasy/three-tile"],A):(Ct=typeof globalThis<"u"?globalThis:Ct||self,A(Ct.reall3dviewer={},Ct.THREE,Ct.tt))})(this,(function(Ct,A,ce){"use strict";var lA=Object.defineProperty;var AA=(Ct,A,ce)=>A in Ct?lA(Ct,A,{enumerable:!0,configurable:!0,writable:!0,value:ce}):Ct[A]=ce;var On=(Ct,A,ce)=>AA(Ct,typeof A!="symbol"?A+"":A,ce);var Xn=typeof document<"u"?document.currentScript:null;function za(n){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(n){for(const e in n)if(e!=="default"){const s=Object.getOwnPropertyDescriptor(n,e);Object.defineProperty(t,e,s.get?s:{enumerable:!0,get:()=>n[e]})}}return t.default=n,Object.freeze(t)}const di=za(ce);let b=1;const le=b++,gi=b++,Xt=b++,fi=b++,Jn=b++,Kn=b++,pi=b++,Zn=b++,hi=b++,Ae=b++,q=b++,W=b++,mi=b++,jn=b++,_t=b++,ue=b++,Lt=b++,Se=b++,Ci=b++,yi=b++,Ii=b++,wi=b++,bi=b++,Ei=b++,qn=b++,Oa=b++,Xa=b++,$n=b++,gt=b++;b++;const rt=b++,te=b++,de=b++,Si=b++,Mi=b++,vi=b++,xi=b++,Ja=b++,Bi=b++,Je=b++,Ke=b++,Qi=b++,Di=b++,ki=b++,ee=b++,Ti=b++,Me=b++,Ri=b++,Li=b++,Fi=b++,ge=b++,ve=b++,Ze=b++,Pi=b++,ts=b++,Ka=b++,je=b++,es=b++,ns=b++,qe=b++,$e=b++,tn=b++,en=b++,Vi=b++,ss=b++,F=b++,ne=b++,xe=b++,z=b++,nn=b++,is=b++,Za=b++,It=b++,_i=b++,Mt=b++,Ui=b++,Ni=b++,Wi=b++,Hi=b++,sn=b++,Gi=b++,se=b++,os=b++,on=b++,Be=b++,Yi=b++,zi=b++,Oi=b++,as=b++;b++;const an=b++,rs=b++,cs=b++,ls=b++,As=b++,rn=b++,us=b++,cn=b++,kt=b++,ds=b++;b++;const Xi=b++,Ji=b++,ln=b++,gs=b++,fs=b++,ja=b++,An=b++,un=b++,ps=b++,fe=b++,pe=b++,dn=b++,gn=b++,Ki=b++,nt=b++,Z=b++,qa=b++,Zi=b++,fn=b++,pn=b++,$a=b++,hs=b++,ms=b++,Zt=b++,Qe=b++,he=b++,tr=b++,ji=b++,er=b++;b++,b++,b++,b++,b++;const jt=b++,De=b++,hn=b++,Cs=b++,qi=b++,$i=b++,ys=b++,Jt=b++,ke=b++,to=b++,Is=b++,eo=b++,nr=b++,sr=b++,Te=b++,Re=b++,Le=b++,mn=b++,Cn=b++,qt=b++,no=b++,so=b++,yn=b++,bt=b++,io=b++,ws=b++,oo=b++,Fe=b++,ao=b++,ro=b++,bs=b++,co=b++,lo=b++,Ao=b++,uo=b++,Es=b++,ir=b++,or=b++,go=b++,Ss=b++,ar=b++,fo=b++,In=b++,wn=b++,bn=b++,Ms=b++,vs=b++,xs=b++,Bs=b++,Qs=b++,Ds=b++,po=b++,ho=b++;b++,b++,b++,b++;const rr=b++,mo=b++,Co=b++;b++;const yo=b++,Io=b++,lt=b++,ks=b++,Ts=b++,Rs=b++,cr=b++,wo=b++,lr=b++,Ar=b++,ur=b++,dr=b++,gr=b++,bo=b++,Eo=b++;b++,b++;const fr=b++,Ls=b++,Fs=b++,Ps=b++,So=b++,pr=b++,hr=b++,mr=b++,Mo=b++,Cr=b++,En=b++,Sn=b++,Pe=b++,Ve=b++,Mn=b++,vn=b++,xn=b++,Bn=b++,Qn=b++,Dn=b++;class kn{constructor(){this.map=new Map}on(t,e=null,s=!1){if(!t)return console.error("Invalid event key",t),null;if(!e)return this.map.get(t);if(s){let i=this.map.get(t);i?typeof i=="function"?console.error("Invalid event type","multiFn=true",t):i.push(e):(i=[],this.map.set(t,i),i.push(e))}else{let i=this.map.get(t);i?typeof i=="function"?console.warn("Replace event",t):console.error("Invalid event type","multiFn=false",t):this.map.set(t,e)}return this.map.get(t)}fire(t,...e){const s=this.map.get(t);if(!s){this.map.size&&[...e];return}if(typeof s=="function")return s(...e);let i=[];return s.forEach(a=>i.push(a(...e))),i}tryFire(t,...e){return this.map.get(t)?this.fire(t,...e):void 0}off(t){this.map.delete(t)}clear(){this.map.clear()}}class yr{}class Ir{constructor(t,e={}){this.fileSize=0,this.downloadSize=0,this.status=0,this.splatData=null,this.watermarkData=null,this.dataSplatCount=0,this.watermarkCount=0,this.sh12Data=[],this.sh3Data=[],this.sh12Count=0,this.sh3Count=0,this.rowLength=0,this.modelSplatCount=-1,this.downloadSplatCount=0,this.renderSplatCount=0,this.header=null,this.CompressionRatio="",this.dataShDegree=0,this.minX=1/0,this.maxX=-1/0,this.minY=1/0,this.maxY=-1/0,this.minZ=1/0,this.maxZ=-1/0,this.topY=0,this.currentRadius=0,this.maxRadius=0,this.textWatermarkVersion=0,this.lastTextWatermarkVersion=0,this.fetchLimit=0,this.opts={...t};const s=this;s.meta=e,e.autoCut&&(s.map=new Map),s.metaMatrix=e.transform?new A.Matrix4().fromArray(e.transform):null,t.format||(t.url?.endsWith(".spx")?s.opts.format="spx":t.url?.endsWith(".splat")?s.opts.format="splat":t.url?.endsWith(".ply")?s.opts.format="ply":t.url?.endsWith(".spz")?s.opts.format="spz":t.url?.endsWith(".sog")?s.opts.format="sog":console.error("unknown format!")),s.abortController=new AbortController}}var U=(n=>(n[n.FetchReady=0]="FetchReady",n[n.Fetching=1]="Fetching",n[n.FetchDone=2]="FetchDone",n[n.FetchAborted=3]="FetchAborted",n[n.FetchFailed=4]="FetchFailed",n[n.Invalid=5]="Invalid",n))(U||{});const Tn="v2.1.0-dev",wt=!navigator.userAgent.includes("cloudphone")&&navigator.userAgent.includes("Mobi"),wr="QWERTYUIOPLKJHGFDSAZXCVBNM1234567890qwertyuioplkjhgfdsazxcvbnm`~!@#$%^&*()-_=+\\|]}[{'\";::,<.>//? 	",br=wt?600:300,Er=wt?2e3:300,me=128,Rn=32,O=32,Sr=20,Mr=16,vo=64*1024,vr=1024*1e4,xr=10240*1e4,Ut=.28209479177387814,Br=0,Qr=20,Dr=190,kr=10190,_e=1,Nt=2,ie=3,Tr=4,Rr=3141592653;var vt=(n=>(n[n.L1=1]="L1",n[n.L2=2]="L2",n[n.L3=3]="L3",n[n.L4=4]="L4",n[n.Default5=5]="Default5",n[n.L6=6]="L6",n[n.L7=7]="L7",n[n.L8=8]="L8",n[n.L9=9]="L9",n))(vt||{}),Ue=(n=>(n[n.Default1=1]="Default1",n[n.ZdepthFrontNearest2010=2010]="ZdepthFrontNearest2010",n[n.ZdepthFront2011=2011]="ZdepthFront2011",n[n.ZdepthFrontNearFar2012=2012]="ZdepthFrontNearFar2012",n[n.ZdepthFullNearFar2112=2112]="ZdepthFullNearFar2112",n))(Ue||{});function Lr(n){if(Object.prototype.hasOwnProperty.call(n,"__esModule"))return n;var t=n.default;if(typeof t=="function"){var e=function s(){return this instanceof s?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};e.prototype=t.prototype}else e={};return Object.defineProperty(e,"__esModule",{value:!0}),Object.keys(n).forEach(function(s){var i=Object.getOwnPropertyDescriptor(n,s);Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:function(){return n[s]}})}),e}var Ln={exports:{}};const Fr=Lr(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));/*!
 * Based on xzwasm (c) Steve Sanderson. License: MIT - https://github.com/SteveSanderson/xzwasm
 * Contains xz-embedded by Lasse Collin and Igor Pavlov. License: Public domain - https://tukaani.org/xz/embedded.html
 * and walloc (c) 2020 Igalia, S.L. License: MIT - https://github.com/wingo/walloc
 */var Pr=Ln.exports,xo;function Vr(){return xo||(xo=1,(function(n,t){(function(s,i){n.exports=i(Fr)})(Pr,e=>(()=>{var s=[,(r=>{r.exports="data:application/wasm;base64,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"}),(r=>{r.exports=e})],i={};function a(r){var l=i[r];if(l!==void 0)return l.exports;var u=i[r]={exports:{}};return s[r](u,u.exports,a),u.exports}a.d=(r,l)=>{for(var u in l)a.o(l,u)&&!a.o(r,u)&&Object.defineProperty(r,u,{enumerable:!0,get:l[u]})},a.o=(r,l)=>Object.prototype.hasOwnProperty.call(r,l),a.r=r=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})};var o={};return(()=>{a.r(o),a.d(o,{XzReadableStream:()=>m});var r=a(1);const l=globalThis.ReadableStream||a(2).ReadableStream,u=0,c=1;class d{constructor(p){this.exports=p.exports,this.memory=this.exports.memory,this.ptr=this.exports.create_context(),this._refresh(),this.bufSize=this.mem32[0],this.inStart=this.mem32[1]-this.ptr,this.inEnd=this.inStart+this.bufSize,this.outStart=this.mem32[4]-this.ptr}supplyInput(p){this._refresh(),this.mem8.subarray(this.inStart,this.inEnd).set(p,0),this.exports.supply_input(this.ptr,p.byteLength),this._refresh()}getNextOutput(){const p=this.exports.get_next_output(this.ptr);if(this._refresh(),p!==u&&p!==c)throw new Error(`get_next_output failed with error code ${p}`);return{outChunk:this.mem8.slice(this.outStart,this.outStart+this.mem32[5]),finished:p===c}}needsMoreInput(){return this.mem32[2]===this.mem32[3]}outputBufferIsFull(){return this.mem32[5]===this.bufSize}resetOutputBuffer(){this.outPos=this.mem32[5]=0}dispose(){this.exports.destroy_context(this.ptr),this.exports=null}_refresh(){this.memory.buffer!==this.mem8?.buffer&&(this.mem8=new Uint8Array(this.memory.buffer,this.ptr),this.mem32=new Uint32Array(this.memory.buffer,this.ptr))}}class f{constructor(){this.locked=!1,this.waitQueue=[]}async acquire(){if(!this.locked){this.locked=!0;return}return new Promise(p=>{this.waitQueue.push(p)})}release(){this.waitQueue.length>0?this.waitQueue.shift()():this.locked=!1}}const C=class C extends l{static async _getModuleInstance(){const p=r.replace("data:application/wasm;base64,",""),h=Uint8Array.from(atob(p),B=>B.charCodeAt(0)).buffer,g={},v=await WebAssembly.instantiate(h,g);C._moduleInstance=v.instance}constructor(p){let h,g=null;const v=p.getReader();super({async start(B){await C._contextMutex.acquire();try{C._moduleInstance||await(C._moduleInstancePromise||(C._moduleInstancePromise=C._getModuleInstance())),h=new d(C._moduleInstance)}catch(R){throw C._contextMutex.release(),R}},async pull(B){try{if(h.needsMoreInput()){if(g===null||g.byteLength===0){const{done:k,value:M}=await v.read();k||(g=M)}const x=Math.min(h.bufSize,g.byteLength);h.supplyInput(g.subarray(0,x)),g=g.subarray(x)}const R=h.getNextOutput();B.enqueue(R.outChunk),h.resetOutputBuffer(),R.finished&&(h.dispose(),C._contextMutex.release(),B.close())}catch(R){throw h&&h.dispose(),C._contextMutex.release(),R}},cancel(){try{return h&&h.dispose(),v.cancel()}finally{C._contextMutex.release()}}})}};On(C,"_moduleInstancePromise"),On(C,"_moduleInstance"),On(C,"_contextMutex",new f);let m=C})(),o})())})(Ln)),Ln.exports}var _r=Vr(),xt=Uint8Array,Ce=Uint16Array,Ur=Int32Array,Bo=new xt([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Qo=new xt([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Nr=new xt([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Do=function(n,t){for(var e=new Ce(31),s=0;s<31;++s)e[s]=t+=1<<n[s-1];for(var i=new Ur(e[30]),s=1;s<30;++s)for(var a=e[s];a<e[s+1];++a)i[a]=a-e[s]<<5|s;return{b:e,r:i}},ko=Do(Bo,2),To=ko.b,Wr=ko.r;To[28]=258,Wr[258]=28;for(var Hr=Do(Qo,0),Gr=Hr.b,Vs=new Ce(32768),at=0;at<32768;++at){var $t=(at&43690)>>1|(at&21845)<<1;$t=($t&52428)>>2|($t&13107)<<2,$t=($t&61680)>>4|($t&3855)<<4,Vs[at]=(($t&65280)>>8|($t&255)<<8)>>1}for(var Ne=(function(n,t,e){for(var s=n.length,i=0,a=new Ce(t);i<s;++i)n[i]&&++a[n[i]-1];var o=new Ce(t);for(i=1;i<t;++i)o[i]=o[i-1]+a[i-1]<<1;var r;if(e){r=new Ce(1<<t);var l=15-t;for(i=0;i<s;++i)if(n[i])for(var u=i<<4|n[i],c=t-n[i],d=o[n[i]-1]++<<c,f=d|(1<<c)-1;d<=f;++d)r[Vs[d]>>l]=u}else for(r=new Ce(s),i=0;i<s;++i)n[i]&&(r[i]=Vs[o[n[i]-1]++]>>15-n[i]);return r}),We=new xt(288),at=0;at<144;++at)We[at]=8;for(var at=144;at<256;++at)We[at]=9;for(var at=256;at<280;++at)We[at]=7;for(var at=280;at<288;++at)We[at]=8;for(var Ro=new xt(32),at=0;at<32;++at)Ro[at]=5;var Yr=Ne(We,9,1),zr=Ne(Ro,5,1),_s=function(n){for(var t=n[0],e=1;e<n.length;++e)n[e]>t&&(t=n[e]);return t},Ft=function(n,t,e){var s=t/8|0;return(n[s]|n[s+1]<<8)>>(t&7)&e},Us=function(n,t){var e=t/8|0;return(n[e]|n[e+1]<<8|n[e+2]<<16)>>(t&7)},Or=function(n){return(n+7)/8|0},Ns=function(n,t,e){return(t==null||t<0)&&(t=0),(e==null||e>n.length)&&(e=n.length),new xt(n.subarray(t,e))},Xr=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],Tt=function(n,t,e){var s=new Error(t||Xr[n]);if(s.code=n,Error.captureStackTrace&&Error.captureStackTrace(s,Tt),!e)throw s;return s},Jr=function(n,t,e,s){var i=n.length,a=s?s.length:0;if(!i||t.f&&!t.l)return e||new xt(0);var o=!e,r=o||t.i!=2,l=t.i;o&&(e=new xt(i*3));var u=function(ct){var Ee=e.length;if(ct>Ee){var re=new xt(Math.max(Ee*2,ct));re.set(e),e=re}},c=t.f||0,d=t.p||0,f=t.b||0,m=t.l,C=t.d,I=t.m,p=t.n,h=i*8;do{if(!m){c=Ft(n,d,1);var g=Ft(n,d+1,3);if(d+=3,g)if(g==1)m=Yr,C=zr,I=9,p=5;else if(g==2){var x=Ft(n,d,31)+257,k=Ft(n,d+10,15)+4,M=x+Ft(n,d+5,31)+1;d+=14;for(var P=new xt(M),G=new xt(19),y=0;y<k;++y)G[Nr[y]]=Ft(n,d+y*3,7);d+=k*3;for(var E=_s(G),Q=(1<<E)-1,w=Ne(G,E,1),y=0;y<M;){var D=w[Ft(n,d,Q)];d+=D&15;var v=D>>4;if(v<16)P[y++]=v;else{var S=0,T=0;for(v==16?(T=3+Ft(n,d,3),d+=2,S=P[y-1]):v==17?(T=3+Ft(n,d,7),d+=3):v==18&&(T=11+Ft(n,d,127),d+=7);T--;)P[y++]=S}}var L=P.subarray(0,x),_=P.subarray(x);I=_s(L),p=_s(_),m=Ne(L,I,1),C=Ne(_,p,1)}else Tt(1);else{var v=Or(d)+4,B=n[v-4]|n[v-3]<<8,R=v+B;if(R>i){l&&Tt(0);break}r&&u(f+B),e.set(n.subarray(v,R),f),t.b=f+=B,t.p=d=R*8,t.f=c;continue}if(d>h){l&&Tt(0);break}}r&&u(f+131072);for(var V=(1<<I)-1,N=(1<<p)-1,tt=d;;tt=d){var S=m[Us(n,d)&V],Y=S>>4;if(d+=S&15,d>h){l&&Tt(0);break}if(S||Tt(2),Y<256)e[f++]=Y;else if(Y==256){tt=d,m=null;break}else{var X=Y-254;if(Y>264){var y=Y-257,H=Bo[y];X=Ft(n,d,(1<<H)-1)+To[y],d+=H}var K=C[Us(n,d)&N],it=K>>4;K||Tt(3),d+=K&15;var _=Gr[it];if(it>3){var H=Qo[it];_+=Us(n,d)&(1<<H)-1,d+=H}if(d>h){l&&Tt(0);break}r&&u(f+131072);var ut=f+X;if(f<_){var mt=a-_,Qt=Math.min(_,ut);for(mt+f<0&&Tt(3);f<Qt;++f)e[f]=s[mt+f]}for(;f<ut;++f)e[f]=e[f-_]}}t.l=m,t.p=tt,t.b=f,t.f=c,m&&(c=1,t.m=I,t.d=C,t.n=p)}while(!c);return f!=e.length&&o?Ns(e,0,f):e.subarray(0,f)},Kr=new xt(0),Wt=function(n,t){return n[t]|n[t+1]<<8},Pt=function(n,t){return(n[t]|n[t+1]<<8|n[t+2]<<16|n[t+3]<<24)>>>0},Ws=function(n,t){return Pt(n,t)+Pt(n,t+4)*4294967296};function Zr(n,t){return Jr(n,{i:2},t&&t.out,t&&t.dictionary)}var Hs=typeof TextDecoder<"u"&&new TextDecoder,jr=0;try{Hs.decode(Kr,{stream:!0}),jr=1}catch{}var qr=function(n){for(var t="",e=0;;){var s=n[e++],i=(s>127)+(s>223)+(s>239);if(e+i>n.length)return{s:t,r:Ns(n,e-1)};i?i==3?(s=((s&15)<<18|(n[e++]&63)<<12|(n[e++]&63)<<6|n[e++]&63)-65536,t+=String.fromCharCode(55296|s>>10,56320|s&1023)):i&1?t+=String.fromCharCode((s&31)<<6|n[e++]&63):t+=String.fromCharCode((s&15)<<12|(n[e++]&63)<<6|n[e++]&63):t+=String.fromCharCode(s)}};function $r(n,t){if(t){for(var e="",s=0;s<n.length;s+=16384)e+=String.fromCharCode.apply(null,n.subarray(s,s+16384));return e}else{if(Hs)return Hs.decode(n);var i=qr(n),a=i.s,e=i.r;return e.length&&Tt(8),a}}var tc=function(n,t){return t+30+Wt(n,t+26)+Wt(n,t+28)},ec=function(n,t,e){var s=Wt(n,t+28),i=$r(n.subarray(t+46,t+46+s),!(Wt(n,t+8)&2048)),a=t+46+s,o=Pt(n,t+20),r=e&&o==4294967295?nc(n,a):[o,Pt(n,t+24),Pt(n,t+42)],l=r[0],u=r[1],c=r[2];return[Wt(n,t+10),l,u,i,a+Wt(n,t+30)+Wt(n,t+32),c]},nc=function(n,t){for(;Wt(n,t)!=1;t+=4+Wt(n,t+2));return[Ws(n,t+12),Ws(n,t+4),Ws(n,t+20)]};function sc(n,t){for(var e={},s=n.length-22;Pt(n,s)!=101010256;--s)(!s||n.length-s>65558)&&Tt(13);var i=Wt(n,s+8);if(!i)return{};var a=Pt(n,s+16),o=a==4294967295||i==65535;if(o){var r=Pt(n,s-12);o=Pt(n,r)==101075792,o&&(i=Pt(n,r+32),a=Pt(n,r+48))}for(var l=0;l<i;++l){var u=ec(n,a,o),c=u[0],d=u[1],f=u[2],m=u[3],C=u[4],I=u[5],p=tc(n,I);a=C,c?c==8?e[m]=Zr(n.subarray(p,p+d),{out:new xt(f)}):Tt(14,"unknown compression type "+c):e[m]=Ns(n,p,p+d)}return e}function Gs(n){let t=!1;const e=(f,m,C)=>n.on(f,m,C),s=(f,...m)=>n.fire(f,...m);e(Mt,()=>s(F).debugMode),e(ss,()=>t=!0);let i=0;(function f(){i++,!t&&requestAnimationFrame(f)})(),e(le,(f,m=null,C=0)=>{const I=()=>{t||(C>0?!(i%C)&&f(i):f(i),m&&m()&&requestAnimationFrame(I))};I()}),e(gi,(f,m=null,C=20)=>{const I=()=>{t||(f(),m&&m()&&setTimeout(I,C))};I()});let a=!1,o=0;e(os,()=>{if(a=!0,(async()=>{const f=document.querySelector("#gsviewer #progressBarWrap");if(f){f.style.display="block";const m=document.querySelector("#gsviewer #progressBar");m&&(m.style.width="0%")}})(),(async()=>document.querySelector("#gsviewer .logo")?.classList.add("loading"))(),parent?.onProgress&&parent.onProgress(.001,"0.001%"),window.onProgress&&window.onProgress(.001,"0.001%"),s(F).debugMode)(async()=>{const f=document.querySelector("#gsviewer #progressBarWrap");if(f){f.style.display="block";const m=document.querySelector("#gsviewer #progressBar");m&&(m.style.width="0%")}})(),(async()=>document.querySelector("#gsviewer .logo")?.classList.add("loading"))();else if(s(F).useCustomControl===!1)try{parent?.onProgress&&parent.onProgress(.001,"0.001%")}catch(m){m.message}}),e(Be,f=>{if(a=!1,f!==void 0&&((async()=>{const C=document.querySelector("#gsviewer #progressBarWrap");C&&(C.style.display="none")})(),(async()=>document.querySelector("#gsviewer .logo")?.classList.remove("loading"))(),parent?.onProgress&&parent.onProgress(0,"100%",9),window.onProgress&&window.onProgress(0,"100%",9),s(F).useCustomControl===!1))try{parent?.onProgress&&parent.onProgress(0,"100%",9)}catch(C){C.message}}),e(on,f=>{if(a=!0,(async()=>{const m=document.querySelector("#gsviewer #progressBar");m&&(m.style.width=`${f}%`)})(),parent?.onProgress&&parent.onProgress(f,`${f}%`),window.onProgress&&window.onProgress(f,`${f}%`),s(F).debugMode)(async()=>{const m=document.querySelector("#gsviewer #progressBar");m&&(m.style.width=`${f}%`)})();else if(s(F).useCustomControl===!1)try{parent?.onProgress&&parent.onProgress(f,`${f}%`)}catch(C){C.message}}),e(Yi,()=>a),e(zi,f=>{o=f}),e(Oi,()=>o),e(as,()=>!a&&o>0),e(ne,()=>{const m=s(Ae).parentElement.getBoundingClientRect();return{width:m.width,height:m.height,left:m.left,top:m.top}}),e(se,f=>{let m=f.x.toFixed(3).split("."),C=f.y.toFixed(3).split("."),I=f.z.toFixed(3).split(".");return(m[1]==="000"||m[1]==="00000")&&(m[1]="0"),(C[1]==="000"||C[1]==="00000")&&(C[1]="0"),(I[1]==="000"||I[1]==="00000")&&(I[1]="0"),`${m.join(".")}, ${C.join(".")}, ${I.join(".")}`}),e(Oa,f=>btoa(f)),e(Xa,f=>atob(f));const r=.001;let l=new A.Vector3,u=new A.Vector3,c=0;e(Za,()=>{const f=s(q),m=f.fov,C=f.position.clone(),I=f.getWorldDirection(new A.Vector3);return Math.abs(c-m)<r&&Math.abs(C.x-l.x)<r&&Math.abs(C.y-l.y)<r&&Math.abs(C.z-l.z)<r&&Math.abs(I.x-u.x)<r&&Math.abs(I.y-u.y)<r&&Math.abs(I.z-u.z)<r?!1:(c=m,l=C,u=I,!0)}),e(Jt,f=>{if(!f)return;const m=[];f.traverse(C=>m.push(C)),m.forEach(C=>{C.dispose?C.dispose():(C.geometry?.dispose?.(),C.material&&C.material instanceof Array?C.material.forEach(I=>I?.dispose?.()):C.material?.dispose?.())}),f.clear()}),e(rt,async({renderSplatCount:f,visibleSplatCount:m,modelSplatCount:C,fps:I,realFps:p,sortTime:h,bucketBits:g,sortType:v,qualityLevel:B,fov:R,position:x,lookUp:k,lookAt:M,worker:P,scene:G,scale:y,cuts:E,shDegree:Q}={})=>{if(!s(Mt))return;Q!==void 0&&d("shDegree",`${Q}`),f!==void 0&&d("renderSplatCount",`${f}`),m!==void 0&&d("visibleSplatCount",`${m}`),C!==void 0&&d("modelSplatCount",`${C}`),I!==void 0&&d("fps",I),p!==void 0&&d("realFps",`raw ${p}`),h!==void 0&&d("sort",`${h} ms （L ${s(F).qualityLevel||vt.Default5}, ${g} B, T ${v}）`),E!==void 0&&d("cuts",E===""?"":`（${E} cuts）`),P&&d("worker",`${P}`),G&&d("scene",G),R&&d("fov",R),x&&d("position",x),k&&d("lookUp",k),M&&d("lookAt",M),M&&d("viewer-version",Tn);let w=performance.memory||{usedJSHeapSize:0,totalJSHeapSize:0,jsHeapSizeLimit:0},D="",S=w.usedJSHeapSize/1024/1024;S>1e3?D+=(S/1024).toFixed(2)+" G":D+=S.toFixed(0)+" M",D+=" / ";let T=w.totalJSHeapSize/1024/1024;T>1e3?D+=(T/1024).toFixed(2)+" G":D+=T.toFixed(0)+" M";let L=w.jsHeapSizeLimit/1024/1024;D+=" / ",L>1e3?D+=(L/1024).toFixed(2)+" G":D+=L.toFixed(0)+" M",d("memory",D),y&&d("scale",y)});async function d(f,m){let C=document.querySelector(`#gsviewer .debug .${f}`);C&&(C.innerText=m)}}const He=A.ShaderChunk;function ic(n=0){const t=new Date;return t.setDate(t.getDate()-7),n>=t.getFullYear()*1e4+(t.getMonth()+1)*100+t.getDate()}function j(n){return n<0?0:n>255?255:n|0}function Fn(n){const t=j(Math.round(n*128)+128);return j(Math.floor((t+4)/8)*8)}function Pn(n,t){return((1500+n*248)/t).toFixed(2)+"x"}async function Lo(n){try{const t=new ReadableStream({async start(s){s.enqueue(n),s.close()}}),e=new Response(t.pipeThrough(new DecompressionStream("gzip")));return new Uint8Array(await e.arrayBuffer())}catch(t){return console.error("Decompress gzip failed:",t),null}}async function oc(n){try{const t=URL.createObjectURL(new Blob([n],{type:"application/octet-stream"})),e=await fetch(t),s=new Response(new _r.XzReadableStream(e.body));return new Uint8Array(await s.arrayBuffer())}catch(t){return console.error("Decompress xz failed:",t),null}}async function ac(n){const t=new Uint32Array(n.slice(0,12).buffer),e=t[0],s=t[2];let i=8;const a=n.slice(i+4,i+4+s),{rgba:o}=await dt(a);i+=4+s;const r=new Uint32Array(n.slice(i,i+4).buffer)[0],l=n.slice(i+4,i+4+r),{rgba:u}=await dt(l);i+=4+r;const c=new Uint32Array(n.slice(i,i+4).buffer)[0],d=n.slice(i+4,i+4+c),{rgba:f}=await dt(d);i+=4+c;const m=new Uint32Array(n.slice(i,i+4).buffer)[0],C=n.slice(i+4,i+4+m),{rgba:I}=await dt(C),p=new Uint8Array(8+e*19);let h=0;p[h]=n[h++],p[h]=n[h++],p[h]=n[h++],p[h]=n[h++],p[h++]=19,p[h++]=0,p[h++]=0,p[h++]=0;for(let g=0;g<e;g++)p[h++]=o[g*4+0];for(let g=0;g<e;g++)p[h++]=o[g*4+1];for(let g=0;g<e;g++)p[h++]=o[g*4+2];for(let g=0;g<e;g++)p[h++]=o[e*4+g*4+0];for(let g=0;g<e;g++)p[h++]=o[e*4+g*4+1];for(let g=0;g<e;g++)p[h++]=o[e*4+g*4+2];for(let g=0;g<e;g++)p[h++]=o[e*8+g*4+0];for(let g=0;g<e;g++)p[h++]=o[e*8+g*4+1];for(let g=0;g<e;g++)p[h++]=o[e*8+g*4+2];for(let g=0;g<e;g++)p[h++]=u[g*4+0];for(let g=0;g<e;g++)p[h++]=u[g*4+1];for(let g=0;g<e;g++)p[h++]=u[g*4+2];for(let g=0;g<e;g++)p[h++]=f[g*4+0];for(let g=0;g<e;g++)p[h++]=f[g*4+1];for(let g=0;g<e;g++)p[h++]=f[g*4+2];for(let g=0;g<e;g++)p[h++]=f[g*4+3];for(let g=0;g<e;g++)p[h++]=I[g*4+0];for(let g=0;g<e;g++)p[h++]=I[g*4+1];for(let g=0;g<e;g++)p[h++]=I[g*4+2];return p}async function rc(n){const t=new Uint32Array(n.slice(0,16).buffer),e=t[0],s=t[3];let i=12;const a=n.slice(i+4,i+4+s),{rgba:o}=await dt(a);i+=4+s;const r=new Uint32Array(n.slice(i,i+4).buffer)[0],l=n.slice(i+4,i+4+r),{rgba:u}=await dt(l);i+=4+r;const c=new Uint32Array(n.slice(i,i+4).buffer)[0],d=n.slice(i+4,i+4+c),{rgba:f}=await dt(d);i+=4+c;const m=new Uint32Array(n.slice(i,i+4).buffer)[0],C=n.slice(i+4,i+4+m),{rgba:I}=await dt(C),p=new Uint8Array(8+e*19);let h=0;p[h]=n[h++],p[h]=n[h++],p[h]=n[h++],p[h]=n[h++],p[h++]=35,p[h++]=39,p[h++]=0,p[h++]=0,p[h]=n[h++],p[h]=n[h++],p[h]=n[h++],p[h]=n[h++];for(let g=0;g<e;g++)p[h++]=o[g*4+0];for(let g=0;g<e;g++)p[h++]=o[g*4+1];for(let g=0;g<e;g++)p[h++]=o[g*4+2];for(let g=0;g<e;g++)p[h++]=o[e*4+g*4+0];for(let g=0;g<e;g++)p[h++]=o[e*4+g*4+1];for(let g=0;g<e;g++)p[h++]=o[e*4+g*4+2];for(let g=0;g<e;g++)p[h++]=o[e*8+g*4+0];for(let g=0;g<e;g++)p[h++]=o[e*8+g*4+1];for(let g=0;g<e;g++)p[h++]=o[e*8+g*4+2];for(let g=0;g<e;g++)p[h++]=u[g*4+0];for(let g=0;g<e;g++)p[h++]=u[g*4+1];for(let g=0;g<e;g++)p[h++]=u[g*4+2];for(let g=0;g<e;g++)p[h++]=f[g*4+0];for(let g=0;g<e;g++)p[h++]=f[g*4+1];for(let g=0;g<e;g++)p[h++]=f[g*4+2];for(let g=0;g<e;g++)p[h++]=f[g*4+3];for(let g=0;g<e;g++)p[h++]=I[g*4+0];for(let g=0;g<e;g++)p[h++]=I[g*4+1];for(let g=0;g<e;g++)p[h++]=I[g*4+2];return p}async function cc(n){const e=new Uint32Array(n.slice(0,8).buffer)[0],s=n.slice(8),{rgba:i}=await dt(s),a=new Uint8Array(8+e*9);let o=0;a[o]=n[o++],a[o]=n[o++],a[o]=n[o++],a[o]=n[o++],a[o++]=1,a[o++]=0,a[o++]=0,a[o++]=0;for(let r=0;r<e;r++)for(let l=0;l<3;l++)a[o++]=i[r*60+l*4+0],a[o++]=i[r*60+l*4+1],a[o++]=i[r*60+l*4+2];return a}async function Fo(n){const e=new Uint32Array(n.slice(0,8).buffer)[0],s=n.slice(8),{rgba:i}=await dt(s),a=new Uint8Array(8+e*24);let o=0;a[o]=n[o++],a[o]=n[o++],a[o]=n[o++],a[o]=n[o++],a[o++]=2,a[o++]=0,a[o++]=0,a[o++]=0;for(let r=0;r<e;r++)for(let l=0;l<8;l++)a[o++]=i[r*60+l*4+0],a[o++]=i[r*60+l*4+1],a[o++]=i[r*60+l*4+2];return a}async function lc(n){const e=new Uint32Array(n.slice(0,8).buffer)[0],s=n.slice(8),{rgba:i}=await dt(s),a=new Uint8Array(8+e*21);let o=0;a[o]=n[o++],a[o]=n[o++],a[o]=n[o++],a[o]=n[o++],a[o++]=3,a[o++]=0,a[o++]=0,a[o++]=0;for(let r=0;r<e;r++)for(let l=8;l<15;l++)a[o++]=i[r*60+l*4+0],a[o++]=i[r*60+l*4+1],a[o++]=i[r*60+l*4+2];return a}async function dt(n){const t=new Blob([n],{type:"image/webp"}),e=URL.createObjectURL(t),s=await createImageBitmap(await fetch(e).then(u=>u.blob())),a=new OffscreenCanvas(s.width,s.height).getContext("2d");a.drawImage(s,0,0);const o=a.getImageData(0,0,s.width,s.height),r=s.width,l=s.height;return s.close(),URL.revokeObjectURL(e),{width:r,height:l,rgba:o.data}}function Ac(n){const t=new Map;try{const e=sc(n);for(const[s,i]of Object.entries(e))if(i){const a=new Uint8Array(i);t.set(s,a)}}catch(e){console.error("解压失败:",e)}return t}function uc(n){return new TextDecoder("utf-8").decode(n)}function Po(n,t=null,e=20){const s=()=>{n(),t?.()&&setTimeout(s,e)};s()}const dc="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",Ys="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",zs="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";async function gc(n){const t=new Uint32Array(n.buffer),e=new Float32Array(n.buffer),s=new yr;s.Fixed=String.fromCharCode(n[0])+String.fromCharCode(n[1])+String.fromCharCode(n[2]),s.Version=n[3],s.SplatCount=t[1],s.MinX=e[2],s.MaxX=e[3],s.MinY=e[4],s.MaxY=e[5],s.MinZ=e[6],s.MaxZ=e[7],s.MinTopY=e[8],s.MaxTopY=e[9],s.CreateDate=t[10],s.CreaterId=t[11],s.ExclusiveId=t[12],s.ShDegree=n[52],s.Flag1=n[53],s.Flag2=n[54],s.Flag3=n[55],s.Reserve1=t[14],s.Reserve2=t[15];let i="";for(let f=64;f<124;f++)i+=String.fromCharCode(n[f]);if(s.Comment=i.trim(),s.HashCheck=!0,s.Fixed!=="spx"&&s.Version!==1)return null;const a=s.ExclusiveId?Ys:zs,o=WebAssembly.compile(Uint8Array.from(atob(a),f=>f.charCodeAt(0)).buffer),r=new WebAssembly.Memory({initial:1,maximum:1}),u=(await WebAssembly.instantiate(await o,{env:{memory:r,expf:Vn}})).exports.H;return new Uint8Array(r.buffer).set(n,0),u(0)&&(s.HashCheck=!1),s}async function Et(n,t=null){let e=new Uint32Array(n.slice(0,8).buffer);const s=e[0],i=e[1];if(i==Tr){if(t.ShDegree==_e){const a=await cc(n);return Ge(s,_e,a,t.Version)}else if(t.ShDegree==Nt){const a=await Fo(n);return Ge(s,Nt,a,t.Version)}else if(t.ShDegree==ie){const a=await Fo(n),o=await lc(n),r=await Ge(s,Nt,a,t.Version),l=await Ge(s,ie,o,t.Version);return r.success=r.success&&l.success,r.isSh23=!0,r.dataSh3=l.datas,r}}return i==Dr?n=await ac(n):i==kr&&(n=await rc(n)),Ge(s,i,n,t?.Version||1)}async function Ge(n,t,e,s){const i=_e==t,a=Nt==t,o=ie==t,r=i||a||o,l=!r,c=t<65536?zs:s>1?Ys:dc,d=n*(r?Mr:O),f=WebAssembly.compile(Uint8Array.from(atob(c),v=>v.charCodeAt(0)).buffer),m=Math.floor((d+e.byteLength)/vo)+2,C=new WebAssembly.Memory({initial:m,maximum:m}),p=(await WebAssembly.instantiate(await f,{env:{memory:C,expf:Vn}})).exports.D,h=new Uint8Array(C.buffer);return h.set(e,d),p(0,d)?{splatCount:n,blockFormat:t,success:!1}:{splatCount:n,blockFormat:t,success:!0,datas:h.slice(0,d),isSplat:l,isSh:r,isSh1:i,isSh2:a,isSh3:o}}async function Ye(n,t){const e=WebAssembly.compile(Uint8Array.from(atob(zs),u=>u.charCodeAt(0)).buffer),s=Math.floor(t*O/vo)+2,i=new WebAssembly.Memory({initial:s,maximum:s}),o=(await WebAssembly.instantiate(await e,{env:{memory:i,expf:Vn}})).exports.s,r=new Uint8Array(i.buffer);r.set(n.slice(0,t*O),0);const l=o(0,t);return l?(console.error("splat data parser failed:",l),new Uint8Array(0)):r.slice(0,t*O)}async function fc(n,t,e=!0,s=!0){const i=WebAssembly.compile(Uint8Array.from(atob(Ys),d=>d.charCodeAt(0)).buffer),a=new WebAssembly.Memory({initial:1,maximum:1}),r=(await WebAssembly.instantiate(await i,{env:{memory:a,expf:Vn}})).exports.w,l=new Uint8Array(a.buffer),u=new Float32Array(l.buffer),c=s?-1:1;return u[0]=n,e?u[1]=c*t:u[2]=c*t,r(0,e?1:0),l.slice(0,O)}function Vn(n){return Math.exp(n)}const ze=wt?20480:51200;async function pc(n){try{n.status=U.Fetching;const i=n.abortController.signal,a=n.opts.fetchReload?"reload":"default",o=await fetch(n.opts.url,{mode:"cors",credentials:"omit",cache:a,signal:i});if(o.status!=200){console.warn(`fetch error: ${o.status}`),n.status===U.Fetching&&(n.status=U.FetchFailed);return}const r=o.body.getReader(),l=parseInt(o.headers.get("content-length")||"0");n.fileSize=l,n.downloadSize=0,n.downloadSplatCount=0,n.watermarkData=new Uint8Array(0);let u=new Uint8Array(256),c=0,d=[],f;for(;;){let{done:m,value:C}=await r.read();if(m)break;if(n.downloadSize+=C.byteLength,d){if(d.push(C),n.downloadSize<200)continue;const I=new Uint8Array(n.downloadSize);for(let p=0,h=0;p<d.length;p++)I.set(d[p],h),h+=d[p].byteLength;if(f=s(I),!f){d=[I];continue}d=null,C=I.slice(f.headerLength),n.rowLength=f.rowLength,n.dataShDegree=f.shDegree,n.modelSplatCount=f.vertexCount,n.splatData=new Uint8Array(Math.min(n.modelSplatCount,n.fetchLimit)*32)}c+C.byteLength<n.rowLength?(u.set(C,c),c+=C.byteLength):(c=await t(f,n,c,u,C),c&&u.set(C.slice(C.byteLength-c),0)),n.downloadSplatCount>=n.fetchLimit&&n.abortController.abort()}}catch(i){i.name==="AbortError"?(n.opts.url,n.status===U.Fetching&&(n.status=U.FetchAborted)):(console.error(i),n.status===U.Fetching&&(n.status=U.FetchFailed))}finally{n.status===U.Fetching&&(n.status=U.FetchDone)}async function t(i,a,o,r,l){return new Promise(async u=>{let c=(o+l.byteLength)/a.rowLength|0,d=(o+l.byteLength)%a.rowLength,f;o?(f=new Uint8Array(c*a.rowLength),f.set(r.slice(0,o),0),f.set(l.slice(0,l.byteLength-d),o)):f=l.slice(0,c*a.rowLength),a.downloadSplatCount+c>a.fetchLimit&&(c=a.fetchLimit-a.downloadSplatCount,d=0);const m=async()=>{if(c>ze){const C=await e(i,f,ze);Vo(a,C),a.downloadSplatCount+=ze,c-=ze,f=f.slice(ze*a.rowLength),setTimeout(m)}else{const C=await e(i,f,c);Vo(a,C),a.downloadSplatCount+=c,u(d)}};await m()})}async function e(i,a,o){const r=new Uint8Array(o*Rn),l=new Float32Array(a.buffer),u=new Float32Array(r.buffer);for(let c=0;c<o;c++)u[c*8+0]=l[(c*n.rowLength+i.offsets.x)/4],u[c*8+1]=l[(c*n.rowLength+i.offsets.y)/4],u[c*8+2]=l[(c*n.rowLength+i.offsets.z)/4],u[c*8+3]=Math.exp(l[(c*n.rowLength+i.offsets.scale_0)/4]),u[c*8+4]=Math.exp(l[(c*n.rowLength+i.offsets.scale_1)/4]),u[c*8+5]=Math.exp(l[(c*n.rowLength+i.offsets.scale_2)/4]),r[c*32+24]=j((.5+Ut*l[(c*n.rowLength+i.offsets.f_dc_0)/4])*255),r[c*32+25]=j((.5+Ut*l[(c*n.rowLength+i.offsets.f_dc_1)/4])*255),r[c*32+26]=j((.5+Ut*l[(c*n.rowLength+i.offsets.f_dc_2)/4])*255),r[c*32+27]=j(1/(1+Math.exp(-l[(c*n.rowLength+i.offsets.opacity)/4]))*255),r[c*32+28]=j(l[(c*n.rowLength+i.offsets.rot_0)/4]*128+128),r[c*32+29]=j(l[(c*n.rowLength+i.offsets.rot_1)/4]*128+128),r[c*32+30]=j(l[(c*n.rowLength+i.offsets.rot_2)/4]*128+128),r[c*32+31]=j(l[(c*n.rowLength+i.offsets.rot_3)/4]*128+128);if(i.shDegree==3){const d=new Uint8Array(o*24+8),f=new Uint8Array(o*21+8),m=new Uint32Array(2);m[0]=o,m[1]=Nt,d.set(new Uint8Array(m.buffer),0);const C=new Uint32Array(2);C[0]=o,C[1]=ie,f.set(new Uint8Array(C.buffer),0);for(let h=0,g=0;h<o;h++)for(let v=0;v<8;v++)for(let B=0;B<3;B++)d[8+g++]=Fn(l[(h*n.rowLength+i.offsets["f_rest_"+(v+B*15)])/4]);for(let h=0,g=0;h<o;h++)for(let v=8;v<15;v++)for(let B=0;B<3;B++)f[8+g++]=Fn(l[(h*n.rowLength+i.offsets["f_rest_"+(v+B*15)])/4]);const I=await Et(d);n.sh12Data.push(I.datas);const p=await Et(f);n.sh3Data.push(p.datas)}else if(i.shDegree==2){const d=new Uint8Array(o*24+8),f=new Uint32Array(2);f[0]=o,f[1]=Nt,d.set(new Uint8Array(f.buffer),0);for(let C=0,I=0;C<o;C++)for(let p=0;p<8;p++)for(let h=0;h<3;h++)d[8+I++]=Fn(l[(C*n.rowLength+i.offsets["f_rest_"+(p+h*8)])/4]);const m=await Et(d);n.sh12Data.push(m.datas)}else if(i.shDegree==1){const d=new Uint8Array(o*9+8),f=new Uint32Array(2);f[0]=o,f[1]=_e,d.set(new Uint8Array(f.buffer),0);for(let C=0,I=0;C<o;C++)for(let p=0;p<3;p++)for(let h=0;h<3;h++)d[8+I++]=Fn(l[(C*n.rowLength+i.offsets["f_rest_"+(p+h*3)])/4]);const m=await Et(d);n.sh12Data.push(m.datas)}return await Ye(r,o)}function s(i){let a=new TextDecoder().decode(i.slice(0,2048));const o=`end_header
`,r=a.indexOf(o);if(r<0){if(i.byteLength>1024*2)throw new Error("Unable to read .ply file header");return null}if(!a.startsWith("ply")||a.indexOf("format binary_little_endian 1.0")<0)throw new Error("Unknow .ply file header");const l=parseInt(/element vertex (\d+)\n/.exec(a)[1]);let u=0,c={},d={};const f={double:"getFloat64",int:"getInt32",uint:"getUint32",float:"getFloat32",short:"getInt16",ushort:"getUint16",uchar:"getUint8"};for(let I of a.slice(0,r).split(`
`).filter(p=>p.startsWith("property "))){const[p,h,g]=I.split(" "),v=f[h]||"getInt8";d[g]=v,c[g]=u,u+=parseInt(v.replace(/[^\d]/g,""))/8}let m=0;d.f_rest_44?m=3:d.f_rest_23?m=2:d.f_rest_8&&(m=1);const C=["x","y","z","scale_0","scale_1","scale_2","f_dc_0","f_dc_1","f_dc_2","opacity","rot_0","rot_1","rot_2","rot_3"];for(let I=0;I<C.length;I++){const p=C[I];if(!d[p])throw new Error(`Property not found: ${p}`)}return{headerLength:r+o.length,offsets:c,rowLength:u,vertexCount:l,shDegree:m}}}function Vo(n,t){let e=t.byteLength/O;const s=Math.min(n.fetchLimit,n.modelSplatCount);n.dataSplatCount+e>s?(e=s-n.dataSplatCount,n.splatData.set(t.slice(0,e*O),n.dataSplatCount*O)):n.splatData.set(t,n.dataSplatCount*O);const i=new Float32Array(t.buffer);for(let o=0,r=0,l=0,u=0;o<e;o++)r=i[o*8],l=i[o*8+1],u=i[o*8+2],n.minX=Math.min(n.minX,r),n.maxX=Math.max(n.maxX,r),n.minY=Math.min(n.minY,l),n.maxY=Math.max(n.maxY,l),n.minZ=Math.min(n.minZ,u),n.maxZ=Math.max(n.maxZ,u);n.dataSplatCount+=e;const a=0;n.currentRadius=Math.sqrt(n.maxX*n.maxX+a*a+n.maxZ*n.maxZ),n.aabbCenter=new A.Vector3((n.minX+n.maxX)/2,(n.minY+n.maxY)/2,(n.minZ+n.maxZ)/2),n.maxRadius=.5*Math.sqrt(Math.pow(n.maxX-n.minX,2)+Math.pow(n.maxY-n.minY,2)+Math.pow(n.maxZ-n.minZ,2)),n.metaMatrix&&n.aabbCenter.applyMatrix4(n.metaMatrix)}const ye=wt?20480:51200;async function hc(n){let t=0;try{n.status=U.Fetching;const s=n.abortController.signal,i=n.opts.fetchReload?"reload":"default",a=await fetch(n.opts.url,{mode:"cors",credentials:"omit",cache:i,signal:s});if(a.status!=200){console.warn(`fetch error: ${a.status}`),n.status===U.Fetching&&(n.status=U.FetchFailed);return}const o=a.body.getReader(),r=parseInt(a.headers.get("content-length")||"0");n.rowLength=32,n.fileSize=r;const l=r/n.rowLength|0;if(l<1){console.warn("data empty",n.opts.url),n.status===U.Fetching&&(n.status=U.Invalid);return}n.CompressionRatio="7.75x",n.modelSplatCount=l,n.downloadSplatCount=0,n.splatData=new Uint8Array(Math.min(n.modelSplatCount,n.fetchLimit)*32),n.watermarkData=new Uint8Array(0);let u=new Uint8Array(32),c=0;for(;;){let{done:d,value:f}=await o.read();if(d)break;c+f.byteLength<n.rowLength?(u.set(f,c),c+=f.byteLength,t+=f.length,n.downloadSize=t):(c=await e(n,c,u,f),c&&u.set(f.slice(f.byteLength-c),0)),n.downloadSplatCount>=n.fetchLimit&&n.abortController.abort()}}catch(s){s.name==="AbortError"?(n.opts.url,n.status===U.Fetching&&(n.status=U.FetchAborted)):(console.error(s),n.status===U.Fetching&&(n.status=U.FetchFailed))}finally{n.status===U.Fetching&&(n.status=U.FetchDone)}async function e(s,i,a,o){return new Promise(async r=>{let l=(i+o.byteLength)/s.rowLength|0,u=(i+o.byteLength)%s.rowLength,c;i?(c=new Uint8Array(l*s.rowLength),c.set(a.slice(0,i),0),c.set(o.slice(0,o.byteLength-u),i)):c=o.slice(0,l*s.rowLength),s.downloadSplatCount+l>s.fetchLimit&&(l=s.fetchLimit-s.downloadSplatCount,u=0);const d=async()=>{if(l>ye){const f=await Ye(c,ye);_o(s,f),s.downloadSplatCount+=ye,t+=ye*s.rowLength,s.downloadSize=t,l-=ye,c=c.slice(ye*s.rowLength),setTimeout(d)}else{const f=await Ye(c,l);_o(s,f),s.downloadSplatCount+=l,t+=l*s.rowLength,s.downloadSize=t,r(u)}};await d()})}}function _o(n,t){let e=t.byteLength/O;const s=Math.min(n.fetchLimit,n.modelSplatCount);n.dataSplatCount+e>s?(e=s-n.dataSplatCount,n.splatData.set(t.slice(0,e*O),n.dataSplatCount*O)):n.splatData.set(t,n.dataSplatCount*O);const i=new Float32Array(t.buffer);for(let o=0,r=0,l=0,u=0;o<e;o++)r=i[o*8],l=i[o*8+1],u=i[o*8+2],n.minX=Math.min(n.minX,r),n.maxX=Math.max(n.maxX,r),n.minY=Math.min(n.minY,l),n.maxY=Math.max(n.maxY,l),n.minZ=Math.min(n.minZ,u),n.maxZ=Math.max(n.maxZ,u);n.dataSplatCount+=e;const a=n.header?.MinTopY||0;n.currentRadius=Math.sqrt(n.maxX*n.maxX+a*a+n.maxZ*n.maxZ),n.aabbCenter=new A.Vector3((n.minX+n.maxX)/2,(n.minY+n.maxY)/2,(n.minZ+n.maxZ)/2),n.maxRadius=.5*Math.sqrt(Math.pow(n.maxX-n.minX,2)+Math.pow(n.maxY-n.minY,2)+Math.pow(n.maxZ-n.minZ,2)),n.metaMatrix&&n.aabbCenter.applyMatrix4(n.metaMatrix)}const mc=[Br,Rr];async function Cc(n){try{n.status=U.Fetching;const t=n.abortController.signal,e=n.opts.fetchReload?"reload":"default",s=await fetch(n.opts.url,{mode:"cors",credentials:"omit",cache:e,signal:t});if(s.status!=200){console.warn(`fetch error: ${s.status}`),n.status===U.Fetching&&(n.status=U.FetchFailed);return}const i=s.body.getReader(),a=parseInt(s.headers.get("content-length")||"0");if(a-me<Sr){console.warn("data empty",n.opts.url),n.status===U.Fetching&&(n.status=U.Invalid);return}n.fileSize=a;let r=[],l=new Uint8Array(me),u=new Uint8Array(O),c=0,d=!1,f=0,m=0,C,I=!1,p=0;for(;;){let{done:h,value:g}=await i.read();if(h)break;if(n.downloadSize+=g.byteLength,r){r.push(g);let R=0;for(let M=0;M<r.length;M++)R+=r[M].byteLength;if(R<me)continue;let x=0;for(let M=0;M<r.length;M++)x+r[M].byteLength<me?(l.set(r[M],x),x+=r[M].byteLength):(l.set(r[M].slice(0,me-x),x),g=new Uint8Array(r[M].slice(me-x)));const k=await gc(l);if(!k){n.abortController.abort(),n.status===U.Fetching&&(n.status=U.Invalid),console.error("invalid spx format");continue}if(n.meta.autoCut>1&&!Ic(k)){n.abortController.abort(),n.status===U.Fetching&&(n.status=U.Invalid),console.error("invalid LOD format");continue}if(n.header=k,n.CompressionRatio=Pn(k.SplatCount,a),n.modelSplatCount=k.SplatCount,n.dataShDegree=k.ShDegree,n.aabbCenter=new A.Vector3((k.MinX+k.MaxX)/2,(k.MinY+k.MaxY)/2,(k.MinZ+k.MaxZ)/2),n.maxRadius=.5*Math.sqrt(Math.pow(k.MaxX-k.MinX,2)+Math.pow(k.MaxY-k.MinY,2)+Math.pow(k.MaxZ-k.MinZ,2)),n.metaMatrix&&n.aabbCenter.applyMatrix4(n.metaMatrix),r=null,l=null,!mc.includes(k.ExclusiveId)){n.abortController.abort(),n.status=U.Invalid,console.error("Unrecognized format, creater id =",k.CreaterId,", exclusive id =",k.ExclusiveId,k.Comment);continue}}if(!d){if(c+g.byteLength<4){u.set(g,c),c+=g.byteLength;continue}const R=new Uint8Array(c+g.byteLength);R.set(u.slice(0,c),0),R.set(g,c),g=R.slice(4),c=0,d=!0,C=[],m=0;const x=new Int32Array(R.slice(0,4).buffer)[0];I=x<0,p=Math.abs(x)>>28>>>0,f=Math.abs(x)<<4>>>4}let v=m+g.byteLength;if(C.push(g),v<f){m+=g.byteLength;continue}for(;v>=f;){let R=new Uint8Array(f),x=0;for(let M=0;M<C.length;M++)x+C[M].byteLength<f?(R.set(C[M],x),x+=C[M].byteLength):(R.set(C[M].slice(0,f-x),x),g=new Uint8Array(C[M].slice(f-x)));I&&(p===0?R=await Lo(R):p===1?R=await oc(R):console.error("unsuported compress type:",p));const k=await Et(R,n.header);if(!k.success){console.error("spx block data parser failed. block format:",k.blockFormat),n.abortController.abort(),n.status=U.Invalid;break}if(k.isSplat)n.downloadSplatCount+=k.splatCount,yc(n,k.datas);else{const M=Math.min(n.fetchLimit,n.modelSplatCount);if(k.isSh23){if(n.sh12Count+k.splatCount>M){const P=M-n.sh12Count;n.sh12Data.push(k.datas.slice(0,P*16)),n.sh12Count+=P}else n.sh12Data.push(k.datas),n.sh12Count+=k.splatCount;if(n.sh3Count+k.splatCount>M){const P=M-n.sh3Count;n.sh3Data.push(k.dataSh3.slice(0,P*16)),n.sh3Count+=P}else n.sh3Data.push(k.dataSh3),n.sh3Count+=k.splatCount}else if(k.isSh3)if(n.sh3Count+k.splatCount>M){const P=M-n.sh3Count;n.sh3Data.push(k.datas.slice(0,P*16)),n.sh3Count+=P}else n.sh3Data.push(k.datas),n.sh3Count+=k.splatCount;else if(n.sh12Count+k.splatCount>M){const P=M-n.sh12Count;n.sh12Data.push(k.datas.slice(0,P*16)),n.sh12Count+=P}else n.sh12Data.push(k.datas),n.sh12Count+=k.splatCount}if(g.byteLength<4){u.set(g,0),c=g.byteLength,d=!1;break}else{const M=new Int32Array(g.slice(0,4).buffer)[0];I=M<0,p=Math.abs(M)>>28>>>0,f=Math.abs(M)<<4>>>4,g=g.slice(4),v=g.byteLength,C=[g],m=g.byteLength}}const B=n.fetchLimit;n.header.ShDegree===3?n.downloadSplatCount>=B&&n.sh12Count>=B&&n.sh3Count>=B&&n.abortController.abort():n.header.ShDegree?n.downloadSplatCount>=B&&n.sh12Count>=B&&n.abortController.abort():n.downloadSplatCount>=B&&n.abortController.abort()}}catch(t){t.name==="AbortError"?(console.warn("Fetch Abort",n.opts.url),n.status===U.Fetching&&(n.status=U.FetchAborted)):(console.error(t),n.status===U.Fetching&&(n.status=U.FetchFailed),n.abortController.abort())}finally{n.status===U.Fetching&&(n.status=U.FetchDone)}}function yc(n,t){let e=!!n.meta.autoCut,s=t.byteLength/32;const i=4096;if(!e){const l=Math.min(n.fetchLimit,n.modelSplatCount);if(!n.splatData&&(n.splatData=new Uint8Array(l*O)),!n.watermarkData&&(n.watermarkData=new Uint8Array(0)),n.dataSplatCount+s>l&&(s=l-n.dataSplatCount,!s))return;const u=new Float32Array(t.buffer),c=new Uint32Array(t.buffer);for(let f=0,m=0,C=0,I=0;f<s;f++)if(m=u[f*8],C=u[f*8+1],I=u[f*8+2],n.minX=Math.min(n.minX,m),n.maxX=Math.max(n.maxX,m),n.minY=Math.min(n.minY,C),n.maxY=Math.max(n.maxY,C),n.minZ=Math.min(n.minZ,I),n.maxZ=Math.max(n.maxZ,I),c[f*8+3]>>16){if(n.watermarkCount*O===n.watermarkData.byteLength){const p=new Uint8Array((n.watermarkCount+i)*O);p.set(n.watermarkData,0),n.watermarkData=p}n.watermarkData.set(t.slice(f*32,f*32+32),n.watermarkCount++*O)}else n.splatData.set(t.slice(f*32,f*32+32),n.dataSplatCount++*O);const d=n.header.MinTopY||0;n.currentRadius=Math.sqrt(n.maxX*n.maxX+d*d+n.maxZ*n.maxZ);return}let a=Math.min(Math.max(n.meta.autoCut,2),50);!n.watermarkData&&(n.watermarkData=new Uint8Array(0));const o=new Float32Array(t.buffer),r=new Uint32Array(t.buffer);for(let l=0,u=Math.floor(t.byteLength/O),c=0,d=0,f=0,m="";l<u;l++){if(r[l*8+3]>>16){if(n.watermarkCount*O===n.watermarkData.byteLength){const h=new Uint8Array((n.watermarkCount+i)*O);h.set(n.watermarkData,0),n.watermarkData=h}n.watermarkData.set(t.slice(l*32,l*32+32),n.watermarkCount++*O);continue}c=o[l*8],d=o[l*8+1],f=o[l*8+2];let C=Math.min(a-1,Math.floor(Math.max(0,c-n.header.MinX)/(n.header.MaxX-n.header.MinX)*a)),I=Math.min(a-1,Math.floor(Math.max(0,f-n.header.MinZ)/(n.header.MaxZ-n.header.MinZ)*a));m=`${C}-${I}`;let p=n.map.get(m);if(!p)p={},p.minX=c,p.maxX=c,p.minY=d,p.maxY=d,p.minZ=f,p.maxZ=f,p.center=new A.Vector3(c,d,f),n.metaMatrix&&p.center.applyMatrix4(n.metaMatrix),p.radius=0,p.splatData=new Uint8Array(i*O),p.splatData.set(t.slice(l*O,l*O+O),0),p.splatCount=1,n.map.set(m,p);else{if(p.splatData.byteLength/O==p.splatCount){const B=new Uint8Array(p.splatData.byteLength+i*O);B.set(p.splatData,0),p.splatData=B}p.minX=Math.min(p.minX,c),p.maxX=Math.max(p.maxX,c),p.minY=Math.min(p.minY,d),p.maxY=Math.max(p.maxY,d),p.minZ=Math.min(p.minZ,f),p.maxZ=Math.max(p.maxZ,f),p.center=new A.Vector3((p.maxX+p.minX)/2,(p.maxY+p.minY)/2,(p.maxZ+p.minZ)/2),n.metaMatrix&&p.center.applyMatrix4(n.metaMatrix);const h=p.maxX-p.minX,g=p.maxY-p.minY,v=p.maxZ-p.minZ;p.radius=Math.sqrt(h*h+g*g+v*v)/2,n.metaMatrix&&(p.radius*=n.metaMatrix.getMaxScaleOnAxis()),p.splatData.set(t.slice(l*O,l*O+O),p.splatCount++*O)}n.dataSplatCount++}}function Ic(n){return((n?.Flag1||0)&1)>0}const $=wt?20480:51200,Os=16,Uo=511;async function wc(n){try{n.status=U.Fetching;const i=n.abortController.signal,a=n.opts.fetchReload?"reload":"default",o=await fetch(n.opts.url,{mode:"cors",credentials:"omit",cache:a,signal:i});if(o.status!=200){console.warn(`fetch error: ${o.status}`),n.status===U.Fetching&&(n.status=U.FetchFailed);return}const r=o.body.getReader(),l=parseInt(o.headers.get("content-length")||"0");n.fileSize=l,n.downloadSize=0,n.downloadSplatCount=0,n.watermarkData=new Uint8Array(0);const u=new Uint8Array(l);for(;;){let{done:f,value:m}=await r.read();if(f)break;u.set(m,n.downloadSize),n.downloadSize+=m.length}const c=await Lo(u);if(!c||c.length<16){console.error("Invalid spz format"),n.status=U.Invalid;return}const d=s(c);n.CompressionRatio=Pn(d.numPoints,l),n.spzVersion=d.version,n.modelSplatCount=d.numPoints,n.dataShDegree=d.shDegree,n.splatData=new Uint8Array(Math.min(n.modelSplatCount,n.fetchLimit)*32),await t(d,n,c)}catch(i){i.name==="AbortError"?(n.opts.url,n.status===U.Fetching&&(n.status=U.FetchAborted)):(console.error(i),n.status===U.Fetching&&(n.status=U.FetchFailed))}finally{n.status===U.Fetching&&(n.status=U.FetchDone)}async function t(i,a,o){const r=i.numPoints*9,l=i.numPoints,u=i.numPoints*3,c=i.numPoints*3,d=i.numPoints*(i.version==2?3:4),f=Os,m=f+r,C=m+l,I=C+u,p=I+c,h=p+d,g=Math.min(i.numPoints,a.fetchLimit),v=Math.ceil(g/$);for(let B=0;B<v;B++){let R=B<v-1?$:g-B*$;a.dataSplatCount+R>a.fetchLimit&&(R=a.fetchLimit-a.dataSplatCount);const x=new Uint8Array(R*20+8),k=new Uint32Array(2);k[0]=R,k[1]=Qr,x.set(new Uint8Array(k.buffer),0);let M=8;for(let y=0;y<R;y++)x[M++]=o[f+(B*$+y)*9+0],x[M++]=o[f+(B*$+y)*9+1],x[M++]=o[f+(B*$+y)*9+2];for(let y=0;y<R;y++)x[M++]=o[f+(B*$+y)*9+3],x[M++]=o[f+(B*$+y)*9+4],x[M++]=o[f+(B*$+y)*9+5];for(let y=0;y<R;y++)x[M++]=o[f+(B*$+y)*9+6],x[M++]=o[f+(B*$+y)*9+7],x[M++]=o[f+(B*$+y)*9+8];for(let y=0;y<R;y++)x[M++]=o[I+(B*$+y)*3];for(let y=0;y<R;y++)x[M++]=o[I+(B*$+y)*3+1];for(let y=0;y<R;y++)x[M++]=o[I+(B*$+y)*3+2];for(let y=0;y<R;y++)x[M++]=Xs(o[C+(B*$+y)*3]);for(let y=0;y<R;y++)x[M++]=Xs(o[C+(B*$+y)*3+1]);for(let y=0;y<R;y++)x[M++]=Xs(o[C+(B*$+y)*3+2]);const P=[];if(i.version==2)for(let y=0,E=0,Q=0,w=0;y<R;y++)x[M++]=o[m+(B*$+y)],E=o[p+(B*$+y)*3+0],Q=o[p+(B*$+y)*3+1],w=o[p+(B*$+y)*3+2],P.push(bc(E,Q,w));else for(let y=0,E=0,Q=0,w=0,D=0;y<R;y++){x[M++]=o[m+(B*$+y)],E=o[p+(B*$+y)*4+0],Q=o[p+(B*$+y)*4+1],w=o[p+(B*$+y)*4+2],D=o[p+(B*$+y)*4+3];const S=E|Q<<8|w<<16|D<<24,T=S>>>30;let L=S,_=0,V=[];for(let N=3;N>=0;N--)if(N!==T){const tt=L&Uo,Y=(L>>9&1)>0;V[N]=Math.SQRT1_2*(tt/Uo),Y&&(V[N]=-V[N]),_+=V[N]*V[N],L=L>>10}V[T]=Math.sqrt(Math.max(1-_,0));for(let N=0;N<4;N++)V[N]=j(V[N]*128+128);P.push(V)}for(let y=0;y<R;y++)x[M++]=P[y][0];for(let y=0;y<R;y++)x[M++]=P[y][1];for(let y=0;y<R;y++)x[M++]=P[y][2];for(let y=0;y<R;y++)x[M++]=P[y][3];const G=await Et(x);if(e(i,a,G.datas),i.shDegree===1){const y=new Uint8Array(R*9+8),E=new Uint32Array(2);E[0]=R,E[1]=_e,y.set(new Uint8Array(E.buffer),0);for(let w=0,D=8;w<R;w++)y.set(o.slice(h+(B*$+w)*9,h+(B*$+w)*9+9),D),D+=9;const Q=await Et(y);a.sh12Data.push(Q.datas)}else if(i.shDegree===2){const y=new Uint8Array(R*24+8),E=new Uint32Array(2);E[0]=R,E[1]=Nt,y.set(new Uint8Array(E.buffer),0);for(let w=0,D=8;w<R;w++)y.set(o.slice(h+(B*$+w)*24,h+(B*$+w)*24+24),D),D+=24;const Q=await Et(y);a.sh12Data.push(Q.datas)}else if(i.shDegree===3){const y=new Uint8Array(R*24+8),E=new Uint32Array(2);E[0]=R,E[1]=Nt,y.set(new Uint8Array(E.buffer),0);for(let T=0,L=8;T<R;T++)y.set(o.slice(h+(B*$+T)*45,h+(B*$+T)*45+24),L),L+=24;const Q=await Et(y);a.sh12Data.push(Q.datas);const w=new Uint8Array(R*21+8),D=new Uint32Array(2);D[0]=R,D[1]=ie,w.set(new Uint8Array(D.buffer),0);for(let T=0,L=8;T<R;T++)w.set(o.slice(h+(B*$+T)*45+24,h+(B*$+T)*45+45),L),L+=21;const S=await Et(w);a.sh3Data.push(S.datas)}if(a.dataSplatCount>=a.fetchLimit)break}}function e(i,a,o){let r=o.byteLength/O;const l=Math.min(a.fetchLimit,i.numPoints);a.dataSplatCount+r>l?(r=l-a.dataSplatCount,a.splatData.set(o.slice(0,r*O),a.dataSplatCount*O)):a.splatData.set(o,a.dataSplatCount*O);const u=new Float32Array(o.buffer);for(let d=0,f=0,m=0,C=0;d<r;d++)f=u[d*8],m=u[d*8+1],C=u[d*8+2],a.minX=Math.min(a.minX,f),a.maxX=Math.max(a.maxX,f),a.minY=Math.min(a.minY,m),a.maxY=Math.max(a.maxY,m),a.minZ=Math.min(a.minZ,C),a.maxZ=Math.max(a.maxZ,C);a.dataSplatCount+=r,a.downloadSplatCount+=r;const c=0;a.currentRadius=Math.sqrt(a.maxX*a.maxX+c*c+a.maxZ*a.maxZ),a.aabbCenter=new A.Vector3((a.minX+a.maxX)/2,(a.minY+a.maxY)/2,(a.minZ+a.maxZ)/2),a.maxRadius=.5*Math.sqrt(Math.pow(a.maxX-a.minX,2)+Math.pow(a.maxY-a.minY,2)+Math.pow(a.maxZ-a.minZ,2)),a.metaMatrix&&a.aabbCenter.applyMatrix4(a.metaMatrix)}function s(i){const a=i.slice(0,Os),o=new Uint32Array(a.buffer),r={};if(r.magic=o[0],r.version=o[1],r.numPoints=o[2],r.shDegree=a[12],r.fractionalBits=a[13],r.flags=a[14],r.reserved=a[15],r.magic!==1347635022)throw new Error("[SPZ ERROR] header not found");if(r.version<2||r.version>3)throw new Error("[SPZ ERROR] version not supported:"+r.version);if(r.shDegree>3)throw new Error("[SPZ ERROR] unsupported SH degree:"+r.shDegree);if(r.fractionalBits!==12)throw new Error("[SPZ ERROR] unsupported FractionalBits:"+r.fractionalBits);const l=r.version==2?19:20;let u=0;if(r.shDegree===1?u=9:r.shDegree===2?u=24:r.shDegree===3&&(u=45),i.length!==Os+r.numPoints*(l+u))throw new Error("[SPZ ERROR] invalid spz data");return r}}function Xs(n){const t=(n-127.5)/38.25;return j((.5+Ut*t)*255)}function bc(n,t,e){const s=n/127.5-1,i=t/127.5-1,a=e/127.5-1,o=Math.sqrt(Math.max(0,1-(s*s+i*i+a*a)));return[j(o*128+128),j(s*128+128),j(i*128+128),j(a*128+128)]}async function Kt(n,t){if(!n)return new Uint8Array(0);const e=new AbortController,s=e.signal;try{t?.fire(os);const i=await fetch(n,{mode:"cors",credentials:"omit",cache:"reload",signal:s});if(i.status!=200)return console.warn(`fetch error: ${i.status}`),null;const a=i.body.getReader(),o=parseInt(i.headers.get("content-length")||"0"),r=new Uint8Array(o);let l=0;for(;;){let{done:u,value:c}=await a.read();if(u)break;r.set(c,l),l+=c.length,t?.fire(on,100*l/o)}return r}catch(i){console.error(i),e.abort()}return null}const No=wt?20480:51200,Ie=Math.sqrt(2);async function Ec(n){try{if(n.status=U.Fetching,n.opts.url.startsWith("blob:")||n.opts.url.endsWith(".sog")){n.status=U.Fetching;const t=n.abortController.signal,e=n.opts.fetchReload?"reload":"default",s=await fetch(n.opts.url,{mode:"cors",credentials:"omit",cache:e,signal:t});if(s.status!=200){console.warn(`fetch error: ${s.status}`),n.status===U.Fetching&&(n.status=U.FetchFailed);return}const i=s.body.getReader(),a=parseInt(s.headers.get("content-length")||"0");n.fileSize=a,n.downloadSize=0,n.downloadSplatCount=0,n.watermarkData=new Uint8Array(0);const o=new Uint8Array(a);for(;;){let{done:c,value:d}=await i.read();if(c)break;o.set(d,n.downloadSize),n.downloadSize+=d.length}const r=Ac(o),l=JSON.parse(uc(r.get("meta.json"))),u=l.count||l.means.shape[0];n.modelSplatCount=u,n.CompressionRatio=Pn(u,a),n.sogVersion=l.version?l.version:1,await Wo(n,r,l)}else{const t=await fetch(n.opts.url,{mode:"cors",credentials:"omit",cache:"reload"});if(t.status!=200)return console.error(`fetch error: ${t.status}`);const e=await t.json(),s=n.opts.url.split("/"),i=new Map,a=e.count||e.means.shape[0];n.modelSplatCount=a;const o=e.means.files[0];s[s.length-1]=o;const r=s.join("/"),l=e.means.files[1];s[s.length-1]=l;const u=s.join("/"),c=e.scales.files[0];s[s.length-1]=c;const d=s.join("/"),f=e.quats.files[0];s[s.length-1]=f;const m=s.join("/"),C=e.sh0.files[0];s[s.length-1]=C;const I=s.join("/"),p=e.shN?e.shN.files[0]:"shN_centroids.webp";s[s.length-1]=p;const h=s.join("/"),g=e.shN?e.shN.files[1]:"shN_labels.webp";s[s.length-1]=g;const v=s.join("/"),[B,R,x,k,M,P,G]=await Promise.all([Kt(r),Kt(u),Kt(d),Kt(m),Kt(I),Kt(e.shN?h:""),Kt(e.shN?v:"")]);i.set(o,B),i.set(l,R),i.set(c,x),i.set(f,k),i.set(C,M),i.set(p,P),i.set(g,G);const y=JSON.stringify(e).length+B.length+R.length+x.length+k.length+M.length+P.length+G.length;n.fileSize=y,n.downloadSize=y,n.watermarkData=new Uint8Array(0),n.CompressionRatio=Pn(a,y),n.sogVersion=e.version?e.version:1,await Wo(n,i,e)}}catch(t){t.name==="AbortError"?(n.opts.url,n.status===U.Fetching&&(n.status=U.FetchAborted)):(console.error(t),n.status===U.Fetching&&(n.status=U.FetchFailed))}finally{n.status===U.Fetching&&(n.status=U.FetchDone)}}async function Wo(n,t,e){const s=!e.version,i=s?e.means.shape[0]:e.count,a=Math.min(i,n.fetchLimit),o=e.shN?3:0,r=new Uint8Array(a*Rn);n.modelSplatCount=i,n.dataShDegree=o,n.splatData=r;const{rgba:l}=await dt(t.get(e.means.files[0])),{rgba:u}=await dt(t.get(e.means.files[1])),{rgba:c}=await dt(t.get(e.scales.files[0])),{rgba:d}=await dt(t.get(e.quats.files[0])),{rgba:f}=await dt(t.get(e.sh0.files[0])),{rgba:m,width:C}=e.shN?await dt(t.get(e.shN.files[0])):{rgba:null,width:0},{rgba:I}=e.shN?await dt(t.get(e.shN.files[1])):{rgba:null};for(;a>n.dataSplatCount;){const v=s?await p(n.dataSplatCount):await h(n.dataSplatCount);n.splatData.set(v.slice(0),n.dataSplatCount*O),g(n,v),n.dataSplatCount+=v.byteLength/O,n.downloadSplatCount=n.dataSplatCount}async function p(v){const B=Math.min(v+No,a),R=B-v,x=new Uint8Array(R*Rn),k=new Float32Array(x.buffer);for(let M=v,P=0;M<B;M++){let G=(u[M*4+0]<<8|l[M*4+0])/65535,y=(u[M*4+1]<<8|l[M*4+1])/65535,E=(u[M*4+2]<<8|l[M*4+2])/65535,Q=e.means.mins[0]+(e.means.maxs[0]-e.means.mins[0])*G,w=e.means.mins[1]+(e.means.maxs[1]-e.means.mins[1])*y,D=e.means.mins[2]+(e.means.maxs[2]-e.means.mins[2])*E;Q=Math.sign(Q)*(Math.exp(Math.abs(Q))-1),w=Math.sign(w)*(Math.exp(Math.abs(w))-1),D=Math.sign(D)*(Math.exp(Math.abs(D))-1);let S=c[M*4+0]/255,T=c[M*4+1]/255,L=c[M*4+2]/255;S=Math.exp(e.scales.mins[0]+(e.scales.maxs[0]-e.scales.mins[0])*S),T=Math.exp(e.scales.mins[1]+(e.scales.maxs[1]-e.scales.mins[1])*T),L=Math.exp(e.scales.mins[2]+(e.scales.maxs[2]-e.scales.mins[2])*L);let _=(d[M*4+0]/255-.5)*Ie,V=(d[M*4+1]/255-.5)*Ie,N=(d[M*4+2]/255-.5)*Ie,tt=Math.sqrt(Math.max(0,1-_*_-V*V-N*N)),Y=d[M*4+3]-252,X,H,K,it;Y==0?(X=tt,H=_,K=V,it=N):Y==1?(X=_,H=tt,K=V,it=N):Y==2?(X=_,H=V,K=tt,it=N):(X=_,H=V,K=N,it=tt);let ut=e.sh0.mins[0]+(e.sh0.maxs[0]-e.sh0.mins[0])*(f[M*4+0]/255),mt=e.sh0.mins[1]+(e.sh0.maxs[1]-e.sh0.mins[1])*(f[M*4+1]/255),Qt=e.sh0.mins[2]+(e.sh0.maxs[2]-e.sh0.mins[2])*(f[M*4+2]/255),ct=e.sh0.mins[3]+(e.sh0.maxs[3]-e.sh0.mins[3])*(f[M*4+3]/255);k[P*8+0]=Q,k[P*8+1]=w,k[P*8+2]=D,k[P*8+3]=S,k[P*8+4]=T,k[P*8+5]=L,x[P*32+24]=j((.5+Ut*ut)*255),x[P*32+25]=j((.5+Ut*mt)*255),x[P*32+26]=j((.5+Ut*Qt)*255),x[P*32+27]=j(1/(1+Math.exp(-ct))*255),x[P*32+28]=j(X*128+128),x[P*32+29]=j(H*128+128),x[P*32+30]=j(K*128+128),x[P*32+31]=j(it*128+128),P++}if(o>0){const M=new Uint32Array(2);M[0]=R,M[1]=Nt;const P=new Uint8Array(8+R*24);P.set(new Uint8Array(M.buffer),0);const G=new Uint32Array(2);G[0]=R,G[1]=ie;const y=new Uint8Array(8+R*21);y.set(new Uint8Array(G.buffer),0);for(let w=v,D=0;w<B;w++){const S=I[w*4+0]+(I[w*4+1]<<8),T=(S&63)*15,_=(S>>6)*C+T,V=new Uint8Array(9),N=new Uint8Array(15),tt=new Uint8Array(21);let Y;for(let X=0;X<3;X++){for(let H=0;H<3;H++)Y=(e.shN.maxs-e.shN.mins)*m[(_+H)*4+X]/255+e.shN.mins,V[H*3+X]=j(Math.round(Y*128)+128);for(let H=0;H<5;H++)Y=(e.shN.maxs-e.shN.mins)*m[(_+3+H)*4+X]/255+e.shN.mins,N[H*3+X]=j(Math.round(Y*128)+128);for(let H=0;H<7;H++)Y=(e.shN.maxs-e.shN.mins)*m[(_+8+H)*4+X]/255+e.shN.mins,tt[H*3+X]=j(Math.round(Y*128)+128)}P.set(V,8+D*24),P.set(N,8+D*24+9),y.set(tt,8+D*21),D++}const E=await Et(P);n.sh12Data.push(E.datas);const Q=await Et(y);n.sh3Data.push(Q.datas)}return await Ye(x,R)}async function h(v){const B=Math.min(v+No,a),R=B-v,x=new Uint8Array(R*Rn),k=new Float32Array(x.buffer);for(let M=v,P=0;M<B;M++){let G=(u[M*4+0]<<8|l[M*4+0])/65535,y=(u[M*4+1]<<8|l[M*4+1])/65535,E=(u[M*4+2]<<8|l[M*4+2])/65535,Q=e.means.mins[0]+(e.means.maxs[0]-e.means.mins[0])*G,w=e.means.mins[1]+(e.means.maxs[1]-e.means.mins[1])*y,D=e.means.mins[2]+(e.means.maxs[2]-e.means.mins[2])*E;Q=Math.sign(Q)*(Math.exp(Math.abs(Q))-1),w=Math.sign(w)*(Math.exp(Math.abs(w))-1),D=Math.sign(D)*(Math.exp(Math.abs(D))-1);let S=Math.exp(e.scales.codebook[c[M*4+0]]),T=Math.exp(e.scales.codebook[c[M*4+1]]),L=Math.exp(e.scales.codebook[c[M*4+2]]),_=(d[M*4+0]/255-.5)*Ie,V=(d[M*4+1]/255-.5)*Ie,N=(d[M*4+2]/255-.5)*Ie,tt=Math.sqrt(Math.max(0,1-_*_-V*V-N*N)),Y=d[M*4+3]-252,X,H,K,it;Y==0?(X=tt,H=_,K=V,it=N):Y==1?(X=_,H=tt,K=V,it=N):Y==2?(X=_,H=V,K=tt,it=N):(X=_,H=V,K=N,it=tt);let ut=e.sh0.codebook[f[M*4+0]],mt=e.sh0.codebook[f[M*4+1]],Qt=e.sh0.codebook[f[M*4+2]],ct=f[M*4+3];k[P*8+0]=Q,k[P*8+1]=w,k[P*8+2]=D,k[P*8+3]=S,k[P*8+4]=T,k[P*8+5]=L,x[P*32+24]=j((.5+Ut*ut)*255),x[P*32+25]=j((.5+Ut*mt)*255),x[P*32+26]=j((.5+Ut*Qt)*255),x[P*32+27]=ct,x[P*32+28]=j(X*128+128),x[P*32+29]=j(H*128+128),x[P*32+30]=j(K*128+128),x[P*32+31]=j(it*128+128),P++}if(o>0){const M=new Uint32Array(2);M[0]=R,M[1]=Nt;const P=new Uint8Array(8+R*24);P.set(new Uint8Array(M.buffer),0);const G=new Uint32Array(2);G[0]=R,G[1]=ie;const y=new Uint8Array(8+R*21);y.set(new Uint8Array(G.buffer),0);for(let w=v,D=0;w<B;w++){const S=I[w*4+0]+(I[w*4+1]<<8),T=(S&63)*15,_=(S>>6)*C+T,V=new Uint8Array(9),N=new Uint8Array(15),tt=new Uint8Array(21);let Y;for(let X=0;X<3;X++){for(let H=0;H<3;H++)Y=e.shN.codebook[m[(_+H)*4+X]],V[H*3+X]=j(Math.round(Y*128)+128);for(let H=0;H<5;H++)Y=e.shN.codebook[m[(_+3+H)*4+X]],N[H*3+X]=j(Math.round(Y*128)+128);for(let H=0;H<7;H++)Y=e.shN.codebook[m[(_+8+H)*4+X]],tt[H*3+X]=j(Math.round(Y*128)+128)}P.set(V,8+D*24),P.set(N,8+D*24+9),y.set(tt,8+D*21),D++}const E=await Et(P);n.sh12Data.push(E.datas);const Q=await Et(y);n.sh3Data.push(Q.datas)}return await Ye(x,R)}function g(v,B){const R=B.byteLength/O,x=new Float32Array(B.buffer);for(let M=0,P=0,G=0,y=0;M<R;M++)P=x[M*8],G=x[M*8+1],y=x[M*8+2],v.minX=Math.min(v.minX,P),v.maxX=Math.max(v.maxX,P),v.minY=Math.min(v.minY,G),v.maxY=Math.max(v.maxY,G),v.minZ=Math.min(v.minZ,y),v.maxZ=Math.max(v.maxZ,y);const k=0;v.currentRadius=Math.sqrt(v.maxX*v.maxX+k*k+v.maxZ*v.maxZ),v.aabbCenter=new A.Vector3((v.minX+v.maxX)/2,(v.minY+v.maxY)/2,(v.minZ+v.maxZ)/2),v.maxRadius=.5*Math.sqrt(Math.pow(v.maxX-v.minX,2)+Math.pow(v.maxY-v.minY,2)+Math.pow(v.maxZ-v.minZ,2)),v.metaMatrix&&v.aabbCenter.applyMatrix4(v.metaMatrix)}}function Sc(n){const t=(E,Q,w)=>n.on(E,Q,w),e=(E,...Q)=>n.fire(E,...Q);let s,i=Date.now()+36e5,a=0,o=0,r=0,l=!1,u=null,c,d={index:0,version:0},f={index:1,version:0},m=!1;const C=e(It);let I=!1,p=0;t(lo,()=>c?.aabbCenter||new A.Vector3);let h;const g=new Promise(E=>h=E);t(te,async()=>{const E=e(F);let Q=wt?E.maxRenderCountOfMobile:E.maxRenderCountOfPc;if(!E.bigSceneMode){let w=await g;Q=Math.min(w,Q)+10240}return Q}),t(Fe,async E=>{const Q=e(F);if(Q.bigSceneMode)return 1;let w=wt?Q.maxRenderCountOfMobile:Q.maxRenderCountOfPc,D=await g;if(w=Math.min(D,w),!c.dataShDegree)return 1;if(E>=3){if(c.dataShDegree<3)return 1}else if(E>=1){if(c.dataShDegree<1)return 1}else return 1;const S=1024*2;return Math.ceil(w/S)}),t(bs,async()=>e(F).bigSceneMode?0:(await g,c.dataShDegree)),t(Is,async(E,Q=!0)=>{try{await g;const w=!!c.header?.Flag2;u=await e(to,E,Q,w),c&&(c.textWatermarkVersion=Date.now())}catch{console.info("failed to generate watermark")}}),t(oo,E=>{C&&(E?(!f.active&&(f.activeTime=Date.now()),f.active=!0):(!d.active&&(d.activeTime=Date.now()),d.active=!0))}),t(As,()=>{if(C)return d.version<=f.version?d.xyz:f.xyz;if(c?.status===U.FetchDone||c?.status===U.FetchAborted){if(c.activePoints&&c.activePoints.length===void 0)return c.activePoints;const E={},Q=d.xyz;for(let w=0,D=Q.length/3,S=0,T=0,L=0,_="";w<D;w++)S=Q[w*3],T=Q[w*3+1],L=Q[w*3+2],_=`${Math.floor(S/2)*2+1},${Math.floor(T/2)*2+1},${Math.floor(L/2)*2+1}`,(E[_]=E[_]||[]).push(S,T,L);return c.activePoints=E}return d.xyz});async function v(E){if(s)return;if(c&&(c.status===U.Invalid||c.status===U.FetchFailed))return e(F).viewerEvents?.fire(gt),e(Be,0)||e(rt,{renderSplatCount:0,visibleSplatCount:0,modelSplatCount:0});if(!c||!c.downloadSize)return;const Q=e(F),w=c.status!==U.FetchReady&&c.status!==U.Fetching;if(M(w),Q.disableStreamLoading&&!w){e(on,100*c.downloadSize/c.fileSize);return}if(w){const D=Math.min(c.fetchLimit,c.downloadSplatCount);!c.notifyFetchStopDone&&(c.notifyFetchStopDone=!0)&&e(Be,D),r||(r=Date.now(),Q.onPerformanceUpdate?.({fullLoadTime:r-a,totalSplatCount:c.downloadSplatCount,phase:"complete"}))}else{const D=100*c.downloadSize/c.fileSize;e(on,D),Q.onPerformanceUpdate?.({currentSplatCount:c.downloadSplatCount,loadProgress:D,phase:"loading"})}c.dataSplatCount&&(m||(m=!0,setTimeout(async()=>{E?await R(w):await B(w),m=!1})))}async function B(E){if(s)return;const Q=d,w=await e(te),D=u;let S=c.dataSplatCount,T=E?c.watermarkCount:0,L=c.meta.showWatermark&&E?(D?.byteLength||0)/32:0;if(c.renderSplatCount=S+T+L,c.renderSplatCount>=w&&(c.renderSplatCount=w,T=0,L=0,S>w&&(S=w)),e(rt,{visibleSplatCount:c.renderSplatCount,modelSplatCount:c.modelSplatCount+L}),Date.now()-Q.textureReadyTime<br||c.smallSceneUploadDone&&c.lastTextWatermarkVersion==c.textWatermarkVersion)return;if(!Q.version){e(Ri,(c.header?.Flag2?c.header.MaxTopY:c.header?.MinTopY)||0);const K=c.aabbCenter||new A.Vector3;e(Li,K.x,K.y,K.z);let it=c.opts.format,ut="　";c.opts.format=="spx"?(it="spx v"+c.header.Version+(c.header.ExclusiveId?(", "+c.header.ExclusiveId).substring(0,5):""),ut+=c.CompressionRatio):c.opts.format=="spz"?(it="spz v"+c.spzVersion,ut+=c.CompressionRatio):c.opts.format=="sog"?(it="sog v"+c.sogVersion,ut+=c.CompressionRatio):c.opts.format=="splat"&&(ut+=c.CompressionRatio);const mt="　"+(c.fileSize/1024/1024).toFixed(1)+"M";e(rt,{scene:`small (${it}) ${ut}${mt}`})}c.lastTextWatermarkVersion=c.textWatermarkVersion,Q.textureReady=!1;const _=1024*2,V=Math.ceil(2*w/_),N=new Uint32Array(_*V*4),tt=new Float32Array(N.buffer),Y=new Uint8Array(N.buffer);Y.set(c.splatData.slice(0,S*32),0),T&&Y.set(c.watermarkData.slice(0,T*32),S*32),L&&Y.set(D.slice(0,L*32),(S+T)*32);const X=new Float32Array(c.renderSplatCount*3);for(let K=0,it=0;K<c.renderSplatCount;K++)X[K*3]=tt[K*8],X[K*3+1]=tt[K*8+1],X[K*3+2]=tt[K*8+2];const H=Date.now();if(Q.version=H,Q.txdata=N,Q.xyz=X,Q.renderSplatCount=c.renderSplatCount,Q.visibleSplatCount=c.downloadSplatCount+L,Q.modelSplatCount=c.downloadSplatCount+L,Q.watermarkCount=T+L,Q.minX=c.minX,Q.maxX=c.maxX,Q.minY=c.minY,Q.maxY=c.maxY,Q.minZ=c.minZ,Q.maxZ=c.maxZ,c.meta.particleMode&&!p&&(p=performance.now(),e(In,p),e(ee,!1),e(bn,1)),c.maxRadius&&e(fo,c.maxRadius),!l&&Q.renderSplatCount>0&&(l=!0,o=Date.now(),e(F).onPerformanceUpdate?.({firstFrameTime:o-a,currentSplatCount:Q.renderSplatCount,phase:"firstFrame"})),e(ws,Q,c.currentRadius,c.currentRadius),i=H,E&&!c.smallSceneUploadDone){c.smallSceneUploadDone=!0,e(ao,c.sh12Data),e(ro,c.sh3Data),c.sh12Data=null,c.sh3Data=null;const K=e(F);e(je,K.shDegree===void 0?3:K.shDegree),e(As),c.meta.particleMode&&setTimeout(()=>{e(In,performance.now()+5e3),e(bn,2),setTimeout(()=>e(wn,!0),5e3)},Math.max(5e3+p-performance.now(),0))}e(rt,{renderSplatCount:c.renderSplatCount})}async function R(E){if(s)return;const Q=await e(te),w=1024*2,D=Math.ceil(2*Q/w),S=u,T=0,L=(S?.byteLength||0)/32,_=Q-T-L;e(rt,{modelSplatCount:c.downloadSplatCount+L});let V=d.version<=f.version?d:f;if(d.version&&(!V.index&&!f.active||V.index&&!d.active)||Date.now()-V.activeTime<Er)return;if(E){const et=e(F).viewerEvents;if(et&&!et.fire(is))return}if(!V.version){let et=c.opts.format,ot="　";c.opts.format=="spx"&&(et="spx"+(c.header.ExclusiveId?(" "+c.header.ExclusiveId).substring(0,6):""),ot+=c.CompressionRatio);const Vt="　"+(c.fileSize/1024/1024).toFixed(1)+"M";e(rt,{scene:`large (${et}) ${ot}${Vt}`})}const N=Date.now();V.version=N,V.active=!1;let tt=0;const Y=[],X=e(wi),H=e(Lt),K=e(_t);for(const et of c.map.values())k(X,H,K,et)&&(Y.push(et),et.currentRenderCnt=et.splatCount,tt+=et.splatCount);e(rt,{cuts:`${Y.length} / ${c.map.size}`});const it=Math.min(_/tt,1);if(it>.95)for(const et of Y)et.currentRenderCnt=et.splatCount*it|0;else{Y.sort((ot,Vt)=>ot.distance-Vt.distance);for(const ot of Y)ot.distance<5?ot.distance*=.5:ot.distance<4?ot.distance*=.4:ot.distance<3?ot.distance*=.3:ot.distance<2&&(ot.distance*=.1);x(Y,_);let et=0;for(let ot of Y)et+=ot.currentRenderCnt;if(et>_){let ot=et-_;for(let Vt=Y.length-1;Vt>=0&&!(ot<=0);Vt--){const Dt=Y[Vt];Dt.currentRenderCnt>=ot?(Dt.currentRenderCnt-=ot,ot=0):(ot-=Dt.currentRenderCnt,Dt.currentRenderCnt=0)}}else if(et<_){let ot=_-et;for(let Vt=0;Vt<Y.length&&!(ot<=0);Vt++){let Dt=Y[Vt];if(Dt.splatCount>Dt.currentRenderCnt)if(Dt.splatCount-Dt.currentRenderCnt>=ot)Dt.currentRenderCnt+=ot,ot=0;else{const Ya=Dt.splatCount-Dt.currentRenderCnt;Dt.currentRenderCnt+=Ya,ot-=Ya}}}}const ut=new Uint32Array(w*D*4),mt=new Float32Array(ut.buffer),Qt=new Uint8Array(ut.buffer);let ct=0;for(let et of Y)Qt.set(et.splatData.slice(0,et.currentRenderCnt*32),ct*32),ct+=et.currentRenderCnt;L&&Qt.set(S.slice(0,L*32),(ct+T)*32);const Ee=ct+T+L,re=new Float32Array(Ee*3);for(let et=0,ot=0;et<Ee;et++)re[et*3]=mt[et*8],re[et*3+1]=mt[et*8+1],re[et*3+2]=mt[et*8+2];V.txdata=ut,V.xyz=re,V.renderSplatCount=Ee,V.visibleSplatCount=tt+c.watermarkCount+L,V.modelSplatCount=c.downloadSplatCount+L,V.watermarkCount=T+L,V.minX=c.header.MinX,V.maxX=c.header.MaxX,V.minY=c.header.MinY,V.maxY=c.header.MaxY,V.minZ=c.header.MinZ,V.maxZ=c.header.MaxZ,e(ws,V),i=N,e(rt,{visibleSplatCount:V.visibleSplatCount,modelSplatCount:V.modelSplatCount})}function x(E,Q){const w=E.map(T=>1/(T.distance+1e-6)),D=w.reduce((T,L)=>T+L,0);let S=0;if(E.forEach((T,L)=>{T.currentRenderCnt=Math.min(Math.floor(w[L]/D*Q),T.splatCount),S+=T.currentRenderCnt}),S<Q){const T=Q-S,L=E.map((V,N)=>V.currentRenderCnt<V.splatCount?w[N]:0),_=L.reduce((V,N)=>V+N,0);E.forEach((V,N)=>{if(_>0&&V.currentRenderCnt<V.splatCount){const tt=Math.min(Math.floor(L[N]/_*T),V.splatCount-V.currentRenderCnt);V.currentRenderCnt+=tt}})}}function k(E,Q,w,D){if(D.distance=Math.max(D.center.distanceTo(Q)-D.radius,0),!D.distance||D.center.distanceTo(w)<=2*D.radius)return!0;const S=new A.Vector4(D.center.x,D.center.y,D.center.z,1).applyMatrix4(E),T=3*S.w;return!(S.z<-T||S.x<-T||S.x>T||S.y<-T||S.y>T)}function M(E){if(!I){if(c.header){I=!0;const Q=new A.Vector3(c.header.MinX,c.header.MinY,c.header.MinZ),w=new A.Vector3(c.header.MaxX,c.header.MaxY,c.header.MaxZ);e(Ss,Q.x,Q.y,Q.z,w.x,w.y,w.z,c.meta.showBoundBox)}else if(E){I=!0;const Q=new A.Vector3(c.minX,c.minY,c.minZ),w=new A.Vector3(c.maxX,c.maxY,c.maxZ);e(Ss,Q.x,Q.y,Q.z,w.x,w.y,w.z,c.meta.showBoundBox)}}}function P(){s||(s=!0,c?.abortController?.abort(),c?.map?.clear(),c=null,u=null,d=null,f=null)}function G(E){if(E.opts.format==="spx")Cc(E);else if(E.opts.format==="splat")hc(E);else if(E.opts.format==="ply")pc(E);else if(E.opts.format==="spz")wc(E);else if(E.opts.format==="sog")Ec(E);else return!1;return!0}function y(E,Q){if(s)return;const w=e(F),D=wt?w.maxRenderCountOfMobile:w.maxRenderCountOfPc,S=e(It);if(a=Date.now(),o=0,r=0,l=!1,E.fetchReload=ic(Q.updateDate||0),c=new Ir(E,Q),S&&Q.autoCut){const L=Q.pcDownloadLimitSplatCount||xr,_=Q.mobileDownloadLimitSplatCount||vr,V=wt?_:L;c.fetchLimit=Math.min(Q.autoCut*Q.autoCut*D+D,V)}else c.fetchLimit=D;const T=()=>{if(!c||c.status==U.Invalid||c.status==U.FetchFailed)return h(0);c.modelSplatCount>0?(h(c.modelSplatCount),!c.meta.particleMode&&c.dataSplatCount?setTimeout(()=>e(Gi),5):setTimeout(T,10)):setTimeout(T,10)};if(T(),!G(c)){console.error("Unsupported format:",E.format),e(Be,0);return}e(os),e(rt,{cuts:""})}t(Ui,(E,Q)=>y(E,Q)),t(Ni,(E=1e4)=>Date.now()-i<E),t(Wi,()=>P()),e(le,async()=>await v(C),()=>!s)}var _n=(n=>(n[n.ModelCenterCirccle=1]="ModelCenterCirccle",n[n.ScreenCenterCircle=2]="ScreenCenterCircle",n[n.ScreenMiddleToLeftRight=3]="ScreenMiddleToLeftRight",n[n.ScreenMiddleToTopBottom=4]="ScreenMiddleToTopBottom",n))(_n||{});const Ho="currentVisibleRadius",Go="currentLightRadius",Yo="transitionEffect",Js="splatShTexture12",zo="maxPixelDiameter",Oo="minPixelDiameter",Ks="activeFlagValue",Zs="splatShTexture3",Xo="performanceNow",Un="performanceAct",Mc="waterMarkColor",Jo="showWaterMark",Ko="useSimilarExp",js="splatTexture0",qs="splatTexture1",Zo="particleMode",jo="bigSceneMode",qo="lightFactor",$o="debugEffect",ta="usingIndex",vc="splatIndex",ea="maxRadius",na="flagValue",sa="pointMode",ia="markPoint",oa="shDegree",aa="viewport",ra="minAlpha",ca="focal",la="topY",Aa="aabbCenter",ua="lookAtCenter";let J=0;J++;const da=`$${J++}`,xc=`$${J++}`;J++,J++,J++;const ga=`$${J++}`,Bc=`$${J++}`,fa=`$${J++}`,Qc=`$${J++}`,Dc=`$${J++}`,kc=`$${J++}`,Tc=`$${J++}`,Rc=`$${J++}`;J++;const Lc=`$${J++}`,Fc=`$${J++}`;J++;const Pc=`$${J++}`;J++;const Vc=`$${J++}`;J++;const _c=`$${J++}`,Uc=`$${J++}`,Nc=`$${J++}`,Wc=`$${J++}`,Hc=`$${J++}`,Gc=`$${J++}`,Yc=`$${J++}`,zc=`$${J++}`,Oc=`$${J++}`,Xc=`$${J++}`,pa=`$${J++}`,Jc=`$${J++}`,Kc=`$${J++}`,$s=`$${J++}`,ha=`$${J++}`,ma=`$${J++}`;var Zc=`precision highp float;
precision highp int;

uniform highp usampler2D splatTexture0, splatTexture1, splatShTexture12, splatShTexture3;
uniform vec2 focal, viewport;
uniform int usingIndex, shDegree, particleMode, transitionEffect;
uniform bool pointMode, bigSceneMode, showWaterMark, debugEffect;
uniform float topY, maxRadius, currentVisibleRadius, currentLightRadius, performanceNow, performanceAct;
uniform float minPixelDiameter, maxPixelDiameter, minAlpha;
uniform vec3 aabbCenter, lookAtCenter;
uniform vec4 markPoint, waterMarkColor;
uniform uint flagValue, activeFlagValue;

attribute uint splatIndex;

varying vec4 vColor;
varying vec3 vPosition;
vec3 animateParticle(vec3 v3Cen) {
    if (particleMode < 1)
        return v3Cen;
    float factor = particleMode > 1 ? ((performanceAct - performanceNow) / 5000.0) : ((performanceNow - performanceAct) / 5000.0);
    float radius = particleMode > 1 ? (max(currentVisibleRadius, maxRadius) * 0.6 * min((performanceNow) / 3000.0, 1.0)) : (max(currentVisibleRadius, maxRadius) * 0.6 * min((performanceNow - performanceAct) / 3000.0, 1.0));
    if (factor <= 0.0)
        return v3Cen;

    
    vec3 randSeed = fract(sin(vec3(dot(v3Cen, vec3(12.9898, 78.233, 37.719)), dot(v3Cen.yzx, vec3(49.123, 23.456, 87.654)), dot(v3Cen.zxy, vec3(34.567, 91.234, 56.789))))) * 2.0 - 1.0;

    
    float phase = factor * 12.0 + v3Cen.y * (15.0 + randSeed.x * 3.0) + v3Cen.z * (13.0 + randSeed.y * 2.0);

    
    float wave1 = sin(phase * (2.0 + randSeed.y * 1.5 + randSeed.z * 1.5));
    float wave2 = cos(phase * (1.2 + randSeed.x * 0.3) + v3Cen.x * 20.0);
    float dynamicFactor = mix(wave1, wave2, 0.5 + randSeed.z * 0.2) * 0.5 + 0.5;

    
    float amplitude = radius * 0.25 * factor * (0.9 + randSeed.z * 0.2);

    
    vec3 offset = vec3(amplitude * (dynamicFactor * 2.0 - 1.0), amplitude * randSeed.x * 5.0, amplitude * randSeed.y * 2.5);

    
    vec3 newPos = v3Cen + offset;
    float newDist = length(newPos);
    if (newDist > radius) {
        vec3 dir = normalize(newPos);
        float penetration = newDist - radius;
        float elasticity = 0.7 + randSeed.z * 0.2;

        
        vec3 bounceVec = dir * penetration * elasticity;
        vec3 tangent = normalize(cross(dir, vec3(randSeed.x, randSeed.y, 1.0)));
        newPos -= bounceVec - tangent * (length(randSeed.xy) * penetration * 0.2);
    }

    
    return normalize(newPos) * min(length(newPos), radius);
}
const float FactorSH = 0.0625;
const uint MaskSH = 0x1Fu;
const float SH_C1 = 0.4886025119029199;
const float[5] SH_C2 = float[](1.0925484305920792, -1.0925484305920792, 0.31539156525252005, -1.0925484305920792, 0.5462742152960396);
const float[7] SH_C3 = float[](-0.5900435899266435, 2.890611442640554, -0.4570457994644658, 0.3731763325901154, -0.4570457994644658, 1.445305721320277, -0.5900435899266435);

vec3[15] splatReadShDatas() {
    int shCnt = 0;
    float[45] fSHs;
    uvec4 rgb12 = texelFetch(splatShTexture12, ivec2((splatIndex & 0x7ffu), (splatIndex >> 11)), 0);
    if (rgb12.a > 0u) {
        shCnt = 3;
        fSHs[0] = float((rgb12.r >> 27) & MaskSH) * FactorSH - 1.0;
        fSHs[1] = float((rgb12.r >> 22) & MaskSH) * FactorSH - 1.0;
        fSHs[2] = float((rgb12.r >> 17) & MaskSH) * FactorSH - 1.0;
        fSHs[3] = float((rgb12.r >> 12) & MaskSH) * FactorSH - 1.0;
        fSHs[4] = float((rgb12.r >> 7) & MaskSH) * FactorSH - 1.0;
        fSHs[5] = float((rgb12.r >> 2) & MaskSH) * FactorSH - 1.0;
        fSHs[6] = float(((rgb12.r << 3) | (rgb12.g >> 29)) & MaskSH) * FactorSH - 1.0;
        fSHs[7] = float((rgb12.g >> 24) & MaskSH) * FactorSH - 1.0;
        fSHs[8] = float((rgb12.g >> 19) & MaskSH) * FactorSH - 1.0;

        if (shDegree > 1) {
            shCnt = 8;
            fSHs[9] = float((rgb12.g >> 14) & MaskSH) * FactorSH - 1.0;
            fSHs[10] = float((rgb12.g >> 9) & MaskSH) * FactorSH - 1.0;
            fSHs[11] = float((rgb12.g >> 4) & MaskSH) * FactorSH - 1.0;
            fSHs[12] = float(((rgb12.g << 1) | (rgb12.b >> 31)) & MaskSH) * FactorSH - 1.0;
            fSHs[13] = float((rgb12.b >> 26) & MaskSH) * FactorSH - 1.0;
            fSHs[14] = float((rgb12.b >> 21) & MaskSH) * FactorSH - 1.0;
            fSHs[15] = float((rgb12.b >> 16) & MaskSH) * FactorSH - 1.0;
            fSHs[16] = float((rgb12.b >> 11) & MaskSH) * FactorSH - 1.0;
            fSHs[17] = float((rgb12.b >> 6) & MaskSH) * FactorSH - 1.0;
            fSHs[18] = float((rgb12.b >> 1) & MaskSH) * FactorSH - 1.0;
            fSHs[19] = float(((rgb12.b << 4) | (rgb12.a >> 28)) & MaskSH) * FactorSH - 1.0;
            fSHs[20] = float(((rgb12.a >> 23) & MaskSH)) * FactorSH - 1.0;
            fSHs[21] = float((rgb12.a >> 18) & MaskSH) * FactorSH - 1.0;
            fSHs[22] = float((rgb12.a >> 13) & MaskSH) * FactorSH - 1.0;
            fSHs[23] = float((rgb12.a >> 8) & MaskSH) * FactorSH - 1.0;

            if (shDegree > 2) {
                uvec4 rgb3 = texelFetch(splatShTexture3, ivec2(splatIndex & 0x7ffu, splatIndex >> 11), 0);
                if (rgb3.a > 0u) {
                    shCnt = 15;
                    fSHs[24] = float((rgb3.r >> 27) & MaskSH) * FactorSH - 1.0;
                    fSHs[25] = float((rgb3.r >> 22) & MaskSH) * FactorSH - 1.0;
                    fSHs[26] = float((rgb3.r >> 17) & MaskSH) * FactorSH - 1.0;
                    fSHs[27] = float((rgb3.r >> 12) & MaskSH) * FactorSH - 1.0;
                    fSHs[28] = float((rgb3.r >> 7) & MaskSH) * FactorSH - 1.0;
                    fSHs[29] = float((rgb3.r >> 2) & MaskSH) * FactorSH - 1.0;
                    fSHs[30] = float(((rgb3.r << 3) | (rgb3.g >> 29)) & MaskSH) * FactorSH - 1.0;
                    fSHs[31] = float((rgb3.g >> 24) & MaskSH) * FactorSH - 1.0;
                    fSHs[32] = float((rgb3.g >> 19) & MaskSH) * FactorSH - 1.0;
                    fSHs[33] = float((rgb3.g >> 14) & MaskSH) * FactorSH - 1.0;
                    fSHs[34] = float((rgb3.g >> 9) & MaskSH) * FactorSH - 1.0;
                    fSHs[35] = float((rgb3.g >> 4) & MaskSH) * FactorSH - 1.0;
                    fSHs[36] = float(((rgb3.g << 1) | (rgb3.b >> 31)) & MaskSH) * FactorSH - 1.0;
                    fSHs[37] = float((rgb3.b >> 26) & MaskSH) * FactorSH - 1.0;
                    fSHs[38] = float((rgb3.b >> 21) & MaskSH) * FactorSH - 1.0;
                    fSHs[39] = float((rgb3.b >> 16) & MaskSH) * FactorSH - 1.0;
                    fSHs[40] = float((rgb3.b >> 11) & MaskSH) * FactorSH - 1.0;
                    fSHs[41] = float((rgb3.b >> 6) & MaskSH) * FactorSH - 1.0;
                    fSHs[42] = float((rgb3.b >> 1) & MaskSH) * FactorSH - 1.0;
                    fSHs[43] = float(((rgb3.b << 4) | (rgb3.a >> 28)) & MaskSH) * FactorSH - 1.0;
                    fSHs[44] = float((rgb3.a >> 23) & MaskSH) * FactorSH - 1.0;
                }
            }
        }
    }

    vec3[15] sh;
    for (int i = 0; i < 15; ++i) {
        sh[i] = i < shCnt ? vec3(fSHs[i * 3], fSHs[i * 3 + 1], fSHs[i * 3 + 2]) : vec3(0.0);
    }
    return sh;
}

vec3 splatEvalSH(in vec3 v3Cen) {
    vec3 dir = normalize(v3Cen - cameraPosition);
    float x = dir.x;
    float y = dir.y;
    float z = dir.z;

    vec3[15] sh = splatReadShDatas();
    vec3 result = SH_C1 * (-sh[0] * y + sh[1] * z - sh[2] * x);

    if (shDegree > 1) {
        float xx = x * x;
        float yy = y * y;
        float zz = z * z;
        float xy = x * y;
        float yz = y * z;
        float xz = x * z;

        result += sh[3] * (SH_C2[0] * xy) +
            sh[4] * (SH_C2[1] * yz) +
            sh[5] * (SH_C2[2] * (2.0 * zz - xx - yy)) +
            sh[6] * (SH_C2[3] * xz) +
            sh[7] * (SH_C2[4] * (xx - yy));

        if (shDegree > 2) {
            result += sh[8] * (SH_C3[0] * y * (3.0 * xx - yy)) +
                sh[9] * (SH_C3[1] * xy * z) +
                sh[10] * (SH_C3[2] * y * (4.0 * zz - xx - yy)) +
                sh[11] * (SH_C3[3] * z * (2.0 * zz - 3.0 * xx - 3.0 * yy)) +
                sh[12] * (SH_C3[4] * x * (4.0 * zz - xx - yy)) +
                sh[13] * (SH_C3[5] * z * (xx - yy)) +
                sh[14] * (SH_C3[6] * x * (xx - 3.0 * yy));
        }
    }
    return result;
}

void main() {
    uvec4 cen, cov3d;
    if (bigSceneMode) {
        if (usingIndex == 0) {
            cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
            cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
        } else {
            cen = texelFetch(splatTexture1, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
            cov3d = texelFetch(splatTexture1, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
        }
    } else {
        cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
        cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
    }

    bool isWatermark = (cen.w & 65536u) > 0u;
    float colorA = (float(cov3d.w >> 24) / 255.0);
    if (colorA < minAlpha && !isWatermark) {
        vColor = vec4(0.0);
        return;
    }

    vec3 v3Cen = uintBitsToFloat(cen.xyz);
    v3Cen = animateParticle(v3Cen);
    v3Cen = WatermarkEffect(v3Cen, isWatermark, debugEffect, performanceNow);

    vec4 cam = modelViewMatrix * vec4(v3Cen, 1.0);
    vec4 pos2d = projectionMatrix * cam;
    float clip = 1.05 * pos2d.w;
    if (pos2d.z < -clip || pos2d.x < -clip || pos2d.x > clip || pos2d.y < -clip || pos2d.y > clip || isWatermark && (!showWaterMark || pointMode)) {
        vColor = vec4(0.0);
        return;
    }

    
    float currentRadius = length(aabbCenter - v3Cen);
    if (currentVisibleRadius > 0.0 && currentRadius > currentVisibleRadius) {
        vColor = vec4(0.0);
        return;
    }

    vec2 uh1 = unpackHalf2x16(cov3d.x), uh2 = unpackHalf2x16(cov3d.y), uh3 = unpackHalf2x16(cov3d.z);
    mat3 Vrk = mat3(uh1.x, uh1.y, uh2.x, uh1.y, uh2.y, uh3.x, uh2.x, uh3.x, uh3.y);

    float ZxZ = cam.z * cam.z;
    mat3 J_m3 = mat3(focal.x / cam.z, 0.0, -(focal.x * cam.x) / ZxZ, 0.0, focal.y / cam.z, -(focal.y * cam.y) / ZxZ, 0.0, 0.0, 0.0);

    mat3 T_m3 = transpose(mat3(modelViewMatrix)) * J_m3;
    mat3 cov2d = transpose(T_m3) * Vrk * T_m3;

    cov2d[0][0] += 0.3;
    cov2d[1][1] += 0.3;
    vec3 cov2Dv = vec3(cov2d[0][0], cov2d[0][1], cov2d[1][1]);
    float disc = max(0.0, (cov2Dv.x + cov2Dv.z) * (cov2Dv.x + cov2Dv.z) / 4.0 - (cov2Dv.x * cov2Dv.z - cov2Dv.y * cov2Dv.y));
    float eigenValue1 = 0.5 * (cov2Dv.x + cov2Dv.z) + sqrt(disc);
    float eigenValue2 = max(0.5 * (cov2Dv.x + cov2Dv.z) - sqrt(disc), 0.0);
    float eigenValueOrig1 = eigenValue1;
    float eigenValueOrig2 = eigenValue2;

    
    if (!isWatermark) {
        if (pointMode) {
            eigenValue1 = eigenValue2 = 0.5;
        }

        if (!bigSceneMode && currentLightRadius > 0.0) {
            
            if (transitionEffect == 1) {
                
                vec3 transitionCenter = lookAtCenter;
                float distanceToCenter = length(v3Cen - transitionCenter);

                
                float fadeWidth = currentLightRadius * 0.1; 
                float fadeStart = currentLightRadius - fadeWidth;

                if (distanceToCenter < fadeStart) {
                    
                    if (pointMode) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                    } else {
                        eigenValue1 = eigenValue2 = 0.5;
                    }
                } else if (distanceToCenter < currentLightRadius) {
                    
                    if (pointMode) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                    } else {
                        eigenValue1 = eigenValue2 = 0.5;
                    }
                    
                }
                
            } else {
                vec4 p = projectionMatrix * (modelViewMatrix * vec4(v3Cen, 1.0));
                float currentRatio = transitionEffect == 2 ? length(p.xy / p.w) : (transitionEffect == 3 ? length(p.xx / p.w) : length(p.yy / p.w));
                float currentLightRatio = (performanceNow - performanceAct) / 500.0;
                if (currentRatio < currentLightRatio) {
                    if (pointMode) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                    } else {
                        eigenValue1 = eigenValue2 = 0.5;
                    }
                }
            }
        }
    }

    vPosition = vec3(position.xy, -1.0);
    vec2 eigenVector1 = normalize(vec2(cov2Dv.y, eigenValue1 - cov2Dv.x));
    if (markPoint.w > 0.0 && length(vec3(markPoint.xyz) - v3Cen) < 0.000001) {
        vColor = vec4(1.0, 1.0, 0.0, 1.0);
        eigenValue1 = eigenValue2 = 11.0;
        eigenVector1 = normalize(vec2(11.0, 0.0));
        vPosition.z = 1.0; 
    } else if (isWatermark) {
        vColor = waterMarkColor;
    } else {
        vColor = vec4(float(cov3d.w & 0xFFu) / 255.0, float((cov3d.w >> 8) & 0xFFu) / 255.0, float((cov3d.w >> 16) & 0xFFu) / 255.0, colorA);
        if (shDegree > 0) {
            vColor.rgb += splatEvalSH(v3Cen);
        }
        vColor = FvEffect(cen, vColor, activeFlagValue, performanceNow);

        
        
    }

    float diameter1 = min(sqrt(2.0 * eigenValue1), maxPixelDiameter);
    float diameter2 = min(sqrt(2.0 * eigenValue2), maxPixelDiameter);
    if (diameter1 < minPixelDiameter && diameter2 < minPixelDiameter && (pointMode && currentRadius < currentLightRadius || !pointMode && currentRadius > currentLightRadius)) {
        vColor = vec4(0.0);
        return;
    }

    vec2 eigenVector2 = vec2(eigenVector1.y, -eigenVector1.x);
    vec2 majorAxis = eigenVector1 * diameter1;
    vec2 minorAxis = eigenVector2 * diameter2;

    vec2 v2Center = vec2(pos2d) / pos2d.w;  
    gl_Position = vec4(v2Center + vPosition.x * majorAxis / viewport + vPosition.y * minorAxis / viewport, 1.0, 1.0);
}`,jc=`precision highp float;

uniform float lightFactor;
uniform float minAlpha;
uniform bool useSimilarExp;
varying vec4 vColor;
varying vec3 vPosition;

void main() {
    float alpha = vColor.a;
    if (alpha < minAlpha) {
        gl_FragColor = vec4(0.0);
        return;
    }
    float r2 = dot(vPosition.xy, vPosition.xy);
    if (r2 > 4.0) {
        gl_FragColor = vec4(0.0);
        return;
    }

    if (vPosition.z >= 1.0) {
        alpha = 1.0;
    } else {
        alpha *= useSimilarExp ? (1.0 / (1.0 + r2 * (1.0 + 0.5 * r2))) : exp(-r2);
        if (alpha <= minAlpha) {
            gl_FragColor = vec4(0.0);
            return;
        }
    }

    gl_FragColor = vec4(lightFactor * vColor.rgb, alpha);
}`,qc=`float fnWave(float minVal, float maxVal, float time) {
    return (sin(time * 0.005) + 1.0) * 0.5 * (maxVal - minVal) + minVal;
}`,$c=`vec4 FvEffect(uvec4 uv4Cen, vec4 v4Color, uint activeFv, float time) {
    uint fvSplat = uv4Cen.w & 65535u;
    if (fvSplat > 0u && fvSplat == activeFv) {
        return vec4(v4Color.rgb * fnWave(1.0, 1.6, time), v4Color.a);
    }
    return v4Color;
}`,tl=`vec3 WatermarkEffect(vec3 v3Cen, bool isWatermark, bool enableEffect, float time) {
    if (isWatermark && enableEffect) {
        v3Cen.y += sin(time * 0.002 + v3Cen.x) * 0.1; 
    }
    return v3Cen;
}`;function el(n){let t=!1;const e=(C,I,p)=>n.on(C,I,p),s=(C,...I)=>n.fire(C,...I);let i=0,a=0;const o=[];let r=0,l=0,u=0;e(co,()=>u),e(es,()=>{},!0),e(ns,()=>{},!0),e(Je,()=>{},!0),e(Ke,()=>{},!0),e(Mi,async()=>{const C=new A.InstancedBufferGeometry;C.setIndex([0,1,2,0,2,3]);const I=new Float32Array(12),p=new A.BufferAttribute(I,3);C.setAttribute("position",p),p.setXYZ(0,-2,-2,0),p.setXYZ(1,-2,2,0),p.setXYZ(2,2,2,0),p.setXYZ(3,2,-2,0),p.needsUpdate=!0;let h=new A.InstancedBufferGeometry().copy(C);const g=await s(te);if(t)return;const v=new Uint32Array(g),B=new A.InstancedBufferAttribute(v,1,!1);return B.setUsage(A.DynamicDrawUsage),B.needsUpdate=!0,h.setAttribute(vc,B),h.instanceCount=0,e(Qi,(R,x,k,M,P)=>{s(ki,x),v.set(R,0),B.clearUpdateRanges(),B.addUpdateRange(0,P),B.needsUpdate=!0,B.onUpload(()=>{s(oo,x),s(rt,{renderSplatCount:P})}),h.instanceCount=P,s(nt),s(rt,{sortTime:`${k} / ${Date.now()-M}`,bucketBits:r,sortType:l})}),e(Ja,()=>h),e(es,()=>{B.array=null,h.dispose()},!0),h}),e(vi,async()=>{const C=await s(te);if(t)return;const I=1024*2,p=Math.ceil(2*C/I),h=s(F),g=new A.ShaderMaterial({uniforms:s(Si),vertexShader:c(Zc),fragmentShader:c(jc),transparent:!0,alphaTest:1,blending:A.NormalBlending,depthTest:h.depthTest!==!1,depthWrite:!1,side:A.DoubleSide}),v=new Uint32Array(I*p*4);let B=new A.DataTexture(v,I,p,A.RGBAIntegerFormat,A.UnsignedIntType);B.internalFormat="RGBA32UI",B.needsUpdate=!0,g.uniforms[js].value=B;const R=s(It)?p:1,x=new Uint32Array(I*R*4);let k=new A.DataTexture(x,I,R,A.RGBAIntegerFormat,A.UnsignedIntType);k.internalFormat="RGBA32UI",k.needsUpdate=!0,g.uniforms[qs].value=k;const M=await s(Fe,1),P=new Uint32Array(I*M*4);let G=new A.DataTexture(P,I,M,A.RGBAIntegerFormat,A.UnsignedIntType);G.internalFormat="RGBA32UI",G.needsUpdate=!0,g.uniforms[Js].value=G;const y=await s(Fe,3),E=new Uint32Array(I*y*4);let Q=new A.DataTexture(E,I,y,A.RGBAIntegerFormat,A.UnsignedIntType);Q.internalFormat="RGBA32UI",Q.needsUpdate=!0,g.uniforms[Zs].value=Q,g.needsUpdate=!0;let w=!1;e(Di,S=>{if(!s(It)){if(w&&!S.renderSplatCount)return;w=!S.renderSplatCount}const T=S.txdata;S.txdata=null;const L=new A.DataTexture(T,I,p,A.RGBAIntegerFormat,A.UnsignedIntType);L.onUpdate=()=>{S.textureReady=!0,S.textureReadyTime=Date.now(),m(S),s(zi,S.renderSplatCount)},L.internalFormat="RGBA32UI",L.needsUpdate=!0,S.index?(g.uniforms[qs].value=L,k=L):(g.uniforms[js].value=L,B=L),g.needsUpdate=!0,s(nt)}),e(ao,async S=>{if(s(It)||!S||!S.length)return;const T=new Uint32Array(I*await s(Fe,1)*4),L=new Uint8Array(T.buffer);for(let V=0,N=0;V<S.length;V++)L.set(S[V],N),N+=S[V].byteLength;const _=new A.DataTexture(T,I,M,A.RGBAIntegerFormat,A.UnsignedIntType);_.internalFormat="RGBA32UI",_.needsUpdate=!0,g.uniforms[Js].value=_,g.needsUpdate=!0,s(nt)}),e(ro,async S=>{if(s(It)||!S||!S.length)return;const T=new Uint32Array(I*await s(Fe,3)*4),L=new Uint8Array(T.buffer);for(let V=0,N=0;V<S.length;V++)L.set(S[V],N),N+=S[V].byteLength;const _=new A.DataTexture(T,I,M,A.RGBAIntegerFormat,A.UnsignedIntType);_.internalFormat="RGBA32UI",_.needsUpdate=!0,g.uniforms[Zs].value=_,g.needsUpdate=!0,s(nt)}),e(Bi,()=>g),e(Je,()=>{const S=s(q),{width:T,height:L}=s(ne),_=Math.abs(S.projectionMatrix.elements[0])*.5*T,V=Math.abs(S.projectionMatrix.elements[5])*.5*L,N=s(Bi);N.uniforms[ca].value.set(_,V),N.uniformsNeedUpdate=!0,s(nt)},!0),e(Ke,()=>{const{width:S,height:T}=s(ne);g.uniforms[aa].value.set(S,T),g.uniformsNeedUpdate=!0,s(nt)},!0),e(ki,S=>{g.uniforms[ta].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(po,S=>{g.uniforms[Yo].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(ee,S=>{const T=s(F);S===void 0&&(S=!T.pointcloudMode),g.uniforms[sa].value=S,g.uniformsNeedUpdate=!0,T.pointcloudMode=S,s(nt),T.viewerEvents&&(T.viewerEvents.fire(F).pointcloudMode=S)}),e(Ti,S=>{g.uniforms[jo].value=S,g.uniformsNeedUpdate=!0;const T=s(F);T.bigSceneMode=S,s(nt)}),e(Me,S=>{g.uniforms[qo].value=S,g.uniformsNeedUpdate=!0;const T=s(F);T.lightFactor=S,s(nt)});let D=!1;return e(Ri,S=>{s(It)||D||(D=!0,g.uniforms[la].value=S,g.uniformsNeedUpdate=!0,s(nt))}),e(Li,(S,T,L)=>{g.uniforms[Aa].value.set(S,T,L),g.uniformsNeedUpdate=!0,s(nt)}),e(Fi,(S,T,L)=>{g.uniforms[ua].value.set(S,T,L),g.uniformsNeedUpdate=!0,s(nt)}),e(ge,S=>{g.uniforms[Ho].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(ve,S=>{g.uniforms[Go].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(fo,S=>{g.uniforms[ea].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(Ze,(S,T,L,_)=>{g.uniforms[ia].value=[S,T,L,_?1:-1],g.uniformsNeedUpdate=!0,s(nt)}),e(ts,(S=!0)=>{g.uniforms[Jo].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(Pi,S=>{g.uniforms[Xo].value=S,g.uniformsNeedUpdate=!0}),e(In,S=>{g.uniforms[Un].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(Ms,(S,T)=>{g.uniforms[Oo].value=S,g.uniforms[zo].value=T,g.uniformsNeedUpdate=!0,s(nt)}),e(vs,S=>{g.uniforms[ra].value=Math.min(Math.max(0,S),255)/255,g.uniformsNeedUpdate=!0,s(nt)}),e(xs,(S=!1)=>{g.uniforms[Ko].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(bn,S=>{g.uniforms[Zo].value=S,g.uniformsNeedUpdate=!0,s(nt)}),e(Ka,S=>{g.uniforms[$o].value=S,g.uniformsNeedUpdate=!0}),e(je,async S=>{if(s(It))return;const T=await s(bs);S<0&&(S=0),S>T&&(S=T),u=S,g.uniforms[oa].value=S,g.uniformsNeedUpdate=!0,s(rt,{shDegree:`${S} / max ${T}`}),s(nt)}),e(ir,(S=0,T=0)=>{g.uniforms[na].value=S<<16|T,g.uniforms[Un].value=performance.now(),g.uniformsNeedUpdate=!0,s(nt)}),e(or,(S=-1)=>{if(S<0)return g.uniforms[Ks].value;g.uniforms[Ks].value=S,g.uniforms[Un].value=performance.now(),g.uniformsNeedUpdate=!0,s(nt)}),e(ns,()=>{g.dispose(),B&&B.dispose(),k&&k.dispose(),G&&G.dispose(),Q&&Q.dispose()},!0),g});function c(C){return He.cmn=qc.trim(),He.FvEffect=(He["custom-FvEffect"]||$c).trim(),He.WatermarkEffect=(He["custom-WatermarkEffect"]||tl).trim(),`#include <cmn>
#include <FvEffect>
#include <WatermarkEffect>
`+C}e(xi,async()=>{const C=new A.Mesh(await s(Mi),await s(vi));return s(Je),s(Ke),s(Ti,s(It)),s(ee,s(_i)),C});function d(){s(Je),s(Ke)}window.addEventListener("resize",d),e(Hi,()=>{t=!0,window.removeEventListener("resize",d),s(es),s(ns)}),e(Bs,()=>{if(!s(ho))return;const C=s(F),I=s(bt)?.meta||{},p=C.qualityLevel||vt.Default5;if(s(je,[0,1,2,3,3,3,3,3,3][p-1]),wt){const g=[4,3,3,2,2,2,1,1,1],v=[7,6,5,4,4,3,2,2,2],B=I.minPixelDiameter||g[p-1],R=I.maxPixelDiameter||(p<vt.L4?128:p>vt.L6?512:256);s(Ms,B,R),s(vs,I.minAlpha||v[p-1]),s(xs,!1)}else{const g=[128,256,256,512,512,1024,1024,1024,1024],v=[5,4,3,2,2,1,1,1,1],B=I.minPixelDiameter||(p<vt.L4?2:1),R=I.maxPixelDiameter||g[p-1];s(Ms,B,R),s(vs,I.minAlpha||v[p-1]),s(xs,p>vt.L6)}s(Ds)}),e(Gi,async()=>{if(s(It))return;const C=s(F);if(C.disableTransitionEffectOnLoad)return s(ge,0);if(C.disableProgressiveLoading){s(ge,0),s(sn,!0);return}let I=Date.now(),p=C.transitionAnimDuration||4e3,h=0,g=!1,v=0;const B=R=>(R=Math.max(0,Math.min(1,R)),R*R*(3-2*R));s(ge,h),s(le,()=>{if(t)return;const R=Date.now(),x=R-I;let k=Math.min(x/p,1);if(s(as)&&!v&&(v=R),v){const G=R-v;k=Math.min(x/p,1),(k>.95||G>2e3)&&(k=1)}h=B(k)*a,s(ge,h),k>=1&&(s(sn,!0),s(ge,0),g=!0)},()=>!t&&!g,3)}),e(sn,(C=!1)=>{if(s(It))return;const I=s(F);if(s(po,I.transitionEffect),I.transitionEffect===_n.ModelCenterCirccle){for(;o.length;)o.pop().stop=!0;const p=Date.now(),h=3e3,g=()=>{const B=s(as),R=s(Yi),x=s(Oi)||0,k=Date.now()-p,M=s(te)||0;I.pointcloudMode,x>=M,!R&&(B||x>0&&k>1e3||k>h)?(k>h?console.warn("等待数据超时，强制启动过渡动画"):!B&&x>0&&console.warn("数据状态检查异常，但有纹理数据，启动过渡动画"),v()):setTimeout(g,50)},v=()=>{const R=Date.now();let x={currentPointMode:I.pointcloudMode,currentLightRadius:0,stop:!1,startTime:R};o.push(x);const k=M=>M;s(le,()=>{if(t)return;const M=Date.now()-x.startTime,P=Math.min(M/1e3,1),G=k(P);x.currentLightRadius=G*i,s(ve,x.currentLightRadius),P>=1&&(s(ee,!x.currentPointMode),s(ve,0),x.stop=!0,o.length===1&&o[0]===x&&o.pop(),s(wn,C))},()=>!t&&!x.stop)};g()}else s(In,performance.now()),s(ve,.1),setTimeout(()=>{s(ee),s(ve,0),s(wn,C)},100)}),e(wn,(C=!1)=>{s(bn,0),C&&s(F).viewerEvents?.fire(De),s(F).viewerEvents?.fire(no)}),e(Si,()=>({[js]:{type:"t",value:null},[qs]:{type:"t",value:null},[Js]:{type:"t",value:null},[Zs]:{type:"t",value:null},[ca]:{type:"v2",value:new A.Vector2},[aa]:{type:"v2",value:new A.Vector2},[ta]:{type:"int",value:0},[Yo]:{type:"int",value:1},[sa]:{type:"bool",value:!1},[$o]:{type:"bool",value:!0},[jo]:{type:"bool",value:!1},[oa]:{type:"int",value:0},[qo]:{type:"float",value:1},[la]:{type:"float",value:0},[Aa]:{type:"v3",value:new A.Vector3},[ua]:{type:"v3",value:new A.Vector3},[Ho]:{type:"float",value:0},[Go]:{type:"float",value:0},[ea]:{type:"float",value:0},[ia]:{type:"v4",value:new A.Vector4(0,0,0,-1)},[Zo]:{type:"int",value:0},[Xo]:{type:"float",value:performance.now()},[Un]:{type:"float",value:0},[Oo]:{type:"float",value:1},[zo]:{type:"float",value:1024},[ra]:{type:"float",value:2/255},[Mc]:{type:"v4",value:new A.Vector4(1,1,0,.5)},[Jo]:{type:"bool",value:!0},[Ko]:{type:"bool",value:!0},[na]:{type:"uint",value:1},[Ks]:{type:"uint",value:0}}));const f=s(hi);f.onmessage=C=>{const I=C.data;if(I[da]){r=I[Kc],l=I[$s];const p=I[da];s(Qi,p,I[ga],I[Tc],I[kc],I[fa]),p.length&&f.postMessage({[xc]:p},[p.buffer])}},e(ws,(C,I,p)=>{s(It)||(a=I,i=p),s(Di,C)});function m(C){const I=C.xyz.slice(0);f.postMessage({[Rc]:!0,[Vc]:I,[zc]:C.watermarkCount,[ga]:C.index,[Bc]:C.version,[fa]:C.renderSplatCount,[Qc]:C.visibleSplatCount,[Dc]:C.modelSplatCount,[_c]:C.minX,[Uc]:C.maxX,[Nc]:C.minY,[Wc]:C.maxY,[Hc]:C.minZ,[Gc]:C.maxZ},[I.buffer])}}function nl(n){const t=(i,...a)=>n.fire(i,...a),e=(i,a,o)=>n.on(i,a,o),s=new Set(wr.split(""));e(to,async(i="",a=!0,o=!0)=>{const r=i.trim().substring(0,100);let l=await t(fi,r),u=[];for(let v=0;v<l.length;v++){let B=[],R=l[v];for(let x=0;x<R.length;x++)B.push([(R[x]%20-10)*.02,((R[x]/20|0)-10)*.02]);u.push(B)}let c=[],d=r.split("");for(let v=0;v<d.length;v++)c[v]=s.has(d[v])?.22:.4;let f=d.length/2|0,m=c[f]/2,C=!(d.length%2),I=C?0:-m;for(let v=f-1;v>=0;v--){I-=c[v]/2;for(let B of u[v])B[0]+=I;I-=c[v]/2}m=c[f]/2,I=C?0:m;for(let v=u.length-f;v<u.length;v++){I+=c[v]/2;for(let B of u[v])B[0]+=I;I+=c[v]/2}let p=0;for(let v of u)p+=v.length;const h=new Uint8Array(p*O);let g=0;for(let v of u)for(let B of v)h.set(await fc(B[0],B[1],a,o),O*g++);return h})}function ti(n){const t=(e,s,i)=>n.on(e,s,i);t(Xt,e=>{e.url;const s={...e};delete s.url;const i=JSON.stringify(s,null,2);console.info(i)}),t(fi,(e="")=>{const s="https://reall3d.com/gsfont/api/getGaussianText",i=new FormData;return i.append("text",e.substring(0,100)),i.append("ver",Tn),new Promise(a=>{fetch(s,{method:"POST",body:i}).then(o=>o.ok?o.json():{}).then(o=>o.success?a(JSON.parse(o.data)):a([])).catch(o=>a([]))})})}const sl=/^[og]\s*(.+)?/,il=/^mtllib /,ol=/^usemtl /,al=/^usemap /,Ca=/\s+/,ya=new A.Vector3,ei=new A.Vector3,Ia=new A.Vector3,wa=new A.Vector3,Rt=new A.Vector3,Nn=new A.Color;function rl(){const n={objects:[],object:{},vertices:[],normals:[],colors:[],uvs:[],materials:{},materialLibraries:[],startObject:function(t,e){if(this.object&&this.object.fromDeclaration===!1){this.object.name=t,this.object.fromDeclaration=e!==!1;return}const s=this.object&&typeof this.object.currentMaterial=="function"?this.object.currentMaterial():void 0;if(this.object&&typeof this.object._finalize=="function"&&this.object._finalize(!0),this.object={name:t||"",fromDeclaration:e!==!1,geometry:{vertices:[],normals:[],colors:[],uvs:[],hasUVIndices:!1},materials:[],smooth:!0,startMaterial:function(i,a){const o=this._finalize(!1);o&&(o.inherited||o.groupCount<=0)&&this.materials.splice(o.index,1);const r={index:this.materials.length,name:i||"",mtllib:Array.isArray(a)&&a.length>0?a[a.length-1]:"",smooth:o!==void 0?o.smooth:this.smooth,groupStart:o!==void 0?o.groupEnd:0,groupEnd:-1,groupCount:-1,inherited:!1,clone:function(l){const u={index:typeof l=="number"?l:this.index,name:this.name,mtllib:this.mtllib,smooth:this.smooth,groupStart:0,groupEnd:-1,groupCount:-1,inherited:!1};return u.clone=this.clone.bind(u),u}};return this.materials.push(r),r},currentMaterial:function(){if(this.materials.length>0)return this.materials[this.materials.length-1]},_finalize:function(i){const a=this.currentMaterial();if(a&&a.groupEnd===-1&&(a.groupEnd=this.geometry.vertices.length/3,a.groupCount=a.groupEnd-a.groupStart,a.inherited=!1),i&&this.materials.length>1)for(let o=this.materials.length-1;o>=0;o--)this.materials[o].groupCount<=0&&this.materials.splice(o,1);return i&&this.materials.length===0&&this.materials.push({name:"",smooth:this.smooth}),a}},s&&s.name&&typeof s.clone=="function"){const i=s.clone(0);i.inherited=!0,this.object.materials.push(i)}this.objects.push(this.object)},finalize:function(){this.object&&typeof this.object._finalize=="function"&&this.object._finalize(!0)},parseVertexIndex:function(t,e){const s=parseInt(t,10);return(s>=0?s-1:s+e/3)*3},parseNormalIndex:function(t,e){const s=parseInt(t,10);return(s>=0?s-1:s+e/3)*3},parseUVIndex:function(t,e){const s=parseInt(t,10);return(s>=0?s-1:s+e/2)*2},addVertex:function(t,e,s){const i=this.vertices,a=this.object.geometry.vertices;a.push(i[t+0],i[t+1],i[t+2]),a.push(i[e+0],i[e+1],i[e+2]),a.push(i[s+0],i[s+1],i[s+2])},addVertexPoint:function(t){const e=this.vertices;this.object.geometry.vertices.push(e[t+0],e[t+1],e[t+2])},addVertexLine:function(t){const e=this.vertices;this.object.geometry.vertices.push(e[t+0],e[t+1],e[t+2])},addNormal:function(t,e,s){const i=this.normals,a=this.object.geometry.normals;a.push(i[t+0],i[t+1],i[t+2]),a.push(i[e+0],i[e+1],i[e+2]),a.push(i[s+0],i[s+1],i[s+2])},addFaceNormal:function(t,e,s){const i=this.vertices,a=this.object.geometry.normals;ya.fromArray(i,t),ei.fromArray(i,e),Ia.fromArray(i,s),Rt.subVectors(Ia,ei),wa.subVectors(ya,ei),Rt.cross(wa),Rt.normalize(),a.push(Rt.x,Rt.y,Rt.z),a.push(Rt.x,Rt.y,Rt.z),a.push(Rt.x,Rt.y,Rt.z)},addColor:function(t,e,s){const i=this.colors,a=this.object.geometry.colors;i[t]!==void 0&&a.push(i[t+0],i[t+1],i[t+2]),i[e]!==void 0&&a.push(i[e+0],i[e+1],i[e+2]),i[s]!==void 0&&a.push(i[s+0],i[s+1],i[s+2])},addUV:function(t,e,s){const i=this.uvs,a=this.object.geometry.uvs;a.push(i[t+0],i[t+1]),a.push(i[e+0],i[e+1]),a.push(i[s+0],i[s+1])},addDefaultUV:function(){const t=this.object.geometry.uvs;t.push(0,0),t.push(0,0),t.push(0,0)},addUVLine:function(t){const e=this.uvs;this.object.geometry.uvs.push(e[t+0],e[t+1])},addFace:function(t,e,s,i,a,o,r,l,u){const c=this.vertices.length;let d=this.parseVertexIndex(t,c),f=this.parseVertexIndex(e,c),m=this.parseVertexIndex(s,c);if(this.addVertex(d,f,m),this.addColor(d,f,m),r!==void 0&&r!==""){const C=this.normals.length;d=this.parseNormalIndex(r,C),f=this.parseNormalIndex(l,C),m=this.parseNormalIndex(u,C),this.addNormal(d,f,m)}else this.addFaceNormal(d,f,m);if(i!==void 0&&i!==""){const C=this.uvs.length;d=this.parseUVIndex(i,C),f=this.parseUVIndex(a,C),m=this.parseUVIndex(o,C),this.addUV(d,f,m),this.object.geometry.hasUVIndices=!0}else this.addDefaultUV()},addPointGeometry:function(t){this.object.geometry.type="Points";const e=this.vertices.length;for(let s=0,i=t.length;s<i;s++){const a=this.parseVertexIndex(t[s],e);this.addVertexPoint(a),this.addColor(a)}},addLineGeometry:function(t,e){this.object.geometry.type="Line";const s=this.vertices.length,i=this.uvs.length;for(let a=0,o=t.length;a<o;a++)this.addVertexLine(this.parseVertexIndex(t[a],s));for(let a=0,o=e.length;a<o;a++)this.addUVLine(this.parseUVIndex(e[a],i))}};return n.startObject("",!1),n}class cl extends A.Loader{constructor(t){super(t),this.materials=null}load(t,e,s,i){const a=this,o=new A.FileLoader(this.manager);o.setPath(this.path),o.setRequestHeader(this.requestHeader),o.setWithCredentials(this.withCredentials),o.load(t,function(r){try{e(a.parse(r))}catch(l){i?i(l):console.error(l),a.manager.itemError(t)}},s,i)}setMaterials(t){return this.materials=t,this}parse(t){const e=new rl;t.indexOf(`\r
`)!==-1&&(t=t.replace(/\r\n/g,`
`)),t.indexOf(`\\
`)!==-1&&(t=t.replace(/\\\n/g,""));const s=t.split(`
`);let i=[];for(let r=0,l=s.length;r<l;r++){const u=s[r].trimStart();if(u.length===0)continue;const c=u.charAt(0);if(c!=="#")if(c==="v"){const d=u.split(Ca);switch(d[0]){case"v":e.vertices.push(parseFloat(d[1]),parseFloat(d[2]),parseFloat(d[3])),d.length>=7?(Nn.setRGB(parseFloat(d[4]),parseFloat(d[5]),parseFloat(d[6]),A.SRGBColorSpace),e.colors.push(Nn.r,Nn.g,Nn.b)):e.colors.push(void 0,void 0,void 0);break;case"vn":e.normals.push(parseFloat(d[1]),parseFloat(d[2]),parseFloat(d[3]));break;case"vt":e.uvs.push(parseFloat(d[1]),parseFloat(d[2]));break}}else if(c==="f"){const f=u.slice(1).trim().split(Ca),m=[];for(let I=0,p=f.length;I<p;I++){const h=f[I];if(h.length>0){const g=h.split("/");m.push(g)}}const C=m[0];for(let I=1,p=m.length-1;I<p;I++){const h=m[I],g=m[I+1];e.addFace(C[0],h[0],g[0],C[1],h[1],g[1],C[2],h[2],g[2])}}else if(c==="l"){const d=u.substring(1).trim().split(" ");let f=[];const m=[];if(u.indexOf("/")===-1)f=d;else for(let C=0,I=d.length;C<I;C++){const p=d[C].split("/");p[0]!==""&&f.push(p[0]),p[1]!==""&&m.push(p[1])}e.addLineGeometry(f,m)}else if(c==="p"){const f=u.slice(1).trim().split(" ");e.addPointGeometry(f)}else if((i=sl.exec(u))!==null){const d=(" "+i[0].slice(1).trim()).slice(1);e.startObject(d)}else if(ol.test(u))e.object.startMaterial(u.substring(7).trim(),e.materialLibraries);else if(il.test(u))e.materialLibraries.push(u.substring(7).trim());else if(al.test(u))console.warn('THREE.OBJLoader: Rendering identifier "usemap" not supported. Textures must be defined in MTL files.');else if(c==="s"){if(i=u.split(" "),i.length>1){const f=i[1].trim().toLowerCase();e.object.smooth=f!=="0"&&f!=="off"}else e.object.smooth=!0;const d=e.object.currentMaterial();d&&(d.smooth=e.object.smooth)}else{if(u==="\0")continue;console.warn('THREE.OBJLoader: Unexpected line: "'+u+'"')}}e.finalize();const a=new A.Group;if(a.materialLibraries=[].concat(e.materialLibraries),!(e.objects.length===1&&e.objects[0].geometry.vertices.length===0)===!0)for(let r=0,l=e.objects.length;r<l;r++){const u=e.objects[r],c=u.geometry,d=u.materials,f=c.type==="Line",m=c.type==="Points";let C=!1;if(c.vertices.length===0)continue;const I=new A.BufferGeometry;I.setAttribute("position",new A.Float32BufferAttribute(c.vertices,3)),c.normals.length>0&&I.setAttribute("normal",new A.Float32BufferAttribute(c.normals,3)),c.colors.length>0&&(C=!0,I.setAttribute("color",new A.Float32BufferAttribute(c.colors,3))),c.hasUVIndices===!0&&I.setAttribute("uv",new A.Float32BufferAttribute(c.uvs,2));const p=[];for(let g=0,v=d.length;g<v;g++){const B=d[g],R=B.name+"_"+B.smooth+"_"+C;let x=e.materials[R];if(this.materials!==null){if(x=this.materials.create(B.name),f&&x&&!(x instanceof A.LineBasicMaterial)){const k=new A.LineBasicMaterial;A.Material.prototype.copy.call(k,x),k.color.copy(x.color),x=k}else if(m&&x&&!(x instanceof A.PointsMaterial)){const k=new A.PointsMaterial({size:10,sizeAttenuation:!1});A.Material.prototype.copy.call(k,x),k.color.copy(x.color),k.map=x.map,x=k}}x===void 0&&(f?x=new A.LineBasicMaterial:m?x=new A.PointsMaterial({size:1,sizeAttenuation:!1}):x=new A.MeshPhongMaterial,x.name=B.name,x.flatShading=!B.smooth,x.vertexColors=C,e.materials[R]=x),p.push(x)}let h;if(p.length>1){for(let g=0,v=d.length;g<v;g++){const B=d[g];I.addGroup(B.groupStart,B.groupCount,g)}f?h=new A.LineSegments(I,p):m?h=new A.Points(I,p):h=new A.Mesh(I,p)}else f?h=new A.LineSegments(I,p[0]):m?h=new A.Points(I,p[0]):h=new A.Mesh(I,p[0]);h.name=u.name,a.add(h)}else if(e.vertices.length>0){const r=new A.PointsMaterial({size:1,sizeAttenuation:!1}),l=new A.BufferGeometry;l.setAttribute("position",new A.Float32BufferAttribute(e.vertices,3)),e.colors.length>0&&e.colors[0]!==void 0&&(l.setAttribute("color",new A.Float32BufferAttribute(e.colors,3)),r.vertexColors=!0);const u=new A.Points(l,r);a.add(u)}return a}}function ll(n){let t=!1;const e=(r,l,u)=>n.on(r,l,u),s=(r,...l)=>n.fire(r,...l);e(Vi,()=>t=!0);const i=new Map,a=new Map;e(qe,()=>s(Mt)&&i.set(Date.now(),1)),e($e,()=>s(Mt)&&s(de,i)),e(tn,()=>s(Mt)&&a.set(Date.now(),1)),e(en,()=>s(Mt)&&s(de,a));let o=0;e(fe,()=>{t||(s(Ci),s(Mt)&&(s(qe),!(o++%5)&&s(rt,{fps:s($e),realFps:s(en),fov:s(Se),position:s(se,s(Lt)),lookAt:s(se,s(_t)),lookUp:s(se,s(ue))})))},!0),e(de,r=>{let l=[],u=Date.now(),c=0;for(const d of r.keys())u-d<=1e3?c++:l.push(d);return l.forEach(d=>r.delete(d)),c}),window.addEventListener("beforeunload",()=>s(nn)),e(uo,async r=>{s(rt,{scene:"small (obj)"});const l=await Kt(r,n);if(l){const u=URL.createObjectURL(new Blob([l],{type:"application/octet-stream"}));new cl().load(u,c=>s(z).add(c)),s(Z,!0),s(qt,!0)}s(Be,0)})}function Al(n){const t={...n};return t.bigSceneMode??(t.bigSceneMode=!1),t.pointcloudMode??(t.pointcloudMode=!t.bigSceneMode),t.lightFactor??(t.lightFactor=1),t.name??(t.name=""),t.showWatermark??(t.showWatermark=!1),t.shDegree??(t.shDegree=0),t.depthTest??(t.depthTest=!0),t.debugMode??(t.debugMode=!1),t.transitionAnimDuration??(t.transitionAnimDuration=4e3),t.maxRenderCountOfMobile??(t.maxRenderCountOfMobile=t.bigSceneMode?256*1e4:384*10240),t.maxRenderCountOfPc??(t.maxRenderCountOfPc=t.bigSceneMode?320*1e4:384*1e4),t}function ba(n){const t={...n};return n.enablePan,n.minDistance,n.maxDistance,t.position=t.position?[...t.position]:[0,-5,15],t.lookAt=t.lookAt?[...t.lookAt]:[0,0,0],t.lookUp=t.lookUp?[...t.lookUp]:[0,-1,0],t.fov??(t.fov=45),t.near??(t.near=.1),t.far??(t.far=1e3),t.enableDamping??(t.enableDamping=!0),t.autoRotate??(t.autoRotate=!0),t.enableZoom??(t.enableZoom=!0),t.enableRotate??(t.enableRotate=!0),t.enablePan??(t.enablePan=!0),t.enableKeyboard??(t.enableKeyboard=!0),t.panDirection??(t.panDirection="both"),t.enablePan,t.panDirection,t.minPan,t.maxPan,t.minDistance,t.maxDistance,t.bigSceneMode??(t.bigSceneMode=!1),t.pointcloudMode??(t.pointcloudMode=!t.bigSceneMode),t.lightFactor??(t.lightFactor=1.1),t.debugMode??(t.debugMode=location.protocol==="http:"||/^test\./.test(location.host)),t.markMode??(t.markMode=!1),t.markVisible??(t.markVisible=!0),t.meterScale??(t.meterScale=1),t.background??(t.background="#000000"),t.minDistance??(t.minDistance=.1),t.maxDistance??(t.maxDistance=1e3),t.minPolarAngle??(t.minPolarAngle=0),t.maxPolarAngle??(t.maxPolarAngle=Math.PI/2),t.qualityLevel??(t.qualityLevel=vt.Default5),t.sortType??(t.sortType=Ue.Default1),t.transitionEffect??(t.transitionEffect=_n.ModelCenterCirccle),t}function ul(n){let t;n.root?t=typeof n.root=="string"?document.querySelector(n.root)||document.querySelector("#gsviewer"):n.root:t=document.querySelector("#gsviewer"),t||(t=document.createElement("div"),t.id="gsviewer",document.body.appendChild(t));let e=null;return n.renderer?e=n.renderer:(e=new A.WebGLRenderer({antialias:!1,stencil:!1,logarithmicDepthBuffer:!0,premultipliedAlpha:!1,precision:"highp",powerPreference:"high-performance"}),e.setSize(t.clientWidth,t.clientHeight),e.setPixelRatio(Math.min(devicePixelRatio,2)),n.renderer=e),e.domElement.classList.add("gsviewer-canvas"),t.appendChild(e.domElement),e}function dl(n){let t=n.camera;if(!t){const e=n.renderer.domElement,s=e.width/e.height;let i=new A.Vector3().fromArray(n.lookUp),a=new A.Vector3().fromArray(n.lookAt),o=new A.Vector3().fromArray(n.position);t=new A.PerspectiveCamera(n.fov,s,n.near,n.far),t.position.copy(o),t.up.copy(i).normalize(),t.lookAt(a),n.camera=t}return n.camera}function gl(n){const{renderer:t,scene:e}=n,s={renderer:t,scene:e};return s.viewerEvents=n.viewerEvents,s.debugMode=n.debugMode,s.renderer=n.renderer,s.scene=n.scene,s.controls=n.controls,s.bigSceneMode=n.bigSceneMode,s.pointcloudMode=n.pointcloudMode,s.maxRenderCountOfMobile=n.maxRenderCountOfMobile,s.maxRenderCountOfPc=n.maxRenderCountOfPc,s.lightFactor=n.lightFactor,s.shDegree=n.shDegree,s.qualityLevel=n.qualityLevel,s.sortType=n.sortType,s.disableTransitionEffectOnLoad=!!n.disableTransitionEffectOnLoad,s.transitionEffect=n.transitionEffect||_n.ModelCenterCirccle,s.transitionAnimDuration=n.transitionAnimDuration,s.disableProgressiveLoading=n.disableProgressiveLoading,s.disableStreamLoading=n.disableStreamLoading,s.onPerformanceUpdate=n.onPerformanceUpdate,s}function fl(n){const t=(o,r,l)=>n.on(o,r,l),e=(o,...r)=>n.fire(o,...r),s="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",i=URL.createObjectURL(new Blob([atob(s)],{type:"text/javascript"})),a=new Worker(new URL(i,typeof document>"u"&&typeof location>"u"?require("url").pathToFileURL(__filename).href:typeof document>"u"?location.href:Xn&&Xn.tagName.toUpperCase()==="SCRIPT"&&Xn.src||new URL("pkg.umd.cjs",document.baseURI).href),{type:"module"});t(hi,()=>a),t(bi,()=>a.postMessage({[Pc]:e(Ii),[Oc]:e(Ji),[Xc]:e(Lt).toArray()})),t(Ei,()=>a.terminate()),t(Ds,()=>a.postMessage({[Jc]:!0,[pa]:e(Ls),[$s]:e(Fs),[ha]:e(bt)?.meta?.depthNearRate,[ma]:e(bt)?.meta?.depthNearValue})),(async()=>a.postMessage({[Yc]:!0,[Fc]:await e(te),[Lc]:e(It),[pa]:e(Ls),[$s]:e(Fs),[ha]:e(bt)?.meta?.depthNearRate,[ma]:e(bt)?.meta?.depthNearValue}))()}class pl extends A.Object3D{constructor(t=0,e=0,s=0,i=0,a=0,o=0){super();const r=this,l=new A.BufferGeometry,u=new A.LineBasicMaterial({color:"#ffffff"});r.boxLines=new A.LineSegments(l,u),r.update(t,e,s,i,a,o),r.add(r.boxLines)}update(t,e,s,i,a,o,r){const l=(i-t)/8,u=(a-e)/8,c=(o-s)/8,d=[new A.Vector3(t,e,s),new A.Vector3(i,e,s),new A.Vector3(i,a,s),new A.Vector3(t,a,s),new A.Vector3(t,e,o),new A.Vector3(i,e,o),new A.Vector3(i,a,o),new A.Vector3(t,a,o)],f=[];d.forEach(m=>{f.push(m.x,m.y,m.z),f.push(m.x+(m.x<i?l:-l),m.y,m.z),f.push(m.x,m.y,m.z),f.push(m.x,m.y+(m.y<a?u:-u),m.z),f.push(m.x,m.y,m.z),f.push(m.x,m.y,m.z+(m.z<o?c:-c))}),this.boxLines.geometry.setAttribute("position",new A.Float32BufferAttribute(f,3)),r&&(this.visible=!0)}dispose(){this.boxLines=null}}class Bt extends A.Mesh{constructor(t){super(),this.isSplatMesh=!0,this.disposed=!1;const e=this,s=new kn,i=(c,d,f)=>s.on(c,d,f),a=(c,...d)=>s.fire(c,...d);let o=!1;const r=Al(t),l=r.camera||r.controls?.object;i(F,()=>r),i(Ae,()=>r.renderer.domElement),i(q,()=>l),i(Se,()=>l.fov),i(Lt,(c=!1)=>c?l.position.clone():l.position),i(_t,(c=!1)=>r.controls?c?r.controls.target.clone():r.controls.target:new A.Vector3),i(Ii,()=>l.projectionMatrix.clone().multiply(l.matrixWorldInverse).multiply(e.matrix).toArray()),i(wi,()=>l.projectionMatrix.clone().multiply(l.matrixWorldInverse)),i(Ji,()=>l.getWorldDirection(new A.Vector3).toArray()),i(xe,()=>r.renderer),i(z,()=>r.scene),i(It,()=>r.bigSceneMode),i(_i,()=>r.pointcloudMode),i(bt,()=>e),i(Ls,()=>r.qualityLevel||vt.Default5),i(Fs,()=>r.sortType||Ue.Default1),i(ho,()=>o),i(nt,()=>r.viewerEvents?.fire(Z)),Gs(s),ti(s),Sc(s),fl(s),el(s),nl(s),e.name=`${r.name||e.id}`,e.events=s,e.opts=r,(async()=>(e.copy(await s.fire(xi)),e.meta.transform&&e.applyMatrix4(new A.Matrix4().fromArray(e.meta.transform)),e.frustumCulled=!1,e.onBeforeRender=()=>{a(bi),a(Pi,performance.now())},e.onAfterRender=()=>{a(Ni,1e4)&&a(nt)},o=!0,a(Bs)))();const u=new pl;u.visible=!1,u.renderOrder=99999,e.boundBox=u,e.add(u),i(Ss,(c,d,f,m,C,I,p)=>{u.update(c,d,f,m,C,I,p)}),i(ar,(c=!0)=>u.visible=c)}options(t){const e=this;if(e.disposed)return;const s=(a,...o)=>e.events.fire(a,...o),i=e.opts;return t&&(t.pointcloudMode!==void 0&&s(ee,t.pointcloudMode),t.lightFactor!==void 0&&s(Me,t.lightFactor),t.maxRenderCountOfMobile!==void 0&&(i.maxRenderCountOfMobile=t.maxRenderCountOfMobile),t.maxRenderCountOfPc!==void 0&&(i.maxRenderCountOfPc=t.maxRenderCountOfPc),t.qualityLevel!==void 0&&(i.qualityLevel=t.qualityLevel)&&s(Bs),!i.mapMode&&t.sortType!==void 0&&(i.sortType=t.sortType)&&s(Ds),s(nt)),{...i}}addModel(t,e={}){const s=this;s.disposed||(s.meta=e,s.events.fire(Ui,t,e))}fire(t,...e){const s=this;if(!s.disposed)return s.events.fire(t,...e)}dispose(){const t=this;if(t.disposed)return;t.disposed=!0;const e=(s,...i)=>t.events.fire(s,...i);e(Jt,t),e(Jt,t.boundBox),e(z).remove(t),e(z).remove(t.boundBox),e(ss),e(Wi),e(Ei),e(Hi),t.events.clear(),t.events=null,t.opts=null,t.onAfterRender=null,t.boundBox=null}}class hl extends A.Object3D{constructor(t=new A.Vector3(0,0,1),e=new A.Vector3(0,0,0),s=1,i=.1,a=16776960,o=s*.2,r=o*.2){super(),this._axis=new A.Vector3,this.type="ArrowHelper";const l=new A.CylinderGeometry(i,i,s,32);l.translate(0,s/2,0);const u=new A.CylinderGeometry(0,r,o,32);u.translate(0,s,0),this.position.copy(e);const c=new A.MeshBasicMaterial({color:a,toneMapped:!1});c.side=A.DoubleSide,this.line=new A.Mesh(l,c),this.line.matrixAutoUpdate=!1,this.line.ignoreIntersect=!0,this.add(this.line);const d=new A.MeshBasicMaterial({color:a,toneMapped:!1});d.side=A.DoubleSide,this.cone=new A.Mesh(u,d),this.cone.matrixAutoUpdate=!1,this.cone.ignoreIntersect=!0,this.add(this.cone),this.setDirection(t),this.renderOrder=99999}setDirection(t){if(t.y>.99999)this.quaternion.set(0,0,0,1);else if(t.y<-.99999)this.quaternion.set(1,0,0,0);else{this._axis.set(t.z,0,-t.x).normalize();const e=Math.acos(t.y);this.quaternion.setFromAxisAngle(this._axis,e)}}setColor(t){this.line.material.color.set(t),this.cone.material.color.set(t)}copy(t){return super.copy(t,!1),this.line.copy(t.line),this.cone.copy(t.cone),this}dispose(){this.line.geometry.dispose(),this.line.material.dispose(),this.cone.geometry.dispose(),this.cone.material.dispose()}}function ml(n){const t=(I,p,h)=>n.on(I,p,h),e=(I,...p)=>n.fire(I,...p),s=new A.PlaneGeometry(1,1);s.rotateX(-Math.PI/2);const i=new A.MeshBasicMaterial({color:16777215});i.transparent=!0,i.opacity=.6,i.depthTest=!1,i.depthWrite=!1,i.side=A.DoubleSide;const a=new A.Mesh(s,i);a.ignoreIntersect=!0;const o=new A.Vector3(0,-1,0);o.normalize();const r=new A.Vector3(0,0,0),l=.5,u=.01,c=16777062,d=.1,f=.03,m=new hl(o,r,l,u,c,d,f),C=new A.Object3D;C.add(a),C.add(m),C.renderOrder=99999,a.renderOrder=99999,C.visible=!1,e(z).add(C),t(ja,()=>C),t(An,I=>{e(un,!0),C.visible=I===void 0?!C.visible:I,e(Z)}),t(ps,()=>C.visible),t(un,(I=!1)=>{if(I||C.visible){const p=new A.Quaternion,h=new A.Vector3(0,-1,0);p.setFromUnitVectors(h,e(ue)),C.position.copy(e(_t)),C.quaternion.copy(p)}})}const Ea={type:"change"},ni={type:"start"},Sa={type:"end"},Wn=new A.Ray,Ma=new A.Plane,Cl=Math.cos(70*A.MathUtils.DEG2RAD),At=new A.Vector3,St=2*Math.PI,st={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},si=1e-6;class va extends A.Controls{constructor(t,e=null){super(t,e),this.state=st.NONE,this.enabled=!0,this.target=new A.Vector3,this.cursor=new A.Vector3,this.minDistance=0,this.maxDistance=1/0,this.minZoom=0,this.maxZoom=1/0,this.minTargetRadius=0,this.maxTargetRadius=1/0,this.minPolarAngle=0,this.maxPolarAngle=Math.PI,this.minAzimuthAngle=-1/0,this.maxAzimuthAngle=1/0,this.enableDamping=!1,this.dampingFactor=.05,this.enableZoom=!0,this.zoomSpeed=1,this.enableRotate=!0,this.rotateSpeed=1,this.enablePan=!0,this.panSpeed=1,this.screenSpacePanning=!0,this.keyPanSpeed=7,this.zoomToCursor=!1,this.autoRotate=!1,this.autoRotateSpeed=2,this.keys={LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"},this.mouseButtons={LEFT:A.MOUSE.ROTATE,MIDDLE:A.MOUSE.DOLLY,RIGHT:A.MOUSE.PAN},this.touches={ONE:A.TOUCH.ROTATE,TWO:A.TOUCH.DOLLY_PAN},this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this._domElementKeyEvents=null,this._lastPosition=new A.Vector3,this._lastQuaternion=new A.Quaternion,this._lastTargetPosition=new A.Vector3,this._quat=new A.Quaternion().setFromUnitVectors(t.up,new A.Vector3(0,1,0)),this._quatInverse=this._quat.clone().invert(),this._spherical=new A.Spherical,this._sphericalDelta=new A.Spherical,this._scale=1,this._panOffset=new A.Vector3,this._rotateStart=new A.Vector2,this._rotateEnd=new A.Vector2,this._rotateDelta=new A.Vector2,this._panStart=new A.Vector2,this._panEnd=new A.Vector2,this._panDelta=new A.Vector2,this._dollyStart=new A.Vector2,this._dollyEnd=new A.Vector2,this._dollyDelta=new A.Vector2,this._dollyDirection=new A.Vector3,this._mouse=new A.Vector2,this._performCursorZoom=!1,this._pointers=[],this._pointerPositions={},this._controlActive=!1,this._onPointerMove=Il.bind(this),this._onPointerDown=yl.bind(this),this._onPointerUp=wl.bind(this),this._onContextMenu=Bl.bind(this),this._onMouseWheel=Sl.bind(this),this._onKeyDown=Ml.bind(this),this._onTouchStart=vl.bind(this),this._onTouchMove=xl.bind(this),this._onMouseDown=bl.bind(this),this._onMouseMove=El.bind(this),this._interceptControlDown=Ql.bind(this),this._interceptControlUp=Dl.bind(this),this.domElement!==null&&this.connect(),this.update()}connect(){this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointercancel",this._onPointerUp),this.domElement.addEventListener("contextmenu",this._onContextMenu),this.domElement.addEventListener("wheel",this._onMouseWheel,{passive:!1}),this.domElement.getRootNode().addEventListener("keydown",this._interceptControlDown,{passive:!0,capture:!0}),this.domElement.style.touchAction="none"}disconnect(){this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.domElement.removeEventListener("pointercancel",this._onPointerUp),this.domElement.removeEventListener("wheel",this._onMouseWheel),this.domElement.removeEventListener("contextmenu",this._onContextMenu),this.stopListenToKeyEvents(),this.domElement.getRootNode().removeEventListener("keydown",this._interceptControlDown,{capture:!0}),this.domElement.style.touchAction="auto"}dispose(){this.disconnect()}getPolarAngle(){return this._spherical.phi}getAzimuthalAngle(){return this._spherical.theta}getDistance(){return this.object.position.distanceTo(this.target)}listenToKeyEvents(t){t.addEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=t}stopListenToKeyEvents(){this._domElementKeyEvents!==null&&(this._domElementKeyEvents.removeEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=null)}saveState(){this.target0.copy(this.target),this.position0.copy(this.object.position),this.zoom0=this.object.zoom}reset(){this.target.copy(this.target0),this.object.position.copy(this.position0),this.object.zoom=this.zoom0,this.object.updateProjectionMatrix(),this.dispatchEvent(Ea),this.update(),this.state=st.NONE}update(t=null){const e=this.object.position;At.copy(e).sub(this.target),At.applyQuaternion(this._quat),this._spherical.setFromVector3(At),this.autoRotate&&this.state===st.NONE&&this._rotateLeft(this._getAutoRotationAngle(t)),this.enableDamping?(this._spherical.theta+=this._sphericalDelta.theta*this.dampingFactor,this._spherical.phi+=this._sphericalDelta.phi*this.dampingFactor):(this._spherical.theta+=this._sphericalDelta.theta,this._spherical.phi+=this._sphericalDelta.phi);let s=this.minAzimuthAngle,i=this.maxAzimuthAngle;isFinite(s)&&isFinite(i)&&(s<-Math.PI?s+=St:s>Math.PI&&(s-=St),i<-Math.PI?i+=St:i>Math.PI&&(i-=St),s<=i?this._spherical.theta=Math.max(s,Math.min(i,this._spherical.theta)):this._spherical.theta=this._spherical.theta>(s+i)/2?Math.max(s,this._spherical.theta):Math.min(i,this._spherical.theta)),this._spherical.phi=Math.max(this.minPolarAngle,Math.min(this.maxPolarAngle,this._spherical.phi)),this._spherical.makeSafe(),this.enableDamping===!0?this.target.addScaledVector(this._panOffset,this.dampingFactor):this.target.add(this._panOffset),this.target.sub(this.cursor),this.target.clampLength(this.minTargetRadius,this.maxTargetRadius),this.target.add(this.cursor);let a=!1;if(this.zoomToCursor&&this._performCursorZoom||this.object.isOrthographicCamera)this._spherical.radius=this._clampDistance(this._spherical.radius);else{const o=this._spherical.radius;this._spherical.radius=this._clampDistance(this._spherical.radius*this._scale),a=o!=this._spherical.radius}if(At.setFromSpherical(this._spherical),At.applyQuaternion(this._quatInverse),e.copy(this.target).add(At),this.object.lookAt(this.target),this.enableDamping===!0?(this._sphericalDelta.theta*=1-this.dampingFactor,this._sphericalDelta.phi*=1-this.dampingFactor,this._panOffset.multiplyScalar(1-this.dampingFactor)):(this._sphericalDelta.set(0,0,0),this._panOffset.set(0,0,0)),this.zoomToCursor&&this._performCursorZoom){let o=null;if(this.object.isPerspectiveCamera){const r=At.length();o=this._clampDistance(r*this._scale);const l=r-o;this.object.position.addScaledVector(this._dollyDirection,l),this.object.updateMatrixWorld(),a=!!l}else if(this.object.isOrthographicCamera){const r=new A.Vector3(this._mouse.x,this._mouse.y,0);r.unproject(this.object);const l=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),this.object.updateProjectionMatrix(),a=l!==this.object.zoom;const u=new A.Vector3(this._mouse.x,this._mouse.y,0);u.unproject(this.object),this.object.position.sub(u).add(r),this.object.updateMatrixWorld(),o=At.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),this.zoomToCursor=!1;o!==null&&(this.screenSpacePanning?this.target.set(0,0,-1).transformDirection(this.object.matrix).multiplyScalar(o).add(this.object.position):(Wn.origin.copy(this.object.position),Wn.direction.set(0,0,-1).transformDirection(this.object.matrix),Math.abs(this.object.up.dot(Wn.direction))<Cl?this.object.lookAt(this.target):(Ma.setFromNormalAndCoplanarPoint(this.object.up,this.target),Wn.intersectPlane(Ma,this.target))))}else if(this.object.isOrthographicCamera){const o=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),o!==this.object.zoom&&(this.object.updateProjectionMatrix(),a=!0)}return this._scale=1,this._performCursorZoom=!1,a||this._lastPosition.distanceToSquared(this.object.position)>si||8*(1-this._lastQuaternion.dot(this.object.quaternion))>si||this._lastTargetPosition.distanceToSquared(this.target)>si?(this.dispatchEvent(Ea),this._lastPosition.copy(this.object.position),this._lastQuaternion.copy(this.object.quaternion),this._lastTargetPosition.copy(this.target),!0):!1}_getAutoRotationAngle(t){return t!==null?St/60*this.autoRotateSpeed*t:St/60/60*this.autoRotateSpeed}_getZoomScale(t){const e=Math.abs(t*.01);return Math.pow(.95,this.zoomSpeed*e)}_rotateLeft(t){this._sphericalDelta.theta-=t}_rotateUp(t){this._sphericalDelta.phi-=t}_panLeft(t,e){At.setFromMatrixColumn(e,0),At.multiplyScalar(-t),this._panOffset.add(At)}_panUp(t,e){this.screenSpacePanning===!0?At.setFromMatrixColumn(e,1):(At.setFromMatrixColumn(e,0),At.crossVectors(this.object.up,At)),At.multiplyScalar(t),this._panOffset.add(At)}_pan(t,e){const s=this.domElement;if(this.object.isPerspectiveCamera){const i=this.object.position;At.copy(i).sub(this.target);let a=At.length();a*=Math.tan(this.object.fov/2*Math.PI/180),this._panLeft(2*t*a/s.clientHeight,this.object.matrix),this._panUp(2*e*a/s.clientHeight,this.object.matrix)}else this.object.isOrthographicCamera?(this._panLeft(t*(this.object.right-this.object.left)/this.object.zoom/s.clientWidth,this.object.matrix),this._panUp(e*(this.object.top-this.object.bottom)/this.object.zoom/s.clientHeight,this.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),this.enablePan=!1)}_dollyOut(t){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale/=t:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_dollyIn(t){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale*=t:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_updateZoomParameters(t,e){if(!this.zoomToCursor)return;this._performCursorZoom=!0;const s=this.domElement.getBoundingClientRect(),i=t-s.left,a=e-s.top,o=s.width,r=s.height;this._mouse.x=i/o*2-1,this._mouse.y=-(a/r)*2+1,this._dollyDirection.set(this._mouse.x,this._mouse.y,1).unproject(this.object).sub(this.object.position).normalize()}_clampDistance(t){return Math.max(this.minDistance,Math.min(this.maxDistance,t))}_handleMouseDownRotate(t){this._rotateStart.set(t.clientX,t.clientY)}_handleMouseDownDolly(t){this._updateZoomParameters(t.clientX,t.clientX),this._dollyStart.set(t.clientX,t.clientY)}_handleMouseDownPan(t){this._panStart.set(t.clientX,t.clientY)}_handleMouseMoveRotate(t){this._rotateEnd.set(t.clientX,t.clientY),this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);const e=this.domElement;this._rotateLeft(St*this._rotateDelta.x/e.clientHeight),this._rotateUp(St*this._rotateDelta.y/e.clientHeight),this._rotateStart.copy(this._rotateEnd),this.update()}_handleMouseMoveDolly(t){this._dollyEnd.set(t.clientX,t.clientY),this._dollyDelta.subVectors(this._dollyEnd,this._dollyStart),this._dollyDelta.y>0?this._dollyOut(this._getZoomScale(this._dollyDelta.y)):this._dollyDelta.y<0&&this._dollyIn(this._getZoomScale(this._dollyDelta.y)),this._dollyStart.copy(this._dollyEnd),this.update()}_handleMouseMovePan(t){this._panEnd.set(t.clientX,t.clientY),this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd),this.update()}_handleMouseWheel(t){this._updateZoomParameters(t.clientX,t.clientY),t.deltaY<0?this._dollyIn(this._getZoomScale(t.deltaY)):t.deltaY>0&&this._dollyOut(this._getZoomScale(t.deltaY)),this.update()}_handleKeyDown(t){let e=!1;switch(t.code){case this.keys.UP:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateUp(St*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,this.keyPanSpeed),e=!0;break;case this.keys.BOTTOM:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateUp(-St*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,-this.keyPanSpeed),e=!0;break;case this.keys.LEFT:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateLeft(St*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(this.keyPanSpeed,0),e=!0;break;case this.keys.RIGHT:t.ctrlKey||t.metaKey||t.shiftKey?this.enableRotate&&this._rotateLeft(-St*this.rotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(-this.keyPanSpeed,0),e=!0;break}e&&(t.preventDefault(),this.update())}_handleTouchStartRotate(t){if(this._pointers.length===1)this._rotateStart.set(t.pageX,t.pageY);else{const e=this._getSecondPointerPosition(t),s=.5*(t.pageX+e.x),i=.5*(t.pageY+e.y);this._rotateStart.set(s,i)}}_handleTouchStartPan(t){if(this._pointers.length===1)this._panStart.set(t.pageX,t.pageY);else{const e=this._getSecondPointerPosition(t),s=.5*(t.pageX+e.x),i=.5*(t.pageY+e.y);this._panStart.set(s,i)}}_handleTouchStartDolly(t){const e=this._getSecondPointerPosition(t),s=t.pageX-e.x,i=t.pageY-e.y,a=Math.sqrt(s*s+i*i);this._dollyStart.set(0,a)}_handleTouchStartDollyPan(t){this.enableZoom&&this._handleTouchStartDolly(t),this.enablePan&&this._handleTouchStartPan(t)}_handleTouchStartDollyRotate(t){this.enableZoom&&this._handleTouchStartDolly(t),this.enableRotate&&this._handleTouchStartRotate(t)}_handleTouchMoveRotate(t){if(this._pointers.length==1)this._rotateEnd.set(t.pageX,t.pageY);else{const s=this._getSecondPointerPosition(t),i=.5*(t.pageX+s.x),a=.5*(t.pageY+s.y);this._rotateEnd.set(i,a)}this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);const e=this.domElement;this._rotateLeft(St*this._rotateDelta.x/e.clientHeight),this._rotateUp(St*this._rotateDelta.y/e.clientHeight),this._rotateStart.copy(this._rotateEnd)}_handleTouchMovePan(t){if(this._pointers.length===1)this._panEnd.set(t.pageX,t.pageY);else{const e=this._getSecondPointerPosition(t),s=.5*(t.pageX+e.x),i=.5*(t.pageY+e.y);this._panEnd.set(s,i)}this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd)}_handleTouchMoveDolly(t){const e=this._getSecondPointerPosition(t),s=t.pageX-e.x,i=t.pageY-e.y,a=Math.sqrt(s*s+i*i);this._dollyEnd.set(0,a),this._dollyDelta.set(0,Math.pow(this._dollyEnd.y/this._dollyStart.y,this.zoomSpeed)),this._dollyOut(this._dollyDelta.y),this._dollyStart.copy(this._dollyEnd);const o=(t.pageX+e.x)*.5,r=(t.pageY+e.y)*.5;this._updateZoomParameters(o,r)}_handleTouchMoveDollyPan(t){this.enableZoom&&this._handleTouchMoveDolly(t),this.enablePan&&this._handleTouchMovePan(t)}_handleTouchMoveDollyRotate(t){this.enableZoom&&this._handleTouchMoveDolly(t),this.enableRotate&&this._handleTouchMoveRotate(t)}_addPointer(t){this._pointers.push(t.pointerId)}_removePointer(t){delete this._pointerPositions[t.pointerId];for(let e=0;e<this._pointers.length;e++)if(this._pointers[e]==t.pointerId){this._pointers.splice(e,1);return}}_isTrackingPointer(t){for(let e=0;e<this._pointers.length;e++)if(this._pointers[e]==t.pointerId)return!0;return!1}_trackPointer(t){let e=this._pointerPositions[t.pointerId];e===void 0&&(e=new A.Vector2,this._pointerPositions[t.pointerId]=e),e.set(t.pageX,t.pageY)}_getSecondPointerPosition(t){const e=t.pointerId===this._pointers[0]?this._pointers[1]:this._pointers[0];return this._pointerPositions[e]}_customWheelEvent(t){const e=t.deltaMode,s={clientX:t.clientX,clientY:t.clientY,deltaY:t.deltaY};switch(e){case 1:s.deltaY*=16;break;case 2:s.deltaY*=100;break}return t.ctrlKey&&!this._controlActive&&(s.deltaY*=10),s}}function yl(n){this.enabled!==!1&&(this._pointers.length===0&&(this.domElement.setPointerCapture(n.pointerId),this.domElement.addEventListener("pointermove",this._onPointerMove),this.domElement.addEventListener("pointerup",this._onPointerUp)),!this._isTrackingPointer(n)&&(this._addPointer(n),n.pointerType==="touch"?this._onTouchStart(n):this._onMouseDown(n)))}function Il(n){this.enabled!==!1&&(n.pointerType==="touch"?this._onTouchMove(n):this._onMouseMove(n))}function wl(n){switch(this._removePointer(n),this._pointers.length){case 0:this.domElement.releasePointerCapture(n.pointerId),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.dispatchEvent(Sa),this.state=st.NONE;break;case 1:const t=this._pointers[0],e=this._pointerPositions[t];this._onTouchStart({pointerId:t,pageX:e.x,pageY:e.y});break}}function bl(n){let t;switch(n.button){case 0:t=this.mouseButtons.LEFT;break;case 1:t=this.mouseButtons.MIDDLE;break;case 2:t=this.mouseButtons.RIGHT;break;default:t=-1}switch(t){case A.MOUSE.DOLLY:if(this.enableZoom===!1)return;this._handleMouseDownDolly(n),this.state=st.DOLLY;break;case A.MOUSE.ROTATE:if(n.ctrlKey||n.metaKey||n.shiftKey){if(this.enablePan===!1)return;this._handleMouseDownPan(n),this.state=st.PAN}else{if(this.enableRotate===!1)return;this._handleMouseDownRotate(n),this.state=st.ROTATE}break;case A.MOUSE.PAN:if(n.ctrlKey||n.metaKey||n.shiftKey){if(this.enableRotate===!1)return;this._handleMouseDownRotate(n),this.state=st.ROTATE}else{if(this.enablePan===!1)return;this._handleMouseDownPan(n),this.state=st.PAN}break;default:this.state=st.NONE}this.state!==st.NONE&&this.dispatchEvent(ni)}function El(n){switch(this.state){case st.ROTATE:if(this.enableRotate===!1)return;this._handleMouseMoveRotate(n);break;case st.DOLLY:if(this.enableZoom===!1)return;this._handleMouseMoveDolly(n);break;case st.PAN:if(this.enablePan===!1)return;this._handleMouseMovePan(n);break}}function Sl(n){this.enabled===!1||this.enableZoom===!1||this.state!==st.NONE||(n.preventDefault(),this.dispatchEvent(ni),this._handleMouseWheel(this._customWheelEvent(n)),this.dispatchEvent(Sa))}function Ml(n){this.enabled!==!1&&this._handleKeyDown(n)}function vl(n){switch(this._trackPointer(n),this._pointers.length){case 1:switch(this.touches.ONE){case A.TOUCH.ROTATE:if(this.enableRotate===!1)return;this._handleTouchStartRotate(n),this.state=st.TOUCH_ROTATE;break;case A.TOUCH.PAN:if(this.enablePan===!1)return;this._handleTouchStartPan(n),this.state=st.TOUCH_PAN;break;default:this.state=st.NONE}break;case 2:switch(this.touches.TWO){case A.TOUCH.DOLLY_PAN:if(this.enableZoom===!1&&this.enablePan===!1)return;this._handleTouchStartDollyPan(n),this.state=st.TOUCH_DOLLY_PAN;break;case A.TOUCH.DOLLY_ROTATE:if(this.enableZoom===!1&&this.enableRotate===!1)return;this._handleTouchStartDollyRotate(n),this.state=st.TOUCH_DOLLY_ROTATE;break;default:this.state=st.NONE}break;default:this.state=st.NONE}this.state!==st.NONE&&this.dispatchEvent(ni)}function xl(n){switch(this._trackPointer(n),this.state){case st.TOUCH_ROTATE:if(this.enableRotate===!1)return;this._handleTouchMoveRotate(n),this.update();break;case st.TOUCH_PAN:if(this.enablePan===!1)return;this._handleTouchMovePan(n),this.update();break;case st.TOUCH_DOLLY_PAN:if(this.enableZoom===!1&&this.enablePan===!1)return;this._handleTouchMoveDollyPan(n),this.update();break;case st.TOUCH_DOLLY_ROTATE:if(this.enableZoom===!1&&this.enableRotate===!1)return;this._handleTouchMoveDollyRotate(n),this.update();break;default:this.state=st.NONE}}function Bl(n){this.enabled!==!1&&n.preventDefault()}function Ql(n){n.key==="Control"&&(this._controlActive=!0,this.domElement.getRootNode().addEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}function Dl(n){n.key==="Control"&&(this._controlActive=!1,this.domElement.getRootNode().removeEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}class kl extends va{constructor(t){const e=t.camera;super(e,t.renderer.domElement),this._mobileAdjusted=!1,this._originalMinDistance=.1,this._originalMaxDistance=1e3,this._panDirection="both";const s=this;s.dampingFactor=.1,s.rotateSpeed=.4,s.autoRotateSpeed=.5,s.updateByOptions(t)}updateByOptions(t={}){if(!t)return;const e=this;t.enableDamping!==void 0&&(e.enableDamping=t.enableDamping),t.autoRotate!==void 0&&(e.autoRotate=t.autoRotate,e.autoRotate),t.autoRotateSpeed!==void 0&&(e.autoRotateSpeed=t.autoRotateSpeed,e.autoRotateSpeed),t.enableZoom!==void 0&&(e.enableZoom=t.enableZoom),t.enableRotate!==void 0&&(e.enableRotate=t.enableRotate),t.enablePan!==void 0&&(e.enablePan=t.enablePan,e.enablePan),t.minPan!==void 0&&(e._minPan=new A.Vector3().fromArray(t.minPan),e._minPan),t.maxPan!==void 0&&(e._maxPan=new A.Vector3().fromArray(t.maxPan),e._maxPan),t.panDirection!==void 0&&(e._panDirection=t.panDirection,e._panDirection),t.minDistance!==void 0&&(e.minDistance=t.minDistance,e._originalMinDistance=t.minDistance,e.minDistance),t.maxDistance!==void 0&&(e.maxDistance=t.maxDistance,e._originalMaxDistance=t.maxDistance,e.maxDistance),t.minPolarAngle!==void 0&&(e.minPolarAngle=t.minPolarAngle),t.maxPolarAngle!==void 0&&(e.maxPolarAngle=t.maxPolarAngle),t.fov!==void 0&&(e.object.fov=t.fov),t.near!==void 0&&(e.object.near=t.near),t.far!==void 0&&(e.object.far=t.far),t.position&&e.object.position.fromArray(t.position),t.lookAt&&e.target.fromArray(t.lookAt),t.lookUp&&e.object.up.fromArray(t.lookUp),e.updateControlMode(t),e.updateRotateAxis(),e.update()}updateControlMode(t){const e=this,s=t.useCustomControl===!0;if(e.object.position.toArray(),e.target.toArray(),s){e.disconnect(),e.enabled=!1,e.enableRotate=!1,e.enablePan=!1,e.enableZoom=!1,e.enableDamping=!1,e.autoRotate=!1;const i=this.getDistance();e.minDistance=i,e.maxDistance=i}else e.enabled=!0,e.enableRotate=t.enableRotate!==void 0?t.enableRotate:!0,e.enablePan=t.enablePan!==void 0?t.enablePan:!0,e.enableZoom=t.enableZoom!==void 0?t.enableZoom:!0,e.enableRotate,e.enablePan,e.enableZoom,e.enableDamping=t.enableDamping!==void 0?t.enableDamping:!0,e.autoRotate=t.autoRotate!==void 0?t.autoRotate:!1,e.autoRotate,e.minDistance=t.minDistance!==void 0?t.minDistance:e._originalMinDistance,e.maxDistance=t.maxDistance!==void 0?t.maxDistance:e._originalMaxDistance,e.minDistance,e.maxDistance,e.minPolarAngle=t.minPolarAngle!==void 0?t.minPolarAngle:0,e.maxPolarAngle=t.maxPolarAngle!==void 0?t.maxPolarAngle:Math.PI/2,e.connect();e.object.position.toArray(),e.target.toArray()}getDistance(){return this._spherical?.radius||1}adjustForMobile(){wt&&!this._mobileAdjusted&&this._dollyOut&&(this._dollyOut(.75),this._mobileAdjusted=!0)}updateRotateAxis(){this._quat?.setFromUnitVectors?.(this.object.up,new A.Vector3(0,1,0)),this._quatInverse=this._quat?.clone?.()?.invert?.()}connect(){super.connect()}disconnect(){super.disconnect()}update(){if(this.enabled===!1)return!1;this._applyPanDirectionConstraints();const t=super.update();return this._applyPanConstraints(),t}_applyPanDirectionConstraints(){if(!this.enablePan||this._panDirection==="both")return;const t=this._panOffset;if(!t)return;const e=new A.Vector3;this.object.getWorldDirection(e);const s=new A.Vector3;s.crossVectors(e,this.object.up).normalize();const i=this.object.up.clone().normalize();if(this._panDirection==="horizontal"){const a=s.clone().multiplyScalar(t.dot(s));t.copy(a)}else if(this._panDirection==="vertical"){const a=i.clone().multiplyScalar(t.dot(i));t.copy(a)}}_applyPanConstraints(){if(!this.enablePan)return;const t=this.target,e=t.clone();if(this._minPan&&this._maxPan?t.clamp(this._minPan,this._maxPan):this._minPan?t.max(this._minPan):this._maxPan&&t.min(this._maxPan),!t.equals(e)){const s=t.clone().sub(e);this.object.position.add(s)}}}class Tl extends va{constructor(t,e){super(t,e),this.screenSpacePanning=!1,this.mouseButtons={LEFT:A.MOUSE.PAN,MIDDLE:A.MOUSE.DOLLY,RIGHT:A.MOUSE.ROTATE},this.touches={ONE:A.TOUCH.PAN,TWO:A.TOUCH.DOLLY_ROTATE}}}const xa=new A.Box3,Hn=new A.Vector3;class Ba extends A.InstancedBufferGeometry{constructor(){super(),this.isLineSegmentsGeometry=!0,this.type="LineSegmentsGeometry";const t=[-1,2,0,1,2,0,-1,1,0,1,1,0,-1,0,0,1,0,0,-1,-1,0,1,-1,0],e=[-1,2,1,2,-1,1,1,1,-1,-1,1,-1,-1,-2,1,-2],s=[0,2,1,2,3,1,2,4,3,4,5,3,4,6,5,6,7,5];this.setIndex(s),this.setAttribute("position",new A.Float32BufferAttribute(t,3)),this.setAttribute("uv",new A.Float32BufferAttribute(e,2))}applyMatrix4(t){const e=this.attributes.instanceStart,s=this.attributes.instanceEnd;return e!==void 0&&(e.applyMatrix4(t),s.applyMatrix4(t),e.needsUpdate=!0),this.boundingBox!==null&&this.computeBoundingBox(),this.boundingSphere!==null&&this.computeBoundingSphere(),this}setPositions(t){let e;t instanceof Float32Array?e=t:Array.isArray(t)&&(e=new Float32Array(t));const s=new A.InstancedInterleavedBuffer(e,6,1);return this.setAttribute("instanceStart",new A.InterleavedBufferAttribute(s,3,0)),this.setAttribute("instanceEnd",new A.InterleavedBufferAttribute(s,3,3)),this.instanceCount=this.attributes.instanceStart.count,this.computeBoundingBox(),this.computeBoundingSphere(),this}setColors(t){let e;t instanceof Float32Array?e=t:Array.isArray(t)&&(e=new Float32Array(t));const s=new A.InstancedInterleavedBuffer(e,6,1);return this.setAttribute("instanceColorStart",new A.InterleavedBufferAttribute(s,3,0)),this.setAttribute("instanceColorEnd",new A.InterleavedBufferAttribute(s,3,3)),this}fromWireframeGeometry(t){return this.setPositions(t.attributes.position.array),this}fromEdgesGeometry(t){return this.setPositions(t.attributes.position.array),this}fromMesh(t){return this.fromWireframeGeometry(new A.WireframeGeometry(t.geometry)),this}fromLineSegments(t){const e=t.geometry;return this.setPositions(e.attributes.position.array),this}computeBoundingBox(){this.boundingBox===null&&(this.boundingBox=new A.Box3);const t=this.attributes.instanceStart,e=this.attributes.instanceEnd;t!==void 0&&e!==void 0&&(this.boundingBox.setFromBufferAttribute(t),xa.setFromBufferAttribute(e),this.boundingBox.union(xa))}computeBoundingSphere(){this.boundingSphere===null&&(this.boundingSphere=new A.Sphere),this.boundingBox===null&&this.computeBoundingBox();const t=this.attributes.instanceStart,e=this.attributes.instanceEnd;if(t!==void 0&&e!==void 0){const s=this.boundingSphere.center;this.boundingBox.getCenter(s);let i=0;for(let a=0,o=t.count;a<o;a++)Hn.fromBufferAttribute(t,a),i=Math.max(i,s.distanceToSquared(Hn)),Hn.fromBufferAttribute(e,a),i=Math.max(i,s.distanceToSquared(Hn));this.boundingSphere.radius=Math.sqrt(i),isNaN(this.boundingSphere.radius)&&console.error("THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.",this)}}toJSON(){}applyMatrix(t){return console.warn("THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4()."),this.applyMatrix4(t)}}A.UniformsLib.line={worldUnits:{value:1},linewidth:{value:1},resolution:{value:new A.Vector2(1,1)},dashOffset:{value:0},dashScale:{value:1},dashSize:{value:1},gapSize:{value:1}},A.ShaderLib.line={uniforms:A.UniformsUtils.merge([A.UniformsLib.common,A.UniformsLib.fog,A.UniformsLib.line]),vertexShader:`
		#include <common>
		#include <color_pars_vertex>
		#include <fog_pars_vertex>
		#include <logdepthbuf_pars_vertex>
		#include <clipping_planes_pars_vertex>

		uniform float linewidth;
		uniform vec2 resolution;

		attribute vec3 instanceStart;
		attribute vec3 instanceEnd;

		attribute vec3 instanceColorStart;
		attribute vec3 instanceColorEnd;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#ifdef USE_DASH

			uniform float dashScale;
			attribute float instanceDistanceStart;
			attribute float instanceDistanceEnd;
			varying float vLineDistance;

		#endif

		void trimSegment( const in vec4 start, inout vec4 end ) {

			// trim end segment so it terminates between the camera plane and the near plane

			// conservative estimate of the near plane
			float a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column
			float b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column
			float nearEstimate = - 0.5 * b / a;

			float alpha = ( nearEstimate - start.z ) / ( end.z - start.z );

			end.xyz = mix( start.xyz, end.xyz, alpha );

		}

		void main() {

			#ifdef USE_COLOR

				vColor.xyz = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;

			#endif

			#ifdef USE_DASH

				vLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;
				vUv = uv;

			#endif

			float aspect = resolution.x / resolution.y;

			// camera space
			vec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );
			vec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );

			#ifdef WORLD_UNITS

				worldStart = start.xyz;
				worldEnd = end.xyz;

			#else

				vUv = uv;

			#endif

			// special case for perspective projection, and segments that terminate either in, or behind, the camera plane
			// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space
			// but we need to perform ndc-space calculations in the shader, so we must address this issue directly
			// perhaps there is a more elegant solution -- WestLangley

			bool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column

			if ( perspective ) {

				if ( start.z < 0.0 && end.z >= 0.0 ) {

					trimSegment( start, end );

				} else if ( end.z < 0.0 && start.z >= 0.0 ) {

					trimSegment( end, start );

				}

			}

			// clip space
			vec4 clipStart = projectionMatrix * start;
			vec4 clipEnd = projectionMatrix * end;

			// ndc space
			vec3 ndcStart = clipStart.xyz / clipStart.w;
			vec3 ndcEnd = clipEnd.xyz / clipEnd.w;

			// direction
			vec2 dir = ndcEnd.xy - ndcStart.xy;

			// account for clip-space aspect ratio
			dir.x *= aspect;
			dir = normalize( dir );

			#ifdef WORLD_UNITS

				vec3 worldDir = normalize( end.xyz - start.xyz );
				vec3 tmpFwd = normalize( mix( start.xyz, end.xyz, 0.5 ) );
				vec3 worldUp = normalize( cross( worldDir, tmpFwd ) );
				vec3 worldFwd = cross( worldDir, worldUp );
				worldPos = position.y < 0.5 ? start: end;

				// height offset
				float hw = linewidth * 0.5;
				worldPos.xyz += position.x < 0.0 ? hw * worldUp : - hw * worldUp;

				// don't extend the line if we're rendering dashes because we
				// won't be rendering the endcaps
				#ifndef USE_DASH

					// cap extension
					worldPos.xyz += position.y < 0.5 ? - hw * worldDir : hw * worldDir;

					// add width to the box
					worldPos.xyz += worldFwd * hw;

					// endcaps
					if ( position.y > 1.0 || position.y < 0.0 ) {

						worldPos.xyz -= worldFwd * 2.0 * hw;

					}

				#endif

				// project the worldpos
				vec4 clip = projectionMatrix * worldPos;

				// shift the depth of the projected points so the line
				// segments overlap neatly
				vec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;
				clip.z = clipPose.z * clip.w;

			#else

				vec2 offset = vec2( dir.y, - dir.x );
				// undo aspect ratio adjustment
				dir.x /= aspect;
				offset.x /= aspect;

				// sign flip
				if ( position.x < 0.0 ) offset *= - 1.0;

				// endcaps
				if ( position.y < 0.0 ) {

					offset += - dir;

				} else if ( position.y > 1.0 ) {

					offset += dir;

				}

				// adjust for linewidth
				offset *= linewidth;

				// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...
				offset /= resolution.y;

				// select end
				vec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;

				// back to clip space
				offset *= clip.w;

				clip.xy += offset;

			#endif

			gl_Position = clip;

			vec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation

			#include <logdepthbuf_vertex>
			#include <clipping_planes_vertex>
			#include <fog_vertex>

		}
		`,fragmentShader:`
		uniform vec3 diffuse;
		uniform float opacity;
		uniform float linewidth;

		#ifdef USE_DASH

			uniform float dashOffset;
			uniform float dashSize;
			uniform float gapSize;

		#endif

		varying float vLineDistance;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#include <common>
		#include <color_pars_fragment>
		#include <fog_pars_fragment>
		#include <logdepthbuf_pars_fragment>
		#include <clipping_planes_pars_fragment>

		vec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {

			float mua;
			float mub;

			vec3 p13 = p1 - p3;
			vec3 p43 = p4 - p3;

			vec3 p21 = p2 - p1;

			float d1343 = dot( p13, p43 );
			float d4321 = dot( p43, p21 );
			float d1321 = dot( p13, p21 );
			float d4343 = dot( p43, p43 );
			float d2121 = dot( p21, p21 );

			float denom = d2121 * d4343 - d4321 * d4321;

			float numer = d1343 * d4321 - d1321 * d4343;

			mua = numer / denom;
			mua = clamp( mua, 0.0, 1.0 );
			mub = ( d1343 + d4321 * ( mua ) ) / d4343;
			mub = clamp( mub, 0.0, 1.0 );

			return vec2( mua, mub );

		}

		void main() {

			#include <clipping_planes_fragment>

			#ifdef USE_DASH

				if ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps

				if ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX

			#endif

			float alpha = opacity;

			#ifdef WORLD_UNITS

				// Find the closest points on the view ray and the line segment
				vec3 rayEnd = normalize( worldPos.xyz ) * 1e5;
				vec3 lineDir = worldEnd - worldStart;
				vec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );

				vec3 p1 = worldStart + lineDir * params.x;
				vec3 p2 = rayEnd * params.y;
				vec3 delta = p1 - p2;
				float len = length( delta );
				float norm = len / linewidth;

				#ifndef USE_DASH

					#ifdef USE_ALPHA_TO_COVERAGE

						float dnorm = fwidth( norm );
						alpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );

					#else

						if ( norm > 0.5 ) {

							discard;

						}

					#endif

				#endif

			#else

				#ifdef USE_ALPHA_TO_COVERAGE

					// artifacts appear on some hardware if a derivative is taken within a conditional
					float a = vUv.x;
					float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
					float len2 = a * a + b * b;
					float dlen = fwidth( len2 );

					if ( abs( vUv.y ) > 1.0 ) {

						alpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );

					}

				#else

					if ( abs( vUv.y ) > 1.0 ) {

						float a = vUv.x;
						float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
						float len2 = a * a + b * b;

						if ( len2 > 1.0 ) discard;

					}

				#endif

			#endif

			vec4 diffuseColor = vec4( diffuse, alpha );

			#include <logdepthbuf_fragment>
			#include <color_fragment>

			gl_FragColor = vec4( diffuseColor.rgb, alpha );

			#include <tonemapping_fragment>
			#include <colorspace_fragment>
			#include <fog_fragment>
			#include <premultiplied_alpha_fragment>

		}
		`};class we extends A.ShaderMaterial{constructor(t){super({type:"LineMaterial",uniforms:A.UniformsUtils.clone(A.ShaderLib.line.uniforms),vertexShader:A.ShaderLib.line.vertexShader,fragmentShader:A.ShaderLib.line.fragmentShader,clipping:!0}),this.isLineMaterial=!0,this.setValues(t)}get color(){return this.uniforms.diffuse.value}set color(t){this.uniforms.diffuse.value=t}get worldUnits(){return"WORLD_UNITS"in this.defines}set worldUnits(t){t===!0?this.defines.WORLD_UNITS="":delete this.defines.WORLD_UNITS}get linewidth(){return this.uniforms.linewidth.value}set linewidth(t){this.uniforms.linewidth&&(this.uniforms.linewidth.value=t)}get dashed(){return"USE_DASH"in this.defines}set dashed(t){t===!0!==this.dashed&&(this.needsUpdate=!0),t===!0?this.defines.USE_DASH="":delete this.defines.USE_DASH}get dashScale(){return this.uniforms.dashScale.value}set dashScale(t){this.uniforms.dashScale.value=t}get dashSize(){return this.uniforms.dashSize.value}set dashSize(t){this.uniforms.dashSize.value=t}get dashOffset(){return this.uniforms.dashOffset.value}set dashOffset(t){this.uniforms.dashOffset.value=t}get gapSize(){return this.uniforms.gapSize.value}set gapSize(t){this.uniforms.gapSize.value=t}get opacity(){return this.uniforms.opacity.value}set opacity(t){this.uniforms&&(this.uniforms.opacity.value=t)}get resolution(){return this.uniforms.resolution.value}set resolution(t){this.uniforms.resolution.value.copy(t)}get alphaToCoverage(){return"USE_ALPHA_TO_COVERAGE"in this.defines}set alphaToCoverage(t){this.defines&&(t===!0!==this.alphaToCoverage&&(this.needsUpdate=!0),t===!0?this.defines.USE_ALPHA_TO_COVERAGE="":delete this.defines.USE_ALPHA_TO_COVERAGE)}}const ii=new A.Vector4,Qa=new A.Vector3,Da=new A.Vector3,ft=new A.Vector4,pt=new A.Vector4,Ht=new A.Vector4,oi=new A.Vector3,ai=new A.Matrix4,ht=new A.Line3,ka=new A.Vector3,Gn=new A.Box3,Yn=new A.Sphere,Gt=new A.Vector4;let Yt,oe;function Ta(n,t,e){return Gt.set(0,0,-t,1).applyMatrix4(n.projectionMatrix),Gt.multiplyScalar(1/Gt.w),Gt.x=oe/e.width,Gt.y=oe/e.height,Gt.applyMatrix4(n.projectionMatrixInverse),Gt.multiplyScalar(1/Gt.w),Math.abs(Math.max(Gt.x,Gt.y))}function Rl(n,t){const e=n.matrixWorld,s=n.geometry,i=s.attributes.instanceStart,a=s.attributes.instanceEnd,o=Math.min(s.instanceCount,i.count);for(let r=0,l=o;r<l;r++){ht.start.fromBufferAttribute(i,r),ht.end.fromBufferAttribute(a,r),ht.applyMatrix4(e);const u=new A.Vector3,c=new A.Vector3;Yt.distanceSqToSegment(ht.start,ht.end,c,u),c.distanceTo(u)<oe*.5&&t.push({point:c,pointOnLine:u,distance:Yt.origin.distanceTo(c),object:n,face:null,faceIndex:r,uv:null,uv1:null})}}function Ll(n,t,e){const s=t.projectionMatrix,a=n.material.resolution,o=n.matrixWorld,r=n.geometry,l=r.attributes.instanceStart,u=r.attributes.instanceEnd,c=Math.min(r.instanceCount,l.count),d=-t.near;Yt.at(1,Ht),Ht.w=1,Ht.applyMatrix4(t.matrixWorldInverse),Ht.applyMatrix4(s),Ht.multiplyScalar(1/Ht.w),Ht.x*=a.x/2,Ht.y*=a.y/2,Ht.z=0,oi.copy(Ht),ai.multiplyMatrices(t.matrixWorldInverse,o);for(let f=0,m=c;f<m;f++){if(ft.fromBufferAttribute(l,f),pt.fromBufferAttribute(u,f),ft.w=1,pt.w=1,ft.applyMatrix4(ai),pt.applyMatrix4(ai),ft.z>d&&pt.z>d)continue;if(ft.z>d){const v=ft.z-pt.z,B=(ft.z-d)/v;ft.lerp(pt,B)}else if(pt.z>d){const v=pt.z-ft.z,B=(pt.z-d)/v;pt.lerp(ft,B)}ft.applyMatrix4(s),pt.applyMatrix4(s),ft.multiplyScalar(1/ft.w),pt.multiplyScalar(1/pt.w),ft.x*=a.x/2,ft.y*=a.y/2,pt.x*=a.x/2,pt.y*=a.y/2,ht.start.copy(ft),ht.start.z=0,ht.end.copy(pt),ht.end.z=0;const I=ht.closestPointToPointParameter(oi,!0);ht.at(I,ka);const p=A.MathUtils.lerp(ft.z,pt.z,I),h=p>=-1&&p<=1,g=oi.distanceTo(ka)<oe*.5;if(h&&g){ht.start.fromBufferAttribute(l,f),ht.end.fromBufferAttribute(u,f),ht.start.applyMatrix4(o),ht.end.applyMatrix4(o);const v=new A.Vector3,B=new A.Vector3;Yt.distanceSqToSegment(ht.start,ht.end,B,v),e.push({point:B,pointOnLine:v,distance:Yt.origin.distanceTo(B),object:n,face:null,faceIndex:f,uv:null,uv1:null})}}}class Fl extends A.Mesh{constructor(t=new Ba,e=new we({color:Math.random()*16777215})){super(t,e),this.isLineSegments2=!0,this.type="LineSegments2"}computeLineDistances(){const t=this.geometry,e=t.attributes.instanceStart,s=t.attributes.instanceEnd,i=new Float32Array(2*e.count);for(let o=0,r=0,l=e.count;o<l;o++,r+=2)Qa.fromBufferAttribute(e,o),Da.fromBufferAttribute(s,o),i[r]=r===0?0:i[r-1],i[r+1]=i[r]+Qa.distanceTo(Da);const a=new A.InstancedInterleavedBuffer(i,2,1);return t.setAttribute("instanceDistanceStart",new A.InterleavedBufferAttribute(a,1,0)),t.setAttribute("instanceDistanceEnd",new A.InterleavedBufferAttribute(a,1,1)),this}raycast(t,e){const s=this.material.worldUnits,i=t.camera;i===null&&!s&&console.error('LineSegments2: "Raycaster.camera" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.');const a=t.params.Line2!==void 0&&t.params.Line2.threshold||0;Yt=t.ray;const o=this.matrixWorld,r=this.geometry,l=this.material;oe=l.linewidth+a,r.boundingSphere===null&&r.computeBoundingSphere(),Yn.copy(r.boundingSphere).applyMatrix4(o);let u;if(s)u=oe*.5;else{const d=Math.max(i.near,Yn.distanceToPoint(Yt.origin));u=Ta(i,d,l.resolution)}if(Yn.radius+=u,Yt.intersectsSphere(Yn)===!1)return;r.boundingBox===null&&r.computeBoundingBox(),Gn.copy(r.boundingBox).applyMatrix4(o);let c;if(s)c=oe*.5;else{const d=Math.max(i.near,Gn.distanceToPoint(Yt.origin));c=Ta(i,d,l.resolution)}Gn.expandByScalar(c),Yt.intersectsBox(Gn)!==!1&&(s?Rl(this,e):Ll(this,i,e))}onBeforeRender(t){const e=this.material.uniforms;e&&e.resolution&&(t.getViewport(ii),this.material.uniforms.resolution.value.set(ii.z,ii.w))}}class Oe extends Ba{constructor(){super(),this.isLineGeometry=!0,this.type="LineGeometry"}setPositions(t){const e=t.length-3,s=new Float32Array(2*e);for(let i=0;i<e;i+=3)s[2*i]=t[i],s[2*i+1]=t[i+1],s[2*i+2]=t[i+2],s[2*i+3]=t[i+3],s[2*i+4]=t[i+4],s[2*i+5]=t[i+5];return super.setPositions(s),this}setColors(t){const e=t.length-3,s=new Float32Array(2*e);for(let i=0;i<e;i+=3)s[2*i]=t[i],s[2*i+1]=t[i+1],s[2*i+2]=t[i+2],s[2*i+3]=t[i+3],s[2*i+4]=t[i+4],s[2*i+5]=t[i+5];return super.setColors(s),this}setFromPoints(t){const e=t.length-1,s=new Float32Array(6*e);for(let i=0;i<e;i++)s[6*i]=t[i].x,s[6*i+1]=t[i].y,s[6*i+2]=t[i].z||0,s[6*i+3]=t[i+1].x,s[6*i+4]=t[i+1].y,s[6*i+5]=t[i+1].z||0;return super.setPositions(s),this}fromLine(t){const e=t.geometry;return this.setPositions(e.attributes.position.array),this}}class ae extends Fl{constructor(t=new Oe,e=new we({color:Math.random()*16777215})){super(t,e),this.isLine2=!0,this.type="Line2"}}const Ra=new A.Vector3,Pl=new A.Quaternion,La=new A.Vector3;class Vl extends A.Object3D{constructor(t=document.createElement("div")){super(),this.isCSS3DObject=!0,this.element=t,this.element.style.position="absolute",this.element.style.pointerEvents="auto",this.element.style.userSelect="none",this.element.setAttribute("draggable",!1),this.addEventListener("removed",function(){this.traverse(function(e){e.element instanceof e.element.ownerDocument.defaultView.Element&&e.element.parentNode!==null&&e.element.remove()})})}copy(t,e){return super.copy(t,e),this.element=t.element.cloneNode(!0),this}}class yt extends Vl{constructor(t){super(t),this.isCSS3DSprite=!0,this.rotation2D=0}copy(t,e){return super.copy(t,e),this.rotation2D=t.rotation2D,this}}const zt=new A.Matrix4,_l=new A.Matrix4;class Ul{constructor(t={}){const e=this;let s,i,a,o;const r={camera:{style:""},objects:new WeakMap},l=t.element!==void 0?t.element:document.createElement("div");l.style.overflow="hidden",this.domElement=l;const u=document.createElement("div");u.style.transformOrigin="0 0",u.style.pointerEvents="none",l.appendChild(u);const c=document.createElement("div");c.style.transformStyle="preserve-3d",u.appendChild(c),this.getSize=function(){return{width:s,height:i}},this.render=function(p,h){const g=h.projectionMatrix.elements[5]*o;h.view&&h.view.enabled?(u.style.transform=`translate( ${-h.view.offsetX*(s/h.view.width)}px, ${-h.view.offsetY*(i/h.view.height)}px )`,u.style.transform+=`scale( ${h.view.fullWidth/h.view.width}, ${h.view.fullHeight/h.view.height} )`):u.style.transform="",p.matrixWorldAutoUpdate===!0&&p.updateMatrixWorld(),h.parent===null&&h.matrixWorldAutoUpdate===!0&&h.updateMatrixWorld();let v,B;h.isOrthographicCamera&&(v=-(h.right+h.left)/2,B=(h.top+h.bottom)/2);const R=h.view&&h.view.enabled?h.view.height/h.view.fullHeight:1,x=h.isOrthographicCamera?`scale( ${R} )scale(`+g+")translate("+d(v)+"px,"+d(B)+"px)"+f(h.matrixWorldInverse):`scale( ${R} )translateZ(`+g+"px)"+f(h.matrixWorldInverse),M=(h.isPerspectiveCamera?"perspective("+g+"px) ":"")+x+"translate("+a+"px,"+o+"px)";r.camera.style!==M&&(c.style.transform=M,r.camera.style=M),I(p,p,h)},this.setSize=function(p,h){s=p,i=h,a=s/2,o=i/2,l.style.width=p+"px",l.style.height=h+"px",u.style.width=p+"px",u.style.height=h+"px",c.style.width=p+"px",c.style.height=h+"px"};function d(p){return Math.abs(p)<1e-10?0:p}function f(p){const h=p.elements;return"matrix3d("+d(h[0])+","+d(-h[1])+","+d(h[2])+","+d(h[3])+","+d(h[4])+","+d(-h[5])+","+d(h[6])+","+d(h[7])+","+d(h[8])+","+d(-h[9])+","+d(h[10])+","+d(h[11])+","+d(h[12])+","+d(-h[13])+","+d(h[14])+","+d(h[15])+")"}function m(p){const h=p.elements;return"translate(-50%,-50%)"+("matrix3d("+d(h[0])+","+d(h[1])+","+d(h[2])+","+d(h[3])+","+d(-h[4])+","+d(-h[5])+","+d(-h[6])+","+d(-h[7])+","+d(h[8])+","+d(h[9])+","+d(h[10])+","+d(h[11])+","+d(h[12])+","+d(h[13])+","+d(h[14])+","+d(h[15])+")")}function C(p){p.isCSS3DObject&&(p.element.style.display="none");for(let h=0,g=p.children.length;h<g;h++)C(p.children[h])}function I(p,h,g,v){if(p.visible===!1){C(p);return}if(p.isCSS3DObject){const B=p.layers.test(g.layers)===!0,R=p.element;if(R.style.display=B===!0?"":"none",B===!0){p.onBeforeRender(e,h,g);let x;p.isCSS3DSprite?(zt.copy(g.matrixWorldInverse),zt.transpose(),p.rotation2D!==0&&zt.multiply(_l.makeRotationZ(p.rotation2D)),p.matrixWorld.decompose(Ra,Pl,La),zt.setPosition(Ra),zt.scale(La),zt.elements[3]=0,zt.elements[7]=0,zt.elements[11]=0,zt.elements[15]=1,x=m(zt)):x=m(p.matrixWorld);const k=r.objects.get(p);if(k===void 0||k.style!==x){R.style.transform=x;const M={style:x};r.objects.set(p,M)}R.parentNode!==c&&c.appendChild(R),p.onAfterRender(e,h,g)}}for(let B=0,R=p.children.length;B<R;B++)I(p.children[B],h,g)}}}class ri extends ae{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.events=t}drawStart(t,e){if(this.disposed)return;const s=this,i=document.querySelectorAll(".mark-wrap-line.main-warp").length+1,a={type:"MarkDistanceLine",name:e||"line"+Date.now(),startPoint:t.toArray(),endPoint:t.toArray(),lineColor:"#eeee00",lineWidth:3,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,distanceTagColor:"#000000",distanceTagBackground:"#e0ffff",distanceTagOpacity:.9,distanceTagVisible:!0,title:"标记距离"+i},o=new Oe;o.setPositions([...a.startPoint,...a.endPoint]);const r=new we({color:a.lineColor,linewidth:a.lineWidth});r.resolution.set(innerWidth,innerHeight),s.copy(new ae(o,r));const l=new A.CircleGeometry(.05,32),u=new A.MeshBasicMaterial({color:16777215,side:A.DoubleSide});u.transparent=!0,u.opacity=.6;const c=new A.Mesh(l,u);c.position.copy(t),c.isMark=!0;const d=new A.Mesh(l,u);d.position.copy(t),d.isMark=!0;const f=document.createElement("div");f.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`,f.classList.add("mark-wrap-line",`${a.name}`,"main-warp"),f.style.position="absolute",f.style.borderRadius="4px",f.style.cursor="pointer",f.onclick=()=>{if(s.events.fire(F).markMode)return;const p=parent?.onActiveMark;p?.(s.getMarkData(!0)),s.events.fire(gt)},f.oncontextmenu=p=>p.preventDefault();const m=new yt(f);m.position.copy(t),m.element.style.pointerEvents="none",m.scale.set(.01,.01,.01),m.visible=a.mainTagVisible;const C=document.createElement("div");C.innerHTML=`<span class="${e}-distance-tag ${e}-distance-tag0" style="color:${a.distanceTagColor};background:${a.distanceTagBackground};opacity:${a.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`,C.classList.add("mark-wrap-line",`${e}`,"distance-warp"),C.style.position="absolute",C.style.borderRadius="4px",C.style.pointerEvents="none";const I=new yt(C);I.position.set(t.x,t.y,t.z),I.element.style.pointerEvents="none",I.scale.set(.008,.008,.008),I.visible=!1,s.add(c,d,m,I),s.data=a,s.circleStart=c,s.circleEnd=d,s.css3dTag=I,s.css3dMainTag=m,s.events.fire(Zt,s)}drawUpdate(t,e=!0){if(this.disposed)return;const s=this;if(t?.endPoint){e&&(s.data.endPoint=[...t.endPoint]);const i=new A.Vector3().fromArray(s.data.startPoint),a=new A.Vector3().fromArray(t.endPoint);s.geometry.setPositions([...s.data.startPoint,...t.endPoint]);const o=new A.Vector3((i.x+a.x)/2,(i.y+a.y)/2,(i.z+a.z)/2);s.css3dTag.position.set(o.x,o.y,o.z);const r=a.clone().sub(i).normalize();s.circleStart.lookAt(s.circleStart.position.clone().add(r)),s.circleEnd.lookAt(s.circleEnd.position.clone().add(r));const l=i.distanceTo(a);s.css3dTag.visible=l>.5;const u=(l*s.events.fire(F).meterScale).toFixed(2)+" m";s.css3dTag.element.childNodes[0].innerText=u}t?.lineColor&&(e&&(s.data.lineColor=t.lineColor),s.material.color.set(t.lineColor)),t?.lineWidth&&(e&&(s.data.lineWidth=t.lineWidth),s.material.linewidth=t.lineWidth),t?.mainTagColor&&(e&&(s.data.mainTagColor=t.mainTagColor),document.querySelector(`.${s.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(s.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${s.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(s.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${s.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(s.data.mainTagVisible=t.mainTagVisible),s.css3dMainTag.visible=t.mainTagVisible),t?.distanceTagColor&&(e&&(s.data.distanceTagColor=t.distanceTagColor),document.querySelector(`.${s.data.name}-distance-tag0`).style.color=t.distanceTagColor),t?.distanceTagBackground&&(e&&(s.data.distanceTagBackground=t.distanceTagBackground),document.querySelector(`.${s.data.name}-distance-tag0`).style.background=t.distanceTagBackground),t?.distanceTagOpacity&&(e&&(s.data.distanceTagOpacity=t.distanceTagOpacity),document.querySelector(`.${s.data.name}-distance-tag0`).style.opacity=t.distanceTagOpacity.toString()),t?.distanceTagVisible!==void 0&&(e&&(s.data.distanceTagVisible=t.distanceTagVisible),s.css3dTag.visible=t.distanceTagVisible),t?.title!==void 0&&(e&&(s.data.title=t.title),this.css3dMainTag.element.querySelector(`.${s.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(s.data.note=t.note),s.events.fire(Z)}updateByMeterScale(t){const e=this,s=new A.Vector3().fromArray(e.data.startPoint),i=new A.Vector3().fromArray(e.data.endPoint),a=(s.distanceTo(i)*t).toFixed(2)+" m";e.css3dTag.element.childNodes[0].innerText=a}drawFinish(t){if(this.disposed)return;const e=this;e.data.endPoint=[...t.toArray()];const s=new A.Vector3().fromArray(e.data.startPoint),i=new A.Vector3().fromArray(e.data.endPoint),a=new A.Vector3((s.x+i.x)/2,(s.y+i.y)/2,(s.z+i.z)/2);e.geometry.setPositions([...e.data.startPoint,...e.data.endPoint]),e.css3dTag.position.set(a.x,a.y,a.z);const o=i.clone().sub(s).normalize();e.circleStart.lookAt(e.circleStart.position.clone().add(o)),e.circleEnd.lookAt(e.circleEnd.position.clone().add(o)),e.circleEnd.position.copy(i);const r=s.distanceTo(i);e.css3dTag.visible=!0;const l=(r*e.events.fire(F).meterScale).toFixed(2)+" m";e.css3dTag.element.childNodes[0].innerText=l,e.events.fire(jt);const u=parent?.onActiveMark,c=e.getMarkData(!0);c.isNew=!0,c.meterScale=e.events.fire(F).meterScale,u?.(c)}draw(t){if(this.disposed)return;const e=this,s={type:"MarkDistanceLine",name:t.name||"line"+Date.now(),startPoint:[...t.startPoint],endPoint:[...t.endPoint],lineColor:t.lineColor||"#eeee00",lineWidth:t.lineWidth||3,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,distanceTagColor:t.distanceTagColor||"#000000",distanceTagBackground:t.distanceTagBackground||"#e0ffff",distanceTagOpacity:t.distanceTagOpacity||.9,distanceTagVisible:t.distanceTagVisible===void 0?!0:t.distanceTagVisible,title:t.title||"标记距离"},i=new A.Vector3().fromArray(s.startPoint),a=new A.Vector3().fromArray(s.endPoint),o=new A.Vector3((i.x+a.x)/2,(i.y+a.y)/2,(i.z+a.z)/2),r=new Oe;r.setPositions([...s.startPoint,...s.endPoint]);const l=new we({color:s.lineColor,linewidth:s.lineWidth});l.resolution.set(innerWidth,innerHeight),e.copy(new ae(r,l));const u=new A.CircleGeometry(.05,32),c=new A.MeshBasicMaterial({color:16777215,side:A.DoubleSide});c.transparent=!0,c.opacity=.6;const d=new A.Mesh(u,c);d.position.copy(i),d.isMark=!0;const f=new A.Mesh(u,c);f.position.copy(a),f.isMark=!0;const m=a.clone().sub(i).normalize();d.lookAt(d.position.clone().add(m)),f.lookAt(f.position.clone().add(m));const C=s.name,I=document.createElement("div");I.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${C}-main-tag" style="color:${s.mainTagColor};background:${s.mainTagBackground};opacity:${s.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${s.title}</span>
                             </div>`,I.classList.add("mark-wrap-line",`${s.name}`,"main-warp"),I.style.position="absolute",I.style.borderRadius="4px",I.style.cursor="pointer",I.onclick=()=>{if(e.events.fire(F).markMode)return;const R=parent?.onActiveMark;R?.(e.getMarkData(!0)),e.events.fire(gt)},I.oncontextmenu=R=>R.preventDefault();const p=new yt(I);p.position.copy(i),p.element.style.pointerEvents="none",p.scale.set(.01,.01,.01),p.visible=s.mainTagVisible;const g=(i.distanceTo(a)*e.events.fire(F).meterScale).toFixed(2)+" m",v=document.createElement("div");v.innerHTML=`<span class="${C}-distance-tag ${C}-distance-tag0" style="color:${s.distanceTagColor};background:${s.distanceTagBackground};opacity:${s.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${g}</span>`,v.classList.add("mark-wrap-line",`${C}`,"distance-warp"),v.style.position="absolute",v.style.borderRadius="4px",v.style.pointerEvents="none";const B=new yt(v);B.position.copy(o),B.element.style.pointerEvents="none",B.scale.set(.008,.008,.008),B.visible=s.distanceTagVisible,e.add(d,f,p,B),e.data=s,e.circleStart=d,e.circleEnd=f,e.css3dTag=B,e.css3dMainTag=p,e.events.fire(Zt,e)}getMarkData(t=!1){const e={...this.data};return t?(delete e.startPoint,delete e.endPoint):(e.startPoint=[...e.startPoint],e.endPoint=[...e.endPoint]),e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Jt,t),t.events.fire(z).remove(t),t.events.fire(he,t),document.querySelectorAll(`.${t.data.name}`).forEach(s=>s.parentElement?.removeChild(s)),t.events=null,t.data=null,t.circleStart=null,t.circleEnd=null,t.css3dTag=null,t.css3dMainTag=null}}class ci extends ae{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.css3dTags=[],this.group=new A.Group,this.add(this.group),this.events=t}drawStart(t,e){if(this.disposed)return;const s=document.querySelectorAll(".mark-wrap-lines.main-warp").length+1,i={type:"MarkMultiLines",name:e||"lines"+Date.now(),points:[...t.toArray(),...t.toArray()],lineColor:"#eeee00",lineWidth:3,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,distanceTagColor:"#000000",distanceTagBackground:"#e0ffff",distanceTagOpacity:.9,distanceTagVisible:!0,title:"标记线"+s,note:""};this.draw(i)}drawUpdate(t,e=!0,s,i=!1){if(this.disposed)return;const a=this;if(s)if(i){const o=a.data.points.length,r=a.css3dTags.length-1,l=new A.Vector3().fromArray(a.data.points.slice(o-6,o-3)),u=s,c=new A.Vector3((l.x+u.x)/2,(l.y+u.y)/2,(l.z+u.z)/2),f=(l.distanceTo(u)*a.events.fire(F).meterScale).toFixed(2)+" m";a.css3dTags[r].element.childNodes[0].innerText=f,a.css3dTags[r].position.set(c.x,c.y,c.z),a.css3dTags[r].visible=!0,a.data.points.pop(),a.data.points.pop(),a.data.points.pop(),a.data.points=[...a.data.points,...s.toArray(),...s.toArray()],a.css3dTags[a.css3dTags.length-1].visible=!0,this.draw(this.data)}else{const o=a.data.points.length;a.data.points[o-3]=s.x,a.data.points[o-2]=s.y,a.data.points[o-1]=s.z;const r=a.css3dTags.length-1,l=new A.Vector3().fromArray(a.data.points.slice(o-6,o-3)),u=new A.Vector3().fromArray(a.data.points.slice(o-3)),c=new A.Vector3((l.x+u.x)/2,(l.y+u.y)/2,(l.z+u.z)/2),d=l.distanceTo(u),f=(d*a.events.fire(F).meterScale).toFixed(2)+" m";a.css3dTags[r].element.childNodes[0].innerText=f,a.geometry.setPositions([...a.data.points]),a.css3dTags[r].position.set(c.x,c.y,c.z),a.css3dTags[r].visible=i?!0:d>.5}t?.lineColor&&(e&&(a.data.lineColor=t.lineColor),a.material.color.set(t.lineColor)),t?.lineWidth&&(e&&(a.data.lineWidth=t.lineWidth),a.material.linewidth=t.lineWidth),t?.mainTagColor&&(e&&(a.data.mainTagColor=t.mainTagColor),document.querySelector(`.${a.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(a.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${a.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(a.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${a.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(a.data.mainTagVisible=t.mainTagVisible),a.css3dMainTag.visible=t.mainTagVisible),t?.distanceTagColor&&(e&&(a.data.distanceTagColor=t.distanceTagColor),document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach(r=>r.style.color=t.distanceTagColor)),t?.distanceTagBackground&&(e&&(a.data.distanceTagBackground=t.distanceTagBackground),document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach(r=>r.style.background=t.distanceTagBackground)),t?.distanceTagOpacity&&(e&&(a.data.distanceTagOpacity=t.distanceTagOpacity),document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach(r=>r.style.opacity=t.distanceTagOpacity.toString())),t?.distanceTagVisible!==void 0&&(e&&(a.data.distanceTagVisible=t.distanceTagVisible),a.css3dTags.forEach(o=>o.visible=t.distanceTagVisible)),t?.title!==void 0&&(e&&(a.data.title=t.title),this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(a.data.note=t.note),a.events.fire(Z)}updateByMeterScale(t){const e=this,s=[];for(let o=0,r=e.data.points.length/3;o<r;o++)s.push(new A.Vector3(e.data.points[o*3],e.data.points[o*3+1],e.data.points[o*3+2]));let i,a;for(let o=1;o<s.length;o++)i=s[o-1],a=s[o],e.css3dTags[o-1].element.childNodes[0].innerText=(i.distanceTo(a)*t).toFixed(2)+" m"}drawFinish(t){if(this.disposed)return;const e=this,s=e.data.points.length,i=new A.Vector3().fromArray(e.data.points.slice(s-6,s-3)),a=new A.Vector3().fromArray(e.data.points.slice(s-3));if((!t||i.distanceTo(a)<.001)&&(e.data.points.pop(),e.data.points.pop(),e.data.points.pop(),e.draw(e.data)),e.events.fire(jt),e.data.points.length<6){e.dispose();return}else{for(;e.css3dTags.length>e.data.points.length/3-1;){const l=e.css3dTags.pop();e.group.remove(l),l.element.parentElement?.removeChild(l.element)}e.css3dTags[e.css3dTags.length-1].visible=!0}const o=parent?.onActiveMark,r=e.getMarkData(!0);r.isNew=!0,r.meterScale=e.events.fire(F).meterScale,o?.(r)}draw(t,e=!1){if(this.disposed)return;const s=this;this.css3dTags=this.css3dTags||[];const i={type:"MarkMultiLines",name:t.name||"lines"+Date.now(),points:[...t.points],lineColor:t.lineColor||"#eeee00",lineWidth:t.lineWidth||3,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,distanceTagColor:t.distanceTagColor||"#000000",distanceTagBackground:t.distanceTagBackground||"#e0ffff",distanceTagOpacity:t.distanceTagOpacity||.9,distanceTagVisible:t.distanceTagVisible===void 0?!0:t.distanceTagVisible,title:t.title||"标记线"+(document.querySelectorAll(".mark-wrap-lines.main-warp").length+1),note:t.note||""},a=s.geometry,o=s.material,r=new Oe;r.setPositions([...i.points]);const l=new we({color:i.lineColor,linewidth:i.lineWidth});l.resolution.set(innerWidth,innerHeight),s.copy(new ae(r,l)),a?.dispose(),o?.dispose();const u=i.name,c=i.points.length/3-1;if(!s.css3dMainTag){const d=document.createElement("div");d.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${u}-main-tag" style="color:${i.mainTagColor};background:${i.mainTagBackground};opacity:${i.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${i.title}</span>
                                     </div>`,d.classList.add("mark-wrap-lines",`${i.name}`,"main-warp"),d.style.position="absolute",d.style.borderRadius="4px",d.style.cursor="pointer",d.onclick=()=>{if(s.events.fire(F).markMode)return;const m=parent?.onActiveMark,C=s.getMarkData(!0);C.meterScale=s.events.fire(F).meterScale,m?.(C),s.events.fire(gt)},d.oncontextmenu=m=>m.preventDefault();const f=new yt(d);f.position.set(i.points[0],i.points[1],i.points[2]),f.element.style.pointerEvents="none",f.scale.set(.01,.01,.01),f.visible=i.mainTagVisible,s.group.add(f),s.css3dMainTag=f}for(let d=s.css3dTags.length;d<c;d++){const f=new A.Vector3().fromArray(i.points.slice(d*3,d*3+3)),m=new A.Vector3().fromArray(i.points.slice(d*3+3,d*3+6)),C=new A.Vector3((f.x+m.x)/2,(f.y+m.y)/2,(f.z+m.z)/2),p=(f.distanceTo(m)*s.events.fire(F).meterScale).toFixed(2)+" m",h=document.createElement("div");h.innerHTML=`<span class="${u}-distance-tag ${u}-distance-tag${d}" style="color:${i.distanceTagColor};background:${i.distanceTagBackground};opacity:${i.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${p}</span>`,h.classList.add("mark-wrap-lines",`${u}`,"distance-warp"),h.style.position="absolute",h.style.borderRadius="4px",h.style.display="none";const g=new yt(h);g.position.copy(C),g.element.style.pointerEvents="none",g.scale.set(.008,.008,.008),g.visible=i.distanceTagVisible,s.css3dTags.push(g),s.group.add(g)}e||(s.css3dTags[s.css3dTags.length-1].visible=!1),s.data=i,s.events.fire(Zt,s)}getMarkData(t=!1){const e={...this.data};return t?delete e.points:e.points=[...e.points],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Jt,t),t.events.fire(z).remove(t),t.events.fire(he,t),t.geometry.dispose(),t.material.dispose(),document.querySelectorAll(`.${t.data.name}`).forEach(s=>s.parentElement?.removeChild(s)),t.events=null,t.data=null,t.css3dTags=null,t.group=null}}class Fa extends A.Group{constructor(t,e,s){super(),this.isMark=!0,this.disposed=!1,this.events=t;const i=this;let a;if(e instanceof A.Vector3){const l=document.querySelectorAll(".mark-wrap-point").length+1;a={type:"MarkSinglePoint",name:s||"point"+Date.now(),point:e.toArray(),iconName:"#svgicon-point2",iconColor:"#eeee00",iconOpacity:.8,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,title:"标记点"+l,note:""}}else a={type:"MarkSinglePoint",name:e.name||"point"+Date.now(),point:[...e.point],iconName:e.iconName||"#svgicon-point2",iconColor:e.iconColor||"#eeee00",iconOpacity:e.iconOpacity||.8,mainTagColor:e.mainTagColor||"#c4c4c4",mainTagBackground:e.mainTagBackground||"#2E2E30",mainTagOpacity:e.mainTagOpacity||.8,title:e.title||"标记点",note:e.note||""};const o=document.createElement("div");o.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${a.name}" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                <svg height="20" width="20" style="color:${a.iconColor};opacity:${a.iconOpacity};"><use href="${a.iconName}" fill="currentColor" /></svg>
                             </div>`,o.classList.add("mark-wrap-point",`mark-wrap-${a.name}`),o.style.position="absolute",o.style.borderRadius="4px",o.style.cursor="pointer",o.onclick=()=>{if(i.events.fire(F).markMode)return;const l=parent?.onActiveMark;l?.(i.getMarkData(!0)),i.events.fire(gt)},o.oncontextmenu=l=>l.preventDefault();const r=new yt(o);r.position.set(a.point[0],a.point[1],a.point[2]),r.element.style.pointerEvents="none",r.scale.set(.01,.01,.01),i.data=a,i.css3dTag=r,i.add(r),t.fire(Zt,i)}drawUpdate(t,e=!0){if(this.disposed)return;const s=this;if(t?.iconName){e&&(s.data.iconName=t.iconName);const i=this.css3dTag.element.querySelector(`.mark-wrap-${s.data.name} svg`);i.innerHTML=`<use href="${t.iconName}" fill="currentColor" />`}if(t?.iconColor){e&&(s.data.iconColor=t.iconColor);const i=this.css3dTag.element.querySelector(`.mark-wrap-${s.data.name} svg`);i.style.color=t.iconColor}if(t?.iconOpacity){e&&(s.data.iconOpacity=t.iconOpacity);const i=this.css3dTag.element.querySelector(`.mark-wrap-${s.data.name} svg`);i.style.opacity=t.iconOpacity.toString()}t?.mainTagColor&&(e&&(s.data.mainTagColor=t.mainTagColor),this.css3dTag.element.querySelector(`.${s.data.name}`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(s.data.mainTagBackground=t.mainTagBackground),this.css3dTag.element.querySelector(`.${s.data.name}`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(s.data.mainTagOpacity=t.mainTagOpacity),this.css3dTag.element.querySelector(`.${s.data.name}`).style.opacity=t.mainTagOpacity.toString()),t?.title!==void 0&&(e&&(s.data.title=t.title),this.css3dTag.element.querySelector(`.${s.data.name}`).innerText=t.title),t?.note!==void 0&&e&&(s.data.note=t.note),s.events.fire(Z)}resetMeterScale(t){t?.meterScale!==void 0&&(this.events.fire(F).meterScale=t.meterScale)}drawFinish(){if(this.disposed)return;const t=this;t.events.fire(jt);const e=parent?.onActiveMark,s=t.getMarkData(!0);s.isNew=!0,s.meterScale=t.events.fire(F).meterScale,e?.(s)}getMarkData(t=!1){const e={...this.data};return t?delete e.point:e.point=[...e.point],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Jt,t),t.events.fire(z).remove(t),t.events.fire(he,t);const e=document.querySelector(`.mark-wrap-${t.data.name}`);e?.parentElement?.removeChild?.(e),t.events=null,t.data=null,t.css3dTag=null}}class li extends ae{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.css3dTags=[],this.group=new A.Group,this.add(this.group),this.events=t}drawStart(t,e){if(this.disposed)return;const s=document.querySelectorAll(".mark-wrap-plans.main-warp").length+1,i={type:"MarkMultiPlans",name:e||"plans"+Date.now(),points:[...t.toArray(),...t.toArray()],lineColor:"#eeee00",lineWidth:3,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,areaTagColor:"#000000",areaTagBackground:"#e0ffff",areaTagOpacity:.8,distanceTagVisible:!0,areaTagVisible:!0,planOpacity:.5,title:"标记面"+s,note:""};this.draw(i)}drawUpdate(t,e=!0,s,i=!1){if(this.disposed)return;const a=this;if(s)if(i){const o=a.data.points.length,r=a.css3dTags.length-1,l=new A.Vector3().fromArray(a.data.points.slice(o-6,o-3)),u=s,c=new A.Vector3((l.x+u.x)/2,(l.y+u.y)/2,(l.z+u.z)/2),f=(l.distanceTo(u)*a.events.fire(F).meterScale).toFixed(2)+" m";a.css3dTags[r].element.innerText=f,a.css3dTags[r].position.set(c.x,c.y,c.z),a.css3dTags[r].visible=!0,a.data.points.pop(),a.data.points.pop(),a.data.points.pop(),a.data.points=[...a.data.points,...s.toArray(),...s.toArray()],this.draw(this.data)}else{const o=a.data.points.length;a.data.points[o-3]=s.x,a.data.points[o-2]=s.y,a.data.points[o-1]=s.z;const r=a.css3dTags.length-1,l=new A.Vector3().fromArray(a.data.points.slice(o-6,o-3)),u=new A.Vector3().fromArray(a.data.points.slice(o-3)),c=new A.Vector3((l.x+u.x)/2,(l.y+u.y)/2,(l.z+u.z)/2),d=l.distanceTo(u),f=(d*a.events.fire(F).meterScale).toFixed(2)+" m";a.css3dTags[r].element.innerText=f,a.geometry.setPositions([...a.data.points]),a.css3dTags[r].position.set(c.x,c.y,c.z),a.css3dTags[r].visible=i?!0:d>.5,a.meshPlans.geometry.setAttribute("position",new A.BufferAttribute(new Float32Array(a.data.points),3)),a.meshPlans.geometry.attributes.position.needsUpdate=!0;const m=a.events.fire(Jn,a.data.points);a.css3dAreaTag.position.copy(m);const C=a.events.fire(Kn,a.data.points);a.css3dAreaTag.element.childNodes[0].innerText=C.toFixed(2)+" m²"}if(t?.lineColor&&(e&&(a.data.lineColor=t.lineColor),a.material.color.set(t.lineColor),a.meshPlans.material.color.set(t.lineColor)),t?.lineWidth&&(e&&(a.data.lineWidth=t.lineWidth),a.material.linewidth=t.lineWidth),t?.mainTagColor&&(e&&(a.data.mainTagColor=t.mainTagColor),document.querySelector(`.${a.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(a.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${a.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(a.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${a.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(a.data.mainTagVisible=t.mainTagVisible),a.css3dMainTag.visible=t.mainTagVisible),t?.areaTagVisible!==void 0&&(e&&(a.data.areaTagVisible=t.areaTagVisible),a.css3dAreaTag.visible=t.areaTagVisible),t?.areaTagColor){e&&(a.data.areaTagColor=t.areaTagColor);const o=document.querySelector(`.${a.data.name}-area-tag`);o&&(o.style.color=t.areaTagColor)}if(t?.areaTagBackground){e&&(a.data.areaTagBackground=t.areaTagBackground);const o=document.querySelector(`.${a.data.name}-area-tag`);o&&(o.style.background=t.areaTagBackground)}if(t?.areaTagOpacity){e&&(a.data.areaTagOpacity=t.areaTagOpacity);const o=document.querySelector(`.${a.data.name}-area-tag`);o&&(o.style.opacity=t.areaTagOpacity.toString())}t?.distanceTagVisible!==void 0&&(e&&(a.data.distanceTagVisible=t.distanceTagVisible),a.css3dTags.forEach(o=>o.visible=t.distanceTagVisible)),t?.planOpacity&&(e&&(a.data.planOpacity=t.planOpacity),this.meshPlans.material.opacity=t.planOpacity),t?.title!==void 0&&(e&&(a.data.title=t.title),this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(a.data.note=t.note),a.events.fire(Z)}updateByMeterScale(t){const e=this,s=[];for(let r=0,l=e.data.points.length/3;r<l;r++)s.push(new A.Vector3(e.data.points[r*3],e.data.points[r*3+1],e.data.points[r*3+2]));let i,a;for(let r=1;r<s.length;r++)i=s[r-1],a=s[r],e.css3dTags[r-1].element.innerText=(i.distanceTo(a)*t).toFixed(2)+" m";const o=e.events.fire(pi,s,t);e.css3dAreaTag.element.childNodes[0].innerText=o.toFixed(2)+" m²"}drawFinish(t){if(this.disposed)return;const e=this,s=e.data.points.length,i=new A.Vector3().fromArray(e.data.points.slice(s-6,s-3)),a=new A.Vector3().fromArray(e.data.points.slice(s-3));if((!t||i.distanceTo(a)<1e-4)&&(e.data.points.pop(),e.data.points.pop(),e.data.points.pop()),e.events.fire(jt),e.data.points.length<9){e.dispose();return}for(;e.css3dTags.length>e.data.points.length/3-1;){const l=e.css3dTags.pop();e.group.remove(l),l.element.parentElement?.removeChild(l.element)}e.data.points.push(e.data.points[0],e.data.points[1],e.data.points[2]),e.draw(e.data,!0),e.css3dTags[e.css3dTags.length-1].visible=!0;const o=parent?.onActiveMark,r=e.getMarkData(!0);r.isNew=!0,r.meterScale=e.events.fire(F).meterScale,o?.(r)}draw(t,e=!1){if(this.disposed)return;const s=this;this.css3dTags=this.css3dTags||[];const i={type:"MarkMultiPlans",name:t.name||"plans"+Date.now(),points:[...t.points],lineColor:t.lineColor||"#eeee00",lineWidth:t.lineWidth||3,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,areaTagColor:t.areaTagColor||"#000000",areaTagBackground:t.areaTagBackground||"#e0ffff",areaTagOpacity:t.areaTagOpacity||.9,areaTagVisible:t.areaTagVisible===void 0?!0:t.areaTagVisible,distanceTagVisible:t.distanceTagVisible===void 0?!0:t.distanceTagVisible,planOpacity:t.planOpacity||.5,title:t.title||"标记面"+(document.querySelectorAll(".mark-wrap-plans.main-warp").length+1),note:t.note||""},a=s.geometry,o=s.material,r=new Oe;r.setPositions([...i.points]);const l=new we({color:i.lineColor,linewidth:i.lineWidth});l.resolution.set(innerWidth,innerHeight),s.copy(new ae(r,l)),a?.dispose(),o?.dispose();const u=i.name,c=i.points.length/3-1;if(!s.css3dMainTag){const m=document.createElement("div");m.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${u}-main-tag" style="color:${i.mainTagColor};background:${i.mainTagBackground};opacity:${i.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${i.title}</span>
                                     </div>`,m.classList.add("mark-wrap-plans",`${i.name}`,"main-warp"),m.style.position="absolute",m.style.borderRadius="4px",m.style.cursor="pointer",m.onclick=()=>{if(s.events.fire(F).markMode)return;const I=parent?.onActiveMark,p=s.getMarkData(!0);p.meterScale=s.events.fire(F).meterScale,I?.(p),s.events.fire(gt)},m.oncontextmenu=I=>I.preventDefault();const C=new yt(m);C.position.set(i.points[0],i.points[1],i.points[2]),C.element.style.pointerEvents="none",C.scale.set(.01,.01,.01),C.visible=i.mainTagVisible,s.group.add(C),s.css3dMainTag=C}const d=s.events.fire(Kn,i.points).toFixed(2)+" m²",f=s.events.fire(Jn,i.points);if(s.css3dAreaTag)s.css3dAreaTag.position.copy(f),s.css3dAreaTag.element.childNodes[0].innerText=d;else{const m=document.createElement("div");m.innerHTML=`<span class="${u}-area-tag" style="color:${i.areaTagColor};background:${i.areaTagBackground};opacity:${i.areaTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: none;">${d}</span>`,m.classList.add("mark-wrap-plans",`${i.name}`,"area-warp"),m.style.position="absolute",m.style.borderRadius="4px";const C=new yt(m);C.position.copy(f),C.element.style.pointerEvents="none",C.scale.set(.01,.01,.01),s.group.add(C),s.css3dAreaTag=C}s.css3dAreaTag.visible=i.points.length>6&&i.areaTagVisible;for(let m=s.css3dTags.length;m<c;m++){const C=new A.Vector3().fromArray(i.points.slice(m*3,m*3+3)),I=new A.Vector3().fromArray(i.points.slice(m*3+3,m*3+6)),p=new A.Vector3((C.x+I.x)/2,(C.y+I.y)/2,(C.z+I.z)/2),g=(C.distanceTo(I)*s.events.fire(F).meterScale).toFixed(2)+" m",v=document.createElement("div");v.innerText=g,v.style.color="white",v.classList.add("mark-wrap-plans",`${u}`,"distance-warp"),v.style.position="absolute",v.style.borderRadius="4px",v.style.display="none";const B=new yt(v);B.position.copy(p),B.element.style.pointerEvents="none",B.scale.set(.008,.008,.008),B.visible=i.distanceTagVisible,s.css3dTags.push(B),s.group.add(B)}s.drawPlans(i),e||(s.css3dTags[s.css3dTags.length-1].visible=!1),s.data=i,s.events.fire(Zt,s)}drawPlans(t){const e=this;if(!e.meshPlans){const s=new A.BufferGeometry;s.setAttribute("position",new A.BufferAttribute(new Float32Array,3)),s.attributes.position.needsUpdate=!0;const i=new A.BufferAttribute(new Uint16Array([]),1);s.setIndex(i);const a=new A.MeshBasicMaterial({color:t.lineColor,transparent:!0,opacity:t.planOpacity,side:A.DoubleSide});e.meshPlans=new A.Mesh(s,a),e.meshPlans.renderOrder=1,e.meshPlans.isMark=!0,e.group.add(e.meshPlans)}if(t.points.length>6){e.meshPlans.geometry.setAttribute("position",new A.BufferAttribute(new Float32Array(t.points),3)),e.meshPlans.geometry.attributes.position.needsUpdate=!0;let s=t.points.length/3-2;const i=new A.Vector3().fromArray(t.points.slice(0,3)),a=new A.Vector3().fromArray(t.points.slice(-3));i.distanceTo(a)<1e-4&&s--;const o=new Uint16Array(s*3);for(let r=0;r<s;r++)o[r*3]=0,o[r*3+1]=r+1,o[r*3+2]=r+2;e.meshPlans.geometry.setIndex(new A.BufferAttribute(o,1))}}getMarkData(t=!1){const e={...this.data};return t?delete e.points:e.points=[...e.points],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Jt,t),t.events.fire(z).remove(t),t.events.fire(he,t),t.geometry.dispose(),t.material.dispose(),document.querySelectorAll(`.${t.data.name}`).forEach(s=>s.parentElement?.removeChild(s)),t.events=null,t.data=null,t.css3dTags=null,t.group=null,t.meshPlans=null,t.css3dMainTag=null,t.css3dAreaTag=null}}class Pa extends A.Group{constructor(t){super(),this.isMark=!0,this.disposed=!1,this.events=t}drawStart(t,e){if(this.disposed)return;const s=this,i=document.querySelectorAll(".mark-wrap-circle.main-warp").length+1,a={type:"MarkCirclePlan",name:e||"circle"+Date.now(),startPoint:t.toArray(),radius:.05,circleColor:"#eeee00",circleOpacity:.5,mainTagColor:"#c4c4c4",mainTagBackground:"#2E2E30",mainTagOpacity:.8,mainTagVisible:!0,circleTagColor:"#000000",circleTagBackground:"#e0ffff",circleTagOpacity:.9,circleTagVisible:!0,title:"标记圆面"+i},o=new A.CircleGeometry(a.radius,32),r=new A.MeshBasicMaterial({color:a.circleColor,side:A.DoubleSide,transparent:!0});r.opacity=a.circleOpacity;const l=new A.Mesh(o,r);l.position.copy(t),l.isMark=!0,l.renderOrder=1;const u=new A.Vector3(0,1,0).normalize();l.lookAt(l.position.clone().add(u));const c=document.createElement("div");c.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`,c.classList.add("mark-wrap-circle",`${a.name}`,"main-warp"),c.style.position="absolute",c.style.borderRadius="4px",c.style.cursor="pointer",c.onclick=()=>{if(s.events.fire(F).markMode)return;const C=parent?.onActiveMark;C?.(s.getMarkData(!0)),s.events.fire(gt)},c.oncontextmenu=C=>C.preventDefault();const d=new yt(c);d.position.copy(t),d.element.style.pointerEvents="none",d.scale.set(.01,.01,.01),d.visible=a.mainTagVisible;const f=document.createElement("div");f.innerHTML=`<span class="${e}-circle-tag" style="color:${a.circleTagColor};background:${a.circleTagBackground};opacity:${a.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`,f.classList.add("mark-wrap-circle",`${e}`,"circle-warp"),f.style.position="absolute",f.style.borderRadius="4px",f.style.pointerEvents="none";const m=new yt(f);m.position.set(t.x+Math.min(a.radius/2,.5),t.y,t.z),m.element.style.pointerEvents="none",m.scale.set(.008,.008,.008),m.visible=!1,s.add(l,d,m),s.data=a,s.circleMesh=l,s.css3dTag=m,s.css3dMainTag=d,s.events.fire(Zt,s)}drawUpdate(t,e=!0,s){if(this.disposed)return;const i=this;if(s){const a=new A.Vector3().fromArray(i.data.startPoint),o=a.distanceTo(new A.Vector3(s.x,a.y,s.z));e&&(i.data.radius=o),i.circleMesh.geometry.copy(new A.CircleGeometry(o,128)),i.css3dTag.visible=o>.3;const r=o*i.events.fire(F).meterScale,l=(Math.PI*r*r).toFixed(2)+" m²";i.css3dTag.element.childNodes[0].innerText=l,i.css3dTag.position.set(a.x+Math.min(i.data.radius/2,.5),a.y,a.z)}if(t?.radius){const a=new A.Vector3().fromArray(i.data.startPoint),o=t?.radius;e&&(i.data.radius=o),i.circleMesh.geometry.copy(new A.CircleGeometry(o,128)),i.css3dTag.visible=o>.3;const r=o*i.events.fire(F).meterScale,l=(Math.PI*r*r).toFixed(2)+" m²";i.css3dTag.element.childNodes[0].innerText=l,i.css3dTag.position.set(a.x+Math.min(o/2,.5),a.y,a.z)}t?.circleColor&&(e&&(i.data.circleColor=t.circleColor),i.circleMesh.material.color.set(t.circleColor)),t?.circleOpacity&&(e&&(i.data.circleOpacity=t.circleOpacity),i.circleMesh.material.opacity=t.circleOpacity),t?.mainTagColor&&(e&&(i.data.mainTagColor=t.mainTagColor),document.querySelector(`.${i.data.name}-main-tag`).style.color=t.mainTagColor),t?.mainTagBackground&&(e&&(i.data.mainTagBackground=t.mainTagBackground),document.querySelector(`.${i.data.name}-main-tag`).style.background=t.mainTagBackground),t?.mainTagOpacity&&(e&&(i.data.mainTagOpacity=t.mainTagOpacity),document.querySelector(`.${i.data.name}-main-tag`).style.opacity=t.mainTagOpacity.toString()),t?.mainTagVisible!==void 0&&(e&&(i.data.mainTagVisible=t.mainTagVisible),i.css3dMainTag.visible=t.mainTagVisible),t?.circleTagColor&&(e&&(i.data.circleTagColor=t.circleTagColor),document.querySelector(`.${i.data.name}-circle-tag`).style.color=t.circleTagColor),t?.circleTagBackground&&(e&&(i.data.circleTagBackground=t.circleTagBackground),document.querySelector(`.${i.data.name}-circle-tag`).style.background=t.circleTagBackground),t?.circleTagOpacity&&(e&&(i.data.circleTagOpacity=t.circleTagOpacity),document.querySelector(`.${i.data.name}-circle-tag`).style.opacity=t.circleTagOpacity.toString()),t?.circleTagVisible!==void 0&&(e&&(i.data.circleTagVisible=t.circleTagVisible),i.css3dTag.visible=t.circleTagVisible),t?.title!==void 0&&(e&&(i.data.title=t.title),this.css3dMainTag.element.querySelector(`.${i.data.name}-main-tag`).innerText=t.title),t?.note!==void 0&&e&&(i.data.note=t.note),i.events.fire(Z)}updateByMeterScale(t){const e=this,s=e.data.radius*t,i=(Math.PI*s*s).toFixed(2)+" m²";e.css3dTag.element.childNodes[0].innerText=i}drawFinish(t){if(this.disposed)return;const e=this,s=new A.Vector3().fromArray(e.data.startPoint),i=s.distanceTo(new A.Vector3(t.x,s.y,t.z));e.data.radius=i,e.circleMesh.geometry.copy(new A.CircleGeometry(i,128));const a=e.data.radius*e.events.fire(F).meterScale,o=(Math.PI*a*a).toFixed(2)+" m²";e.css3dTag.element.childNodes[0].innerText=o,e.events.fire(jt);const r=parent?.onActiveMark,l=e.getMarkData(!0);l.isNew=!0,l.meterScale=e.events.fire(F).meterScale,r?.(l)}draw(t){if(this.disposed)return;const e=this,s={type:"MarkCirclePlan",name:t.name||"circle"+Date.now(),startPoint:[...t.startPoint],radius:t.radius,circleColor:t.circleColor||"#eeee00",circleOpacity:t.circleOpacity||.5,mainTagColor:t.mainTagColor||"#c4c4c4",mainTagBackground:t.mainTagBackground||"#2E2E30",mainTagOpacity:t.mainTagOpacity||.8,mainTagVisible:t.mainTagVisible===void 0?!0:t.mainTagVisible,circleTagColor:t.circleTagColor||"#000000",circleTagBackground:t.circleTagBackground||"#e0ffff",circleTagOpacity:t.circleTagOpacity||.9,circleTagVisible:t.circleTagVisible===void 0?!0:t.circleTagVisible,title:t.title||"标记圆面"},i=new A.CircleGeometry(s.radius,128),a=new A.MeshBasicMaterial({color:s.circleColor,side:A.DoubleSide,transparent:!0});a.opacity=s.circleOpacity;const o=new A.Mesh(i,a);o.position.fromArray(s.startPoint),o.isMark=!0,o.renderOrder=1;const r=new A.Vector3(0,1,0).normalize();o.lookAt(o.position.clone().add(r));const l=s.name,u=document.createElement("div");u.innerHTML=`<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${l}-main-tag" style="color:${s.mainTagColor};background:${s.mainTagBackground};opacity:${s.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${s.title}</span>
                             </div>`,u.classList.add("mark-wrap-circle",`${s.name}`,"main-warp"),u.style.position="absolute",u.style.borderRadius="4px",u.style.cursor="pointer",u.onclick=()=>{if(e.events.fire(F).markMode)return;const p=parent?.onActiveMark;p?.(e.getMarkData(!0)),e.events.fire(gt)},u.oncontextmenu=p=>p.preventDefault();const c=new A.Vector3().fromArray(s.startPoint),d=s.radius*e.events.fire(F).meterScale,f=(Math.PI*d*d).toFixed(2)+" m²",m=new yt(u);m.position.copy(c),m.element.style.pointerEvents="none",m.scale.set(.01,.01,.01),m.visible=s.mainTagVisible;const C=document.createElement("div");C.innerHTML=`<span class="${l}-distance-tag ${l}-circle-tag" style="color:${s.circleTagColor};background:${s.circleTagBackground};opacity:${s.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${f}</span>`,C.classList.add("mark-wrap-circle",`${l}`,"circle-warp"),C.style.position="absolute",C.style.borderRadius="4px",C.style.pointerEvents="none";const I=new yt(C);I.position.set(c.x+Math.min(s.radius/2,.5),c.y,c.z),I.element.style.pointerEvents="none",I.scale.set(.008,.008,.008),I.visible=s.circleTagVisible,e.add(o,m,I),e.data=s,e.circleMesh=o,e.css3dTag=I,e.css3dMainTag=m,e.events.fire(Zt,e)}getMarkData(t=!1){const e={...this.data};return t?(delete e.startPoint,delete e.radius):e.startPoint=[...e.startPoint],e}dispose(){if(this.disposed)return;const t=this;t.disposed=!0,t.events.fire(Jt,t),t.events.fire(z).remove(t),t.events.fire(he,t),document.querySelectorAll(`.${t.data.name}`).forEach(s=>s.parentElement?.removeChild(s)),t.events=null,t.data=null,t.circleMesh=null,t.css3dTag=null,t.css3dMainTag=null}}const Nl=(()=>{const n=new kn;let t=!1;n.on(pr,async(s="",i=!1,a=.5)=>{const o=e();let r=null;if(new Promise(c=>r=c),o.isPlaying){t?(o?.stop(),r(!1)):r(!0);return}const l=await Kt(s||"https://reall3d.com/demo-models/demo/background1.mp3"),u=URL.createObjectURL(new Blob([l],{type:"application/octet-stream"}));new A.AudioLoader().load(u,c=>{t||(o.setBuffer(c),o.setLoop(i),o.setVolume(a),o.play()),r(!t)},()=>r(!1))}),n.on(Mo,()=>{const s=e(!1);s?.isPlaying&&s.stop()}),n.on(Cr,(s=!0)=>((s===!0||s===!1)&&(t=s),t)),n.on(hr,()=>{const s=e();if(!s?.isPlaying)return;let i=s.getVolume();Po(()=>{s&&(t?s.stop():s.setVolume(i=Math.max(i*.99,.15)))},()=>i>.15)}),n.on(mr,()=>{const s=e();if(!s?.isPlaying)return;let i=s.getVolume();Po(()=>{s&&(t?s.stop():s.setVolume(i=Math.min(i*1.01,.5)))},()=>i<.5)});function e(s=!0){if(t)return null;let i=n.tryFire(So);return!i&&s&&(i=new A.Audio(new A.AudioListener),n.on(So,()=>i)),i}return n})();let Wl=class{constructor(){this.down=0,this.move=!1,this.downTime=0,this.isDbClick=!1,this.x=0,this.y=0,this.lastClickX=0,this.lastClickY=0,this.lastClickPointTime=0,this.lastMovePoint=null,this.lastMovePointTime=0,this.touchStartDistance=0,this.touchPrevDistance=0,this.touchStartX1=0,this.touchStartY1=0,this.touchStartX2=0,this.touchStartY2=0,this.touchPrevX1=0,this.touchPrevY1=0,this.touchPrevX2=0,this.touchPrevY2=0}};function Hl(n){const t=(y,E,Q)=>n.on(y,E,Q),e=(y,...E)=>n.fire(y,...E),s=e(Ae);let i=new Set,a,o=new Wl,r,l=0,u=0;function c(){const y=e(q),E=new A.Vector3;y.getWorldDirection(E),l=Math.asin(E.y),u=Math.atan2(E.z,E.x)}t($n,()=>{const y=e(W),E=e(F);y.autoRotate=E.autoRotate=!0,y.autoRotate,E.autoRotate}),t(gt,(y=!0)=>{const E=e(W),Q=e(F);E.autoRotate=Q.autoRotate=!1,E.autoRotate,Q.autoRotate,y&&e(yn)}),t(rs,(y=!0)=>{if(a)return;const E=e(W),Q=y?Math.PI/128:-(Math.PI/128),w=new A.Matrix4().makeRotationAxis(new A.Vector3(0,0,-1).transformDirection(E.object.matrixWorld),Q);E.object.up.transformDirection(w),e(Z)}),t(cs,()=>{if(a)return;if(e(F).useCustomControl===!0){const Q=e(q),w=e(z);let D;if(w.traverse(S=>{S instanceof Bt&&(D=S)}),D){const S=D.position.clone(),T=Q.position.clone(),L=S.sub(T).normalize(),_=-(Math.PI/128),V=new A.Quaternion().setFromAxisAngle(L,_);D.applyQuaternion(V)}e(Z)}else n.fire(rs,!0)}),t(ls,()=>{if(a)return;if(e(F).useCustomControl===!0){const Q=e(q),w=e(z);let D;if(w.traverse(S=>{S instanceof Bt&&(D=S)}),D){const S=D.position.clone(),T=Q.position.clone(),L=S.sub(T).normalize(),_=Math.PI/128,V=new A.Quaternion().setFromAxisAngle(L,_);D.applyQuaternion(V)}e(Z)}else n.fire(rs,!1)}),t(fn,y=>{const E=e(F);y??(y=!E.pointcloudMode),E.pointcloudMode=y,e(z).traverse(w=>w instanceof Bt&&w.fire(ee,y))}),t(pn,()=>{e(z).traverse(E=>E instanceof Bt&&E.fire(sn))}),t(Me,y=>{e(z).traverse(Q=>Q instanceof Bt&&Q.fire(Me,y))}),t(Ao,()=>{const y=e(z);let E;y.traverse(Q=>Q instanceof Bt&&(E=Q)),E&&e(jn,E.fire(lo))}),t(En,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=e(F),D=w.useCustomControl===!0,S=y!==void 0?y:w.cameraMoveSpeed!==void 0?w.cameraMoveSpeed:.005,T=new A.Vector3;if(E.getWorldDirection(T),E.position.addScaledVector(T,S),D){const L=new A.Vector3().copy(E.position).add(T);Q.target.copy(L)}else Q.target.addScaledVector(T,S);e(Z)}),t(Sn,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=e(F),D=y!==void 0?y:w.cameraMoveSpeed!==void 0?w.cameraMoveSpeed:.005,S=new A.Vector3;E.getWorldDirection(S),E.position.addScaledVector(S,-D),Q.target.addScaledVector(S,-D),e(Z)}),t(Pe,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=e(F),D=y!==void 0?y:w.cameraMoveSpeed!==void 0?w.cameraMoveSpeed:.005,S=new A.Vector3,T=new A.Vector3(0,1,0),L=new A.Vector3;E.getWorldDirection(L),S.crossVectors(T,L).normalize(),E.position.addScaledVector(S,-D),Q.target.addScaledVector(S,-D),e(Z)}),t(Ve,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=e(F),D=y!==void 0?y:w.cameraMoveSpeed!==void 0?w.cameraMoveSpeed:.005,S=new A.Vector3,T=new A.Vector3(0,1,0),L=new A.Vector3;E.getWorldDirection(L),S.crossVectors(T,L).normalize(),E.position.addScaledVector(S,D),Q.target.addScaledVector(S,D),e(Z)}),t(Mn,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=e(F),D=w.useCustomControl===!0,S=y!==void 0?y:w.cameraMoveSpeed!==void 0?w.cameraMoveSpeed:.005;if(D){const T=new A.Vector3(0,1,0).applyQuaternion(E.quaternion);T.toArray(),E.position.addScaledVector(T,S);const L=new A.Vector3;E.getWorldDirection(L);const _=new A.Vector3().copy(E.position).add(L);Q.target.copy(_)}else{const T=new A.Vector3(0,1,0);E.position.addScaledVector(T,-S),Q.target.addScaledVector(T,-S)}e(Z)}),t(vn,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=e(F),D=w.useCustomControl===!0,S=y!==void 0?y:w.cameraMoveSpeed!==void 0?w.cameraMoveSpeed:.005;if(D){const T=new A.Vector3(0,1,0).applyQuaternion(E.quaternion);T.toArray(),E.position.addScaledVector(T,-S);const L=new A.Vector3;E.getWorldDirection(L);const _=new A.Vector3().copy(E.position).add(L);Q.target.copy(_)}else{const T=new A.Vector3(0,1,0);E.position.addScaledVector(T,S),Q.target.addScaledVector(T,S)}e(Z)}),t(xn,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=E.position.clone();l+=y!==void 0?y:.05,l=Math.min(Math.PI/2-.1,Math.max(-Math.PI/2+.1,l));const S=new A.Vector3(Math.cos(l)*Math.cos(u),Math.sin(l),Math.cos(l)*Math.sin(u)).normalize(),T=new A.Vector3().copy(w).add(S);Q.target.copy(T),E.lookAt(T),E.position.copy(w),e(Z)}),t(Bn,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=E.position.clone();l-=y!==void 0?y:.05,l=Math.min(Math.PI/2-.1,Math.max(-Math.PI/2+.1,l));const S=new A.Vector3(Math.cos(l)*Math.cos(u),Math.sin(l),Math.cos(l)*Math.sin(u)).normalize(),T=new A.Vector3().copy(w).add(S);Q.target.copy(T),E.lookAt(T),E.position.copy(w),e(Z)}),t(Qn,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=E.position.clone();w.toArray(),u+=y!==void 0?y:.05;const S=new A.Vector3(Math.cos(l)*Math.cos(u),Math.sin(l),Math.cos(l)*Math.sin(u)).normalize(),T=new A.Vector3().copy(w).add(S);Q.target.copy(T),E.lookAt(T),E.position.copy(w),E.position.toArray(),Q.minDistance=Q.maxDistance=Q.getDistance(),e(Z)}),t(Dn,(y=void 0)=>{if(a)return;const E=e(q),Q=e(W),w=E.position.clone();u-=y!==void 0?y:.05;const S=new A.Vector3(Math.cos(l)*Math.cos(u),Math.sin(l),Math.cos(l)*Math.sin(u)).normalize(),T=new A.Vector3().copy(w).add(S);Q.target.copy(T),E.lookAt(T),E.position.copy(w),Q.minDistance=Q.maxDistance=Q.getDistance(),e(Z)}),t(an,()=>{if(!i.size)return;const y=e(F);if(!y.enableKeyboard)return i.clear();if(y.markMode&&i.has("Escape")){e(ke),i.clear();return}const E=y.useCustomControl===!0,Q=i.has("Shift");Q&&E?(i.has("KeyW")&&e(Bn,.01),i.has("KeyS")&&e(xn,.01),i.has("KeyA")&&e(Qn,.01),i.has("KeyD")&&e(Dn,.01)):E&&!Q&&(i.has("KeyW")&&e(En),i.has("KeyS")&&e(Sn),i.has("KeyA")&&e(Pe),i.has("KeyD")&&e(Ve),i.has("ArrowLeft")&&e(Pe),i.has("ArrowRight")&&e(Ve),i.has("ArrowUp")&&e(Mn),i.has("ArrowDown")&&e(vn)),i.has("Space")?(y.bigSceneMode?e(fn):e(pn),i.clear()):i.has("Escape")?(Nl.fire(Mo),i.clear()):i.has("KeyR")?(y.autoRotate?e(gt):e($n),i.clear()):i.has("KeyM")?(e(De,!y.markVisible),i.clear()):E&&i.has("KeyQ")?(e(cs),i.clear()):E&&i.has("KeyE")?(e(ls),i.clear()):!E&&i.has("ArrowLeft")?(e(cs),e(An,!0),i.clear()):!E&&i.has("ArrowRight")?(e(ls),e(An,!0),i.clear()):i.has("KeyP")?(e(qt,!0),i.clear()):i.has("Equal")?(e(Te),i.clear()):i.has("Minus")?(e(Le),i.clear()):i.has("KeyY")?(e(Re,!1),e(Cs),i.clear()):i.has("KeyI")?(e(io),i.clear()):i.has("KeyF")?(e(Ao),i.clear()):i.has("F2")?(!y.bigSceneMode&&window.open("/editor/index.html?url="+encodeURIComponent(e(bt).meta.url)),i.clear()):i.has("KeyW")?(zl(e(W),.15),i.clear()):i.has("KeyS")?(Ol(e(W),.15),i.clear()):i.has("KeyA")?(Gl(e(W),.15),i.clear()):i.has("KeyD")?(Yl(e(W),.15),i.clear()):i.has("KeyQ")?(Xl(e(W)),i.clear()):i.has("KeyE")?(Jl(e(W)),i.clear()):i.has("KeyC")?(Kl(e(W)),i.clear()):i.has("KeyZ")&&(Zl(e(W)),i.clear())}),t(cn,async(y,E)=>{if(o.move||e(F).useCustomControl===!0)return;const D=await e(rn,y,E);D.length&&(D[0].toArray(),e(jn,D[0],!0,!1))}),t(kt,async(y,E)=>{const Q=e(z),w=[];Q.traverse(S=>S instanceof Bt&&w.push(S));const D=await e(rn,y,E);return D.length?(w.length&&w[0].fire(Ze,D[0].x,D[0].y,D[0].z,!0),new A.Vector3(D[0].x,D[0].y,D[0].z)):(w.length&&w[0].fire(Ze,0,0,0,!1),null)}),t(ds,async()=>{const y=e(z),E=[];y.traverse(Q=>Q instanceof Bt&&E.push(Q));for(let Q=0;Q<E.length;Q++)E[Q].fire(Ze,0,0,0,!1)});const d=y=>{y.target.type!=="text"&&(a||y.code==="F5"||(y.preventDefault(),y.code!=="KeyR"&&(i.add(y.code),(y.code==="ShiftLeft"||y.code==="ShiftRight")&&i.add("Shift"),e(gt)),r=Date.now()))},f=y=>{y.target.type!=="text"&&(a||(y.code==="KeyR"&&i.add(y.code),(y.code==="ArrowLeft"||y.code==="ArrowRight")&&e(yi),i.delete(y.code),(y.code==="ShiftLeft"||y.code==="ShiftRight")&&i.delete("Shift"),r=Date.now()))};t(pe,()=>{e(ps)&&Date.now()-r>2e3&&(e(An,!1),e(Cs));const y=e(F),E=e(W);y.autoRotate&&!E.autoRotate&&Date.now()-r>3e3&&(y.useCustomControl||e($n))},!0);const m=()=>{i.clear()},C=y=>{if(parent&&setTimeout(()=>window.focus()),y.preventDefault(),a)return;if(e(gt),e(F).useCustomControl===!0){const w=e(q),D=e(W),S=y.deltaY>0?1.1:.9,T=w.position.clone().sub(D.target).normalize(),L=w.position.distanceTo(D.target),_=Math.max(.1,Math.min(1e3,L*S)),V=D.target.clone().add(T.multiplyScalar(_));w.position.copy(V),e(Z),_.toFixed(2)}r=Date.now()},I=y=>{y.preventDefault(),!a&&(e(gt),r=Date.now())};let p,h,g,v;t(ke,()=>{p?.dispose(),h?.dispose(),g?.dispose(),v?.dispose(),p=null,h=null,g=null,v=null,o.lastMovePoint=null,e(jt)});const B=async y=>{if(parent&&setTimeout(()=>window.focus()),y.preventDefault(),a)return;e(gt),y.button===1?o.down=3:o.down=y.button===2?2:1,o.move=!1,o.isDbClick=Date.now()-o.downTime<300,o.x=y.clientX,o.y=y.clientY;const E=e(F);if(E.useCustomControl===!0){const w=e(q),D=w.position.clone();D.toArray();const S=e(W);S.enabled=!1,S.minDistance=S.maxDistance=S.getDistance(),S.updateControlMode(E),o.down,o.down===1&&(y.clientX,y.clientY,Math.abs(l)<.001&&Math.abs(u)<.001&&c(),w.position.copy(D),y.stopPropagation())}r=Date.now(),o.downTime=Date.now()},R=async y=>{if(y.preventDefault(),a)return;const E=e(F),Q=E.useCustomControl===!0;if(o.down){const w=y.clientX-o.x,D=y.clientY-o.y;if(o.down===3){y.stopPropagation();const S=e(W);S.enabled=!1;const T=E.cameraMoveSpeed!==void 0?E.cameraMoveSpeed:.005,L=.5;Math.abs(D)>2&&(D>0?e(Sn,Math.abs(D)*L*T):e(En,Math.abs(D)*L*T)),o.x=y.clientX,o.y=y.clientY,e(Z),o.move=!0,r=Date.now();return}if(o.down===2&&Q){y.stopPropagation();const S=e(W);S.enabled=!1;const T=E.cameraMoveSpeed!==void 0?E.cameraMoveSpeed:.005,L=.5;Math.abs(w)>2&&(w>0?e(Pe,Math.abs(w)*L*T):e(Ve,Math.abs(w)*L*T)),Math.abs(D)>2&&(D>0?e(Mn,Math.abs(D)*L*T):e(vn,Math.abs(D)*L*T)),o.x=y.clientX,o.y=y.clientY,e(Z),o.move=!0,r=Date.now();return}if(Q&&o.down===1){y.stopPropagation();const S=e(W);S.enabled=!1;const T=e(q),L=T.position.clone(),_=.001,V=.001;Math.abs(l)<.001&&Math.abs(u)<.001&&c(),Math.abs(w)>0&&(w>0?e(Dn,Math.abs(w)*_):e(Qn,Math.abs(w)*_)),Math.abs(D)>0&&(D>0?e(xn,Math.abs(D)*V):e(Bn,Math.abs(D)*V)),T.position.copy(L),S.minDistance=S.maxDistance=S.getDistance(),o.x=y.clientX,o.y=y.clientY,e(Z),o.move=!0,r=Date.now();return}o.move=!0,r=Date.now()}if(!Q&&E.markMode){const w=await e(kt,y.clientX,y.clientY);w&&!o.down&&E.markType==="distance"&&p?p.drawUpdate({endPoint:w.toArray()}):!o.down&&E.markType==="circle"&&v?w?(v.drawUpdate(null,!0,w),o.lastMovePoint=w,o.lastMovePointTime=Date.now()):(o.lastMovePoint=null,o.lastMovePointTime=0):!o.down&&E.markType==="lines"&&h?w?(h.drawUpdate(null,!0,w),o.lastMovePoint=w,o.lastMovePointTime=Date.now()):(o.lastMovePoint=null,o.lastMovePointTime=0):!o.down&&E.markType==="plans"&&g&&(w?(g.drawUpdate(null,!0,w),o.lastMovePoint=w,o.lastMovePointTime=Date.now()):(o.lastMovePoint=null,o.lastMovePointTime=0))}},x=async y=>{if(y.preventDefault(),a)return;const E=e(F),Q=E.useCustomControl===!0;if(Q){const w=e(q),D=e(W),S=w.position.clone();o.down,o.move,D.enabled=!1,D.updateControlMode(E),D.minDistance=D.maxDistance=D.getDistance(),w.position.copy(S),w.position.toArray(),y.stopPropagation()}else if(Q&&o.down===2&&o.move){const w=e(W);w.enabled=!0,w.update(),y.stopPropagation()}else if(o.down===3&&o.move){const w=e(W);w.enabled=!0,w.update(),y.stopPropagation()}if(o.isDbClick&&(h?Math.abs(y.clientX-o.lastClickX)<2&&Math.abs(y.clientY-o.lastClickY)<2&&(h.drawFinish(o.lastClickPointTime>0),h=null,o.lastMovePoint=null):g&&Math.abs(y.clientX-o.lastClickX)<2&&Math.abs(y.clientY-o.lastClickY)<2&&(g.drawFinish(o.lastClickPointTime>0),g=null,o.lastMovePoint=null)),E.markMode&&o.down===1&&!o.move&&Date.now()-o.downTime<500){if(E.markType==="point"){if(await e(kt,y.clientX,y.clientY)){const D=new Fa(n,await e(kt,y.clientX,y.clientY));e(z).add(D),D.drawFinish()}}else if(E.markType==="distance")if(p){const w=await e(kt,y.clientX,y.clientY);w?(p.drawFinish(w),p=null):o.isDbClick&&e(ke)}else{const w=await e(kt,y.clientX,y.clientY);w&&(p=new ri(n),p.drawStart(w),e(z).add(p))}else if(E.markType==="lines")if(h)if(o.lastMovePoint&&e(us,y.clientX,y.clientY,o.lastMovePoint)<.03)h.drawUpdate(null,!0,o.lastMovePoint,!0),o.lastClickPointTime=Date.now();else{const w=await e(kt,y.clientX,y.clientY);w?(h.drawUpdate(null,!0,w,!0),o.lastClickPointTime=Date.now()):o.lastClickPointTime=0}else{const w=await e(kt,y.clientX,y.clientY);w&&(h=new ci(n),h.drawStart(w),e(z).add(h))}else if(E.markType==="plans")if(g)if(o.lastMovePoint&&e(us,y.clientX,y.clientY,o.lastMovePoint)<.03)g.drawUpdate(null,!0,o.lastMovePoint,!0),o.lastClickPointTime=Date.now();else{const w=await e(kt,y.clientX,y.clientY);w?(g.drawUpdate(null,!0,w,!0),o.lastClickPointTime=Date.now()):o.lastClickPointTime=0}else{const w=await e(kt,y.clientX,y.clientY);w&&(g=new li(n),g.drawStart(w),e(z).add(g))}else if(E.markType==="circle")if(v){const w=await e(kt,y.clientX,y.clientY);w?(v.drawFinish(w),v=null):o.isDbClick&&e(ke)}else{const w=await e(kt,y.clientX,y.clientY);w&&(v=new Pa(n),v.drawStart(w),e(z).add(v))}o.lastClickX=y.clientX,o.lastClickY=y.clientY}o.down===2&&!o.move&&(E.useCustomControl===!0||e(cn,y.clientX,y.clientY)),o.down=0,o.move=!1,r=Date.now()};function k(y){if(y.preventDefault(),a)return;e(gt),o.down=y.touches.length;const Q=e(F).useCustomControl===!0;if(o.down===1){if(o.move=!1,o.x=y.touches[0].clientX,o.y=y.touches[0].clientY,Q){Math.abs(l)<.001&&Math.abs(u)<.001&&c();const w=e(W);w.enabled=!1}}else if(o.down===2&&Q){const w=y.touches[0],D=y.touches[1];o.touchStartX1=w.clientX,o.touchStartY1=w.clientY,o.touchStartX2=D.clientX,o.touchStartY2=D.clientY,o.touchPrevX1=w.clientX,o.touchPrevY1=w.clientY,o.touchPrevX2=D.clientX,o.touchPrevY2=D.clientY;const S=w.clientX-D.clientX,T=w.clientY-D.clientY;o.touchStartDistance=Math.sqrt(S*S+T*T),o.touchPrevDistance=o.touchStartDistance,o.touchStartDistance;const L=e(W);L.enabled=!1}r=Date.now()}function M(y){if(y.preventDefault(),a)return;const E=e(F),Q=E.useCustomControl===!0;if(y.touches.length,y.touches.length===1){if(o.move=!0,Q){const w=y.touches[0],D=w.clientX-o.x,S=w.clientY-o.y,T=e(q),L=e(W),_=T.position.clone(),V=.001,N=.001;Math.abs(D)>0&&(D<0?e(Qn,Math.abs(D)*V):e(Dn,Math.abs(D)*V)),Math.abs(S)>0&&(S<0?e(Bn,Math.abs(S)*N):e(xn,Math.abs(S)*N)),T.position.copy(_),L.minDistance=L.maxDistance=L.getDistance(),o.x=w.clientX,o.y=w.clientY,e(Z)}}else if(y.touches.length===2&&Q){console.warn("[TouchMove] 双指移动 - 在自定义控制模式下处理");const w=y.touches[0],D=y.touches[1],S=w.clientX-D.clientX,T=w.clientY-D.clientY,L=Math.sqrt(S*S+T*T),_=L-o.touchPrevDistance,V=(w.clientX+D.clientX)/2,N=(w.clientY+D.clientY)/2,tt=(o.touchPrevX1+o.touchPrevX2)/2,Y=(o.touchPrevY1+o.touchPrevY2)/2,X=V-tt,H=N-Y,K=E.cameraMoveSpeed!==void 0?E.cameraMoveSpeed:.05,it=.1,ut=Math.abs(_),mt=Math.abs(X),Qt=Math.abs(H);if(ut>1){const ct=ut*it*K;_>0?e(En,ct):e(Sn,ct)}if(mt>1||Qt>1){if(mt>Qt&&mt>1){const ct=mt*it*K;X<0?e(Ve,ct):e(Pe,ct)}else if(Qt>1){const ct=Qt*it*K;H<0?e(vn,ct):e(Mn,ct)}}o.touchPrevX1=w.clientX,o.touchPrevY1=w.clientY,o.touchPrevX2=D.clientX,o.touchPrevY2=D.clientY,o.touchPrevDistance=L,e(Z)}r=Date.now()}function P(y){if(a)return;if(e(F).useCustomControl===!0){const w=e(W);w.enabled=!1}y.touches.length===0?(o.down=0,o.move=!1):o.down=y.touches.length,r=Date.now()}window.addEventListener("keydown",d),window.addEventListener("keyup",f),window.addEventListener("blur",m),window.addEventListener("wheel",C,{passive:!1}),s.addEventListener("contextmenu",I),s.addEventListener("mousedown",B),s.addEventListener("mousemove",R),s.addEventListener("mouseup",x),s.addEventListener("touchstart",k,{passive:!1}),s.addEventListener("touchmove",M,{passive:!1}),s.addEventListener("touchend",P,{passive:!1}),window.addEventListener("resize",G),G();function G(){const{width:y,height:E,top:Q,left:w}=e(ne),D=e(q);D.aspect=y/E,D.updateProjectionMatrix();const S=e(hs);S.setSize(y,E),S.domElement.style.position="absolute",S.domElement.style.left=`${w}px`,S.domElement.style.top=`${Q}px`;const T=e(xe);T.setPixelRatio(Math.min(devicePixelRatio,2)),T.setSize(y,E)}t(qn,()=>{a=!0,window.removeEventListener("keydown",d),window.removeEventListener("keyup",f),window.removeEventListener("blur",m),window.removeEventListener("wheel",C),s.removeEventListener("contextmenu",I),s.removeEventListener("mousedown",B),s.removeEventListener("mousemove",R),s.removeEventListener("mouseup",x),s.removeEventListener("touchstart",k),s.removeEventListener("touchmove",M),s.removeEventListener("touchend",P),window.removeEventListener("resize",G)})}function zn(n,t,e=.012){const s=n.object.up.clone().normalize(),a=new A.Vector3().copy(t).projectOnPlane(s).normalize().multiplyScalar(e);n.object.position.add(a),n.target.add(a),n.update()}function Gl(n,t=.2){const e=new A.Vector3;n.object.getWorldDirection(e);const s=new A.Vector3().crossVectors(n.object.up,e).normalize();zn(n,s,t)}function Yl(n,t=.2){const e=new A.Vector3;n.object.getWorldDirection(e);const s=new A.Vector3().crossVectors(n.object.up,e).normalize().negate();zn(n,s,t)}function zl(n,t=.2){const e=new A.Vector3;n.object.getWorldDirection(e),zn(n,e,t)}function Ol(n,t=.2){const e=new A.Vector3;n.object.getWorldDirection(e).negate(),zn(n,e,t)}function Va(n,t=.006){const e=n.object.position.clone(),s=n.target.clone(),i=new A.Vector3().subVectors(s,e),a=n.object.up.clone().normalize(),o=new A.Quaternion;o.setFromAxisAngle(a,-t),i.applyQuaternion(o);const r=new A.Vector3().addVectors(e,i);n.target.copy(r),n.update()}function Xl(n){Va(n,-.01)}function Jl(n){Va(n,.01)}function _a(n,t=.006){const e=n.object.position.clone(),s=n.target.clone(),i=new A.Vector3().subVectors(s,e),a=new A.Vector3;n.object.getWorldDirection(a);const o=new A.Vector3().crossVectors(n.object.up,a).normalize(),r=new A.Quaternion;r.setFromAxisAngle(o,t),i.applyQuaternion(r);const l=new A.Vector3().addVectors(e,i);n.target.copy(l),n.update()}function Kl(n,t=.01){_a(n,-t)}function Zl(n,t=.01){_a(n,t)}function Ua(n){const t=new A.Raycaster,e=5,s=(a,o,r)=>n.on(a,o,r),i=(a,...o)=>n.fire(a,...o);s(rn,async(a,o)=>{const{width:r,height:l,left:u,top:c}=i(ne),d=new A.Vector2;d.x=(a-u)/r*2-1,d.y=(c-o)/l*2+1;const f=(d.x+1)/2*r,m=(1-d.y)/2*l,C=i(q);t.setFromCamera(d,C);const I=[],p=i(z),h=[],g=[];p.traverse(function(k){k instanceof Bt?g.push(k):k.isMesh&&!k.ignoreIntersect&&!k.isMark&&h.push(k)});const v=t.intersectObjects(h,!0);for(let k=0;k<v.length;k++)I.push({point:v[k].point,d:t.ray.distanceToPoint(v[k].point),p:1});const B=i(go),R=C.projectionMatrix.clone().multiply(C.matrixWorldInverse);for(let k=0;k<g.length;k++){const M=g[k].fire(As);if(M)if(M.length!==void 0){const P=M,G=P.length/3;for(let y=0;y<G;y++){const E=new A.Vector3(P[3*y+0],P[3*y+1],P[3*y+2]);B&&E.applyMatrix4(B);const Q=new A.Vector4(E.x,E.y,E.z,1).applyMatrix4(R),w=(Q.x/Q.w+1)/2*r,D=(1-Q.y/Q.w)/2*l,S=Math.sqrt((w-f)**2+(D-m)**2);S<=e&&I.push({point:E,d:C.position.distanceTo(E),p:S})}}else for(let P of Object.keys(M)){const G=P.split(","),y=new A.Vector3(Number(G[0]),Number(G[1]),Number(G[2]));if(t.ray.distanceToPoint(y)<=1.4143){const E=M[P];for(let Q=0,w=E.length/3;Q<w;Q++){const D=new A.Vector3(E[3*Q+0],E[3*Q+1],E[3*Q+2]);B&&D.applyMatrix4(B);const S=new A.Vector4(D.x,D.y,D.z,1).applyMatrix4(R),T=(S.x/S.w+1)/2*r,L=(1-S.y/S.w)/2*l,_=Math.sqrt((T-f)**2+(L-m)**2);_<=e&&I.push({point:D,d:C.position.distanceTo(D),p:_})}}}}if(!I.length)return[];I.sort((k,M)=>k.d-M.d);const x=[];for(let k=0,M=I[0].d,P=Math.min(I.length,20);k<P;k++)I[k].d-M<.01&&x.push(I[k]);return x.sort((k,M)=>k.p-M.p),[x[0].point]}),s(us,(a,o,r)=>{const{width:l,height:u,left:c,top:d}=i(ne),f=new A.Vector2;f.x=(a-c)/l*2-1,f.y=(d-o)/u*2+1;const m=i(q);return t.setFromCamera(f,m),t.ray.distanceToPoint(r)})}function jl(n){const t=(f,m,C)=>n.on(f,m,C),e=(f,...m)=>n.fire(f,...m),s=e(W),i=s.target.clone();e(z).traverse(f=>{f.fire&&typeof f.fire=="function"&&f.fire(Fi,i.x,i.y,i.z)}),t(Se,()=>e(q).fov),t(Lt,(f=!1)=>f?s.object.position.clone():s.object.position),t(_t,(f=!1)=>f?s.target.clone():s.target),t(ue,(f=!1)=>f?e(q).up.clone():e(q).up);let o;const r=[];t(jn,(f,m=!1,C)=>{if(e(Xi,f),!m){s.target.copy(f);const B=new A.Vector3().subVectors(f,s.object.position);B.length()<1&&s.object.position.copy(f).sub(B.setLength(1)),B.length()>50&&s.object.position.copy(f).sub(B.setLength(50)),e(un),e(fs);return}for(;r.length;)r.pop().stop=!0;let I={alpha:0,time:Date.now(),stop:!1};r.push(I),o=o||{enablePan:s.enablePan,enableRotate:s.enableRotate},s.enablePan=!1,s.enableRotate=!1;const p=e(_t,!0),h=e(Lt,!0),g=p.clone().sub(h).normalize(),v=f.clone().sub(g.multiplyScalar(f.clone().sub(h).dot(g)));e(le,()=>{I.alpha=(Date.now()-I.time)/600;const B=p.clone().lerp(f,I.alpha);e(W).target.copy(B),!C&&e(W).object.position.copy(h.clone().lerp(v,I.alpha)),e(un),I.alpha>=.9&&(s.enablePan=o.enablePan,s.enableRotate=o.enableRotate),I.alpha>=1&&(I.stop=!0,e(fs))},()=>!I.stop)}),t(mi,()=>{let f=e(Lt).toArray(),m=e(ue).toArray(),C=e(_t).toArray();return{position:f,lookUp:m,lookAt:C}}),t(Ci,()=>e(W).update()),t(yi,()=>e(W).updateRotateAxis());const l=.01;let u=new A.Vector3,c=new A.Vector3,d=0;t(is,()=>{const f=e(W).object,m=f.fov,C=f.position.clone(),I=f.getWorldDirection(new A.Vector3);return Math.abs(d-m)<l&&Math.abs(C.x-u.x)<l&&Math.abs(C.y-u.y)<l&&Math.abs(C.z-u.z)<l&&Math.abs(I.x-c.x)<l&&Math.abs(I.y-c.y)<l&&Math.abs(I.z-c.z)<l?!1:(d=m,u=C,c=I,!0)})}function ql(n){const t=(o,r,l)=>n.on(o,r,l),e=(o,...r)=>n.fire(o,...r);let s=a();const i=[];t(gs,()=>{const{height:o}=e(ne),r=e(Lt).distanceTo(s.position)*3.2/o;s.scale.set(r,r,r)}),t(ln,o=>{e(gs),s.visible=o>.1,s.element.style.opacity=o+"",e(Z)}),t(Xi,o=>{for(s.position.copy(o);i.length;)i.pop().stop=!0;e(ln,1),e(Z)}),t(fs,()=>{for(;i.length;)i.pop().stop=!0;let o={opacity:1,time:Date.now(),stop:!1};i.push(o),e(le,()=>{o=i[0],!o&&e(ln,1),o&&(o.opacity=1-Math.min((Date.now()-o.time)/1500,1),o.opacity<.2&&(o.opacity=0,o.stop=!0),e(ln,o.opacity))},()=>!o?.stop)});function a(){const o=document.createElement("div");o.innerHTML='<svg height="16" width="16" style="fill:white;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M108.8 469.333333C128 279.466667 279.466667 128 469.333333 108.8V64c0-23.466667 19.2-42.666667 42.666667-42.666667s42.666667 19.2 42.666667 42.666667v44.8c189.866667 19.2 341.333333 170.666667 360.533333 360.533333H960c23.466667 0 42.666667 19.2 42.666667 42.666667s-19.2 42.666667-42.666667 42.666667h-44.8c-19.2 189.866667-170.666667 341.333333-360.533333 360.533333V960c0 23.466667-19.2 42.666667-42.666667 42.666667s-42.666667-19.2-42.666667-42.666667v-44.8C279.466667 896 128 744.533333 108.8 554.666667H64c-23.466667 0-42.666667-19.2-42.666667-42.666667s19.2-42.666667 42.666667-42.666667h44.8zM469.333333 194.133333C326.4 213.333333 215.466667 326.4 196.266667 469.333333H234.666667c23.466667 0 42.666667 19.2 42.666666 42.666667s-19.2 42.666667-42.666666 42.666667H196.266667c19.2 142.933333 132.266667 256 275.2 273.066666V789.333333c0-23.466667 19.2-42.666667 42.666666-42.666666s42.666667 19.2 42.666667 42.666666v38.4C697.6 810.666667 810.666667 697.6 829.866667 554.666667H789.333333c-23.466667 0-42.666667-19.2-42.666666-42.666667s19.2-42.666667 42.666666-42.666667h40.533334C810.666667 326.4 697.6 213.333333 554.666667 194.133333V234.666667c0 23.466667-19.2 42.666667-42.666667 42.666666s-42.666667-19.2-42.666667-42.666666V194.133333z"></path></svg>',o.classList.add("css3d-focus-marker"),o.style.position="absolute";const r=new yt(o);return r.element.style.pointerEvents="none",r.element.style.opacity="1",r.visible=!1,e(z).add(r),r.onBeforeRender=()=>e(gs),r}}function Na(n){const t=(o,r,l)=>n.on(o,r,l),e=(o,...r)=>n.fire(o,...r),s=new Map,i=document.createElement("div");i.classList.add("mark-warp"),document.body.appendChild(i);const a=new Ul;a.setSize(innerWidth,innerHeight),a.domElement.style.position="absolute",a.domElement.style.top="0px",a.domElement.style.pointerEvents="none",i.appendChild(a.domElement),t($a,()=>i),t(hs,()=>a),t(ms,()=>document.body.removeChild(i)),t(pe,()=>a.render(e(z),e(q)),!0),t(Zt,o=>{const r=o?.getMarkData?.()?.name||o?.name;r&&s.set(r,new WeakRef(o))}),t(he,o=>{const r=o?.getMarkData?.()?.name||o?.name;s.delete(r)}),t(Qe,o=>s.get(o)?.deref()),t(tr,(o,r)=>{const l=e(Qe,o);!l||!r||l.drawUpdate?.(r)}),t(er,o=>{const r=e(Qe,o);return r?r.getMarkData?.():{}}),t(De,o=>{o!==void 0&&(e(F).markVisible=o),e(z).traverse(r=>r.isMark&&(r.visible=e(F).markVisible)),e(Z)}),t(Cs,async()=>{const o=[];e(z).traverse(l=>{if(l.isMark){const u=l.getMarkData?.();u&&o.push(u)}});const r=e(bt).meta||{};return o.length?r.marks=o:delete r.marks,r.cameraInfo=e(mi),await e(Xt,r)}),t(hn,async()=>{const o=[];e(z).traverse(l=>{if(l.isMark){const u=l.getMarkData?.();u&&o.push(u)}});const r=e(bt).meta||{};return o.length?r.marks=o:delete r.marks,await e(Xt,r)}),t(qi,async()=>{const o=e(bt).meta||{};delete o.marks;const r=await e(Xt,o),l=[];return s.forEach(u=>l.push(u)),l.forEach(u=>u.deref()?.dispose?.()),e(Z),r}),t($i,async()=>{e(z).traverse(r=>{r.isMark&&r.getMarkData?.()});const o=e(bt).meta||{};return o.watermark=e(Ki)||"",await e(Xt,o)}),t(ys,o=>{o.meterScale&&(e(F).meterScale=o.meterScale,e(rt,{scale:`1 : ${e(F).meterScale} m`})),e(W).updateByOptions({...o,...o.cameraInfo||{}}),(o.marks||[]).forEach(l=>{if(l.type==="MarkSinglePoint"){const u=new Fa(n,l);u.visible=!1,e(z).add(u)}else if(l.type==="MarkDistanceLine"){const u=new ri(n);u.draw(l),u.visible=!1,e(z).add(u)}else if(l.type==="MarkMultiLines"){const u=new ci(n);u.draw(l,!0),u.visible=!1,e(z).add(u)}else if(l.type==="MarkMultiPlans"){const u=new li(n);u.draw(l,!0),u.visible=!1,e(z).add(u)}else if(l.type==="MarkCirclePlan"){const u=new Pa(n);u.draw(l),u.visible=!1,e(z).add(u)}}),e(mn,o.flyPositions||[]),e(Cn,o.flyTargets||[])}),t(jt,()=>{const o=e(F);o.markMode=!1,e(ds),e(Z)}),t(ji,(o,r=!0)=>{const l=o?.meterScale;if(l){if(typeof l!="number"||l<=0){console.warn("meterScale is not a number or <= 0",o);return}r&&(e(F).meterScale=o.meterScale),e(rt,{scale:`1 : ${l} m`});for(const u of s.values()){const c=u.deref();c&&(c instanceof ri||c instanceof ci||c instanceof li)&&c.updateByMeterScale(l)}}}),t(Jn,o=>{const r=new A.Vector3().fromArray(o.slice(0,3)),l=new A.Vector3().fromArray(o.slice(-6,-3)),u=new A.Vector3().fromArray(o.slice(-3)),c=r.distanceTo(u)<1e-4,d=l.distanceTo(u)<1e-4,f=new A.Vector3,m=c||d?o.length/3-1:o.length/3;for(let C=0;C<m;C++)f.add(new A.Vector3(o[C*3],o[C*3+1],o[C*3+2]));return f.divideScalar(m),f}),t(Kn,o=>{const r=[];for(let d=0,f=o.length/3;d<f;d++)r.push(new A.Vector3(o[d*3],o[d*3+1],o[d*3+2]));const l=r[0].distanceTo(r[r.length-1])<1e-4,u=r[r.length-2].distanceTo(r[r.length-1])<1e-4;if((l||u)&&r.pop(),r.length<3)return 0;let c=0;for(let d=0,f=r.length-2;d<f;d++)c+=e(Zn,r[0],r[d+1],r[d+2],e(F).meterScale);return c}),t(pi,(o,r)=>{let l=0;for(let u=0,c=o.length-2;u<c;u++)l+=e(Zn,o[0],o[u+1],o[u+2],r);return l}),t(Zn,(o,r,l,u)=>{const c=o.distanceTo(r)*u,d=r.distanceTo(l)*u,f=l.distanceTo(o)*u,m=(c+d+f)/2;return Math.sqrt(m*(m-c)*(m-d)*(m-f))})}function Wa(n){const t=(h,...g)=>n.fire(h,...g),e=(h,g,v)=>n.on(h,g,v),s=[],i=[];let a=!1,o=!1;e(yn,()=>a=!1),e(so,()=>a=!0),e(eo,()=>s),e(nr,()=>{const h=[];for(let g=0,v=s.length;g<v;g++)h.push(...s[g].toArray());return h}),e(sr,()=>{const h=[];for(let g=0,v=i.length;g<v;g++)h.push(...i[g].toArray());return h}),e(mn,h=>{for(let g=0,v=h.length/3|0;g<v;g++)s[g]=new A.Vector3(h[g*3+0],h[g*3+1],h[g*3+2])}),e(Cn,h=>{for(let g=0,v=h.length/3|0;g<v;g++)i[g]=new A.Vector3(h[g*3+0],h[g*3+1],h[g*3+2])}),e(Te,()=>{const h=t(W);s.push(h.object.position.clone()),i.push(h.target.clone())}),e(Le,()=>{s.length=0,i.length=0}),e(Re,async(h=!0)=>{const g=t(bt)?.meta||t(Es);if(g.scenes){const B=g;if(s.length){const x=[],k=[];for(let M=0,P=s.length;M<P;M++)x.push(...s[M].toArray()),k.push(...i[M].toArray());B.flyPositions=x,B.flyTargets=k}else delete B.flyPositions,delete B.flyTargets;const R=t(W);B.position=R.object.position.toArray(),B.lookAt=R.target.toArray(),h&&await t(Xt,B)}else{const B=g;if(s.length){const R=[],x=[];for(let k=0,M=s.length;k<M;k++)R.push(...s[k].toArray()),x.push(...i[k].toArray());B.flyPositions=R,B.flyTargets=x}else delete B.flyPositions,delete B.flyTargets;h&&await t(Xt,B)}const v=(g.scenes,g);if(s.length){const B=[],R=[];for(let x=0,k=s.length;x<k;x++)B.push(...s[x].toArray()),R.push(...i[x].toArray());v.flyPositions=B,v.flyTargets=R}else delete v.flyPositions,delete v.flyTargets;h&&await t(Xt,v)}),e(no,()=>{o||(o=!0)&&t(qt,!0)});let r=0;const l=125*1e3;let u=l,c=0,d=0,f=0,m=0,C=1,I,p;e(qt,h=>{if(r=0,u=l,c=Date.now(),d=0,f=c,m=0,C=1,I=null,p=null,!s.length||!h&&!t(W).autoRotate)return;const g=t(W),v=[g.object.position.clone()],B=[g.target.clone()],R=t(eo)||[];for(let x=0,k=Math.min(R.length,1e3);x<k;x++)R[x]&&v.push(R[x]),i[x]&&B.push(i[x]);I=new A.CatmullRomCurve3(v),I.closed=!1,p=new A.CatmullRomCurve3(B),p.closed=!1,t(so),t(gt,!1)}),e(rr,()=>{d=d||Date.now()}),e(fr,(h,g)=>{g&&(m=g*l),d&&(f=Date.now(),d=0),a||t(qt,!0),C=h}),e(dn,()=>{const h=t(W);if(d||(m+=C*(Date.now()-f),f=Date.now(),(m<0||m>u)&&t(yn),!a||!I||!p))return;r=m/u;const g=I.getPoint(r),v=p.getPoint(r);h.object.position.set(g.x,g.y,g.z),h.target.set(v.x,v.y,v.z)},!0)}class $l{constructor(t={}){this.disposed=!1,this.needUpdate=!0,console.info("Reall3dViewer",Tn),this.init(ba(t)),!t.disableDropLocalFile&&this.enableDropLocalFile()}init(t){const e=this;t.position=t.position?[...t.position]:[0,-5,15],t.lookAt=t.lookAt?[...t.lookAt]:[0,0,0],t.lookUp=t.lookUp?[...t.lookUp]:[0,-1,0];const s=ul(t),i=t.scene=t.scene||new A.Scene;i.background=new A.Color(t.background),dl(t);const a=t.controls=new kl(t);a.updateByOptions(t);const o=a.object,r=new kn;t.viewerEvents=r,e.events=r;const l=(m,C,I)=>r.on(m,C,I),u=(m,...C)=>r.fire(m,...C);l(go,()=>e.metaMatrix),l(F,()=>t),l(Ae,()=>s.domElement),l(xe,()=>s),l(z,()=>i),l(W,()=>a),l(q,()=>o),l(It,()=>t.bigSceneMode),l(qa,m=>t.pointcloudMode=m);const c=[];l(Z,()=>{for(e.needUpdate=!0;c.length;)c.pop().stop=!0;let m={count:0,stop:!1};c.push(m),u(gi,()=>{!e.disposed&&(e.needUpdate=!0),m.count++>=600&&(m.stop=!0)},()=>!e.disposed&&(u(ps)||!m.stop),10)}),Gs(r),ll(r),ti(r),jl(r),Na(r),Hl(r),Ua(r),ql(r),Wa(r),e.splatMesh=new Bt(gl(t)),l(bt,()=>e.splatMesh),i.add(e.splatMesh),ml(r),l(Qs,m=>{t.qualityLevel=m,e.splatMesh.options({qualityLevel:m,renderer:void 0,scene:void 0})}),l(Ps,m=>{t.sortType=m,e.splatMesh.options({sortType:m,renderer:void 0,scene:void 0})}),i.add(new A.AmbientLight("#ffffff",2)),s.setAnimationLoop(e.update.bind(e)),l(Zi,()=>{if(!(u(F).useCustomControl===!0)){const I=a.update();Math.random()<.001&&a.autoRotate}!e.needUpdate&&u(is)&&u(Z)}),l(fe,()=>u(an),!0),l(fe,()=>u(Zi),!0);let d=performance.now();l(pe,()=>{try{const m=performance.now();if(!e.needUpdate||m-d<(wt?25:t.qualityLevel>5?1:18))return;e.needUpdate=!1,d=m,s.render(i,o),u(Mt)&&u(tn)}catch(m){console.warn(m.message)}},!0),l(nn,()=>e.dispose()),l(io,()=>console.info(JSON.stringify(u(bt).meta||{},null,2)));let f="";l(gn,(m="")=>{f=m,e.splatMesh.fire(Is,f,!0)}),l(Ki,()=>f),u(rt,{scale:`1 : ${u(F).meterScale} m`}),e.initGsApi()}enableDropLocalFile(){const t=this;document.addEventListener("dragover",function(e){e.preventDefault(),e.stopPropagation()}),document.addEventListener("drop",async function(e){e.preventDefault(),e.stopPropagation();let s=e.dataTransfer.files[0];if(!s)return;let i,a=!1;if(s.name.endsWith(".spx"))i="spx";else if(s.name.endsWith(".splat"))i="splat";else if(s.name.endsWith(".ply"))i="ply";else if(s.name.endsWith(".spz"))i="spz";else if(s.name.endsWith(".sog"))i="sog";else if(s.name.endsWith(".obj"))i="obj";else if(s.name.endsWith(".scene.json"))a=!0;else return console.error("unsupported format:",s.name);const o=URL.createObjectURL(s),r=t.events.fire(F);r.bigSceneMode=!1,r.pointcloudMode=!0,r.debugMode=!0,r.autoRotate=i!=="obj",r.maxRenderCountOfPc=1024*1e4,r.qualityLevel=9,r.disableTransitionEffectOnLoad=!1,t.reset(r),a?await t.addScene(o):await t.addModel({url:o,format:i}),URL.revokeObjectURL(o)})}update(){const t=this;if(t.disposed)return;const e=(s,...i)=>t.events.fire(s,...i);e(fe),e(pe),e(dn)}fire(t,e,s){const i=this;if(t===1&&i.splatMesh.fire(ts,e),t===2&&i.events.fire(Te),t===3&&i.events.fire(qt,!0),t===4&&i.events.fire(Le),t===5&&i.events.fire(Re),t===6&&i.events.fire(hn),t===7&&i.events.fire(qi),t===8&&(async()=>{let a=await i.splatMesh.fire(bs);e&&(a=i.splatMesh.fire(co)+e),i.splatMesh.fire(je,a)})(),t===9)if(!e)i.events.fire(Qs,vt.Default5);else{const a=i.events.fire(F).qualityLevel;i.events.fire(Qs,Math.max(vt.L1,Math.min(a+e,vt.L9)))}if(t===10)if(!e)i.events.fire(Ps,Ue.Default1);else{const a=[1,2010,2011,2012,2112],o=i.events.fire(F).sortType;let r=a.indexOf(o)+e;r=Math.max(0,Math.min(r,a.length-1)),i.events.fire(Ps,a[r])}}options(t){const e=this;if(e.disposed)return{};const s=(r,...l)=>e.events.fire(r,...l);let i;if(s(z).traverse(r=>!i&&r instanceof Bt&&(i=r.options())),t){const r=s(F);t.autoRotate!==void 0&&(r.autoRotate=t.autoRotate,s(W).autoRotate=t.autoRotate),t.pointcloudMode!==void 0&&s(fn,t.pointcloudMode),t.lightFactor!==void 0&&s(Me,t.lightFactor),t.maxRenderCountOfMobile&&(r.maxRenderCountOfMobile=t.maxRenderCountOfMobile),t.maxRenderCountOfPc&&(r.maxRenderCountOfPc=t.maxRenderCountOfPc),t.debugMode!==void 0&&(r.debugMode=t.debugMode),t.qualityLevel!==void 0&&(r.qualityLevel=t.qualityLevel)&&this.splatMesh.options({qualityLevel:t.qualityLevel}),t.sortType!==void 0&&(r.sortType=t.sortType)&&this.splatMesh.options({sortType:t.sortType}),t.cameraMoveSpeed!==void 0&&(s(F).cameraMoveSpeed=t.cameraMoveSpeed),t.useCustomControl!==void 0&&(s(F).useCustomControl=t.useCustomControl),t.useCustomControl!==void 0&&s(W).updateControlMode(s(F)),t.markType!==void 0&&(s(F).markType=t.markType),t.markVisible!==void 0&&s(De,t.markVisible),t.meterScale!==void 0&&(r.meterScale=t.meterScale),t.markMode!==void 0&&(r.markMode=t.markMode,!t.markMode&&s(ds),s(W).autoRotate=s(F).autoRotate=!1),t.disableProgressiveLoading!==void 0&&(s(F).disableProgressiveLoading=t.disableProgressiveLoading),t.disableStreamLoading!==void 0&&(s(F).disableStreamLoading=t.disableStreamLoading),t.onPerformanceUpdate!==void 0&&(s(F).onPerformanceUpdate=t.onPerformanceUpdate)}return s(W).updateByOptions(t),s(rt,{scale:`1 : ${s(F).meterScale} m`}),Object.assign({...s(F)},i)}reset(t={}){const e=this;e.dispose(),e.disposed=!1,e.init(ba(t))}switchDeiplayMode(){const t=this;t.disposed||t.events.fire(pn)}async addScene(t){const e=this;if(e.disposed)return;const s=(o,...r)=>e.events.fire(o,...r);let i={};try{const o=await fetch(t,{mode:"cors",credentials:"omit",cache:"reload"});if(o.status===200)i=await o.json();else return console.error("scene file fetch failed, status:",o.status)}catch(o){return console.error("scene file fetch failed",o.message)}if(!i.url)return console.error("missing model file url");if(!i.url.endsWith(".spx"))return console.error("The format is unsupported in the large scene mode",i.url);const a={...i,...i.cameraInfo||{}};i.autoCut=Math.min(Math.max(i.autoCut||0,0),50),a.bigSceneMode=i.autoCut>1,e.reset({...a}),!a.bigSceneMode&&delete i.autoCut,e.metaMatrix=i.transform?new A.Matrix4().fromArray(i.transform):null,e.splatMesh.meta=i,a.bigSceneMode?(s(mn,i.flyPositions||[]),s(Cn,i.flyTargets||[])):s(ys,i),await e.splatMesh.addModel({url:i.url},i),await s(gn,i.watermark),s(W).updateRotateAxis()}async addModel(t){const e=this;if(e.disposed)return;const s=(u,c,d)=>this.events.on(u,c,d),i=(u,...c)=>e.events.fire(u,...c);let a="",o={url:""};if(Object.prototype.toString.call(t)==="[object String]"?t.endsWith(".meta.json")?a=t:o.url=t:o=t,!o.url&&!a)return console.error("model url is empty");const r=i(F);r.bigSceneMode=!1;let l={};if(!o.url.startsWith("blob:"))try{a=a||o.url.substring(0,o.url.lastIndexOf("."))+".meta.json";const u=await fetch(a,{mode:"cors",credentials:"omit",cache:"reload"});u.status===200?l=await u.json():console.warn("meta file fetch failed, status:",u.status)}catch(u){console.warn("meta file fetch failed",u.message,o.url)}if(l.showWatermark=l.showWatermark!==!1,l.url=l.url||o.url,delete l.autoCut,!o.format)if(o.url=o.url||l.url,o.url.endsWith(".spx"))o.format="spx";else if(o.url.endsWith(".splat"))o.format="splat";else if(o.url.endsWith(".ply"))o.format="ply";else if(o.url.endsWith(".spz"))o.format="spz";else if(o.url.endsWith(".sog")||o.url.endsWith("/meta.json")||o.url=="meta.json")o.format="sog";else if(o.url.endsWith(".obj"))o.format="obj";else{console.error("unknow format!",o.url);return}l.qualityLevel=l.qualityLevel||r.qualityLevel||vt.Default5,l.sortType=l.sortType||r.sortType||Ue.Default1,s(Es,()=>l),e.metaMatrix=l.transform?new A.Matrix4().fromArray(l.transform):null,i(ys,l),this.options({qualityLevel:l.qualityLevel,sortType:l.sortType}),o.format==="obj"?(this.options({autoRotate:!1}),await i(uo,o.url)):(this.splatMesh.addModel(o,l),await i(gn,l.watermark))}initGsApi(){const t=(c,...d)=>this.events.fire(c,...d),e=()=>{setTimeout(()=>window.focus()),t(yn),t(W).autoRotate=t(F).autoRotate=!t(F).autoRotate},s=(c=!0)=>{setTimeout(()=>window.focus()),c=!!c;const d=t(F);d.pointcloudMode!==c&&(d.bigSceneMode?t(fn,c):t(pn))},i=(c=!0)=>{setTimeout(()=>window.focus()),t(De,!!c)},a=c=>{setTimeout(()=>window.focus());const d=t(F);if(d.markMode)return!1;if(c===1)d.markType="point";else if(c===2)d.markType="lines";else if(c===3)d.markType="plans";else if(c===4)d.markType="distance";else return!1;return d.markMode=!0,t(gt),!0},o=async c=>(setTimeout(()=>window.focus()),c?(t(Qe,c)?.dispose(),t(Z),await t(hn)):!1),r=async(c,d=!0)=>(t(ji,c,d),t(Qe,c?.name)?.drawUpdate?.(c,d),t(Z),d?(setTimeout(()=>window.focus()),await t(hn)):!0),l=(c=!0)=>{setTimeout(()=>window.focus()),t(ts,!!c)},u=(c,d=!0)=>{t(gn,c),d&&t($i,c)};window.$api={switchAutoRotate:e,changePointCloudMode:s,showMark:i,startMark:a,deleteMark:o,updateMark:r,showWaterMark:l,setWaterMark:u}}dispose(){const t=this;if(t.disposed)return;t.disposed=!0;const e=(a,...o)=>t.events.fire(a,...o),s=e(xe),i=s.domElement;e(ss),e(Vi),e(ms),e(qn),e(W).dispose(),e(Jt,e(z)),s.clear(),s.dispose(),i.parentElement.removeChild(i),t.splatMesh=null,t.events.clear(),t.events=null}enableFirstPersonMode(t=!0,e){const s=this,i=(d,...f)=>s.events.fire(d,...f),a=i(q),o=i(W),r=a.position.clone();r.toArray();const l=new A.Vector3;a.getWorldDirection(l);const u={useCustomControl:t};e!==void 0&&(u.cameraMoveSpeed=e);const c=this.options(u);if(t){a.position.copy(r);const d=r.clone().add(l);o.target.copy(d);const f=o.getDistance();o.minDistance=f,o.maxDistance=f,o.updateControlMode(i(F)),i(Z),a.position.toArray()}return a.position.toArray(),c}}function tA(n){const t=(o,r,l)=>n.on(o,r,l),e=(o,...r)=>n.fire(o,...r),s=wt?2:20,i=new Map,a=new Map;t(qe,()=>e(Mt)&&i.set(Date.now(),1)),t($e,()=>e(Mt)&&e(de,i)),t(tn,()=>e(Mt)&&a.set(Date.now(),1)),t(en,()=>e(Mt)&&e(de,a)),t(de,o=>{let r=[],l=Date.now(),u=0;for(const c of o.keys())l-c<=1e3?u++:r.push(c);return r.forEach(c=>o.delete(c)),Math.min(u,60)}),t(lt,()=>{const o=e(z),r=e(q),l=[];o?.traverse(function(u){u.isWarpSplatMesh&&u.splatMesh?.visible&&l.push(u)}),l.sort((u,c)=>r.position.distanceTo(u.position)-r.position.distanceTo(c.position));for(let u=0;u<l.length;u++)l[u].splatMesh.boundBox.visible=u<1;return window.splat=l[0]?.splatMesh,l[0]?.splatMesh}),t(bo,()=>{const o=e(z),r=e(q),l=[];o?.traverse(function(u){u.isWarpSplatMesh&&l.push(u)}),l.sort((u,c)=>r.position.distanceTo(u.position)-r.position.distanceTo(c.position));for(let u=0;u<l.length;u++)l[u].active=u<s,l[u].splatMesh&&(l[u].splatMesh.renderOrder=1e3-u)}),t(Eo,()=>{const o=e(z),r=[];o?.traverse(l=>r.push(l)),r.forEach(l=>{l.dispose?l.dispose():(l.geometry?.dispose?.(),l.material&&l.material instanceof Array?l.material.forEach(u=>u?.dispose?.()):l.material?.dispose?.())}),o?.clear()}),t(ks,(o=.1)=>{const r=e(lt);r&&r.visible&&r.rotateOnAxis(new A.Vector3(1,0,0),A.MathUtils.degToRad(o))}),t(Ts,(o=.1)=>{const r=e(lt);r&&r.visible&&r.rotateOnAxis(new A.Vector3(0,1,0),A.MathUtils.degToRad(o))}),t(Rs,(o=.1)=>{const r=e(lt);r&&r.visible&&r.rotateOnAxis(new A.Vector3(0,0,1),A.MathUtils.degToRad(o))}),t(cr,(o=.01)=>{const r=e(lt);r&&r.visible&&(r.position.x+=o)}),t(wo,(o=.01)=>{const r=e(lt);r&&r.visible&&(r.position.y+=o)}),t(lr,(o=.01)=>{const r=e(lt);r&&r.visible&&(r.position.z+=o)}),t(Ar,o=>{const r=e(lt);r&&r.visible&&o&&r.position.copy(o),console.info(r,o)}),t(ur,(o=.01)=>{const r=e(lt);r&&r.visible&&r.scale.set(r.scale.x+o,r.scale.y+o,r.scale.z+o)}),t(dr,()=>{const o=e(lt);o&&(o.visible=!o.visible)}),t(gr,async o=>{if(!o&&(o=e(lt)),!o)return;const r=o.meta||{};return r.transform=o.matrix.toArray(),await e(Xt,JSON.stringify(r),o.meta.url)}),t(mo,()=>{const o=e(F).root,r=new A.WebGLRenderer({antialias:!1,stencil:!0,alpha:!0,logarithmicDepthBuffer:!0,premultipliedAlpha:!1,precision:"highp",powerPreference:"high-performance"});return r.setSize(o.clientWidth,o.clientHeight),r.setPixelRatio(Math.min(devicePixelRatio,2)),t(xe,()=>r),t(Ae,()=>r.domElement),r}),t(Co,()=>{const o=e(F),r=new A.Scene,l=o.background||"#dbf0ff";return r.background=new A.Color(l),r.fog=new A.FogExp2(l,0),t(z,()=>r),r}),t(yo,()=>{const r=e(q),l=e(z),u=e(F),c=new Tl(e(q),u.root);c.screenSpacePanning=!1,c.minDistance=.1,c.maxDistance=6e4,c.maxPolarAngle=1.2,c.enableDamping=!0,c.dampingFactor=.07,c.zoomToCursor=!0;const d=new A.Vector3().fromArray(u.minPan||[-2e4,.1,-6e4]),f=new A.Vector3().fromArray(u.maxPan||[5e4,1e4,0]),m=new A.Vector3;return c.addEventListener("change",()=>{const C=Math.max(c.getPolarAngle(),.1),I=Math.max(c.getDistance(),.1);c.zoomSpeed=Math.max(Math.log(I),0)+.5,r.far=A.MathUtils.clamp(I/C*8,100,1e5),r.near=r.far/1e3,r.updateProjectionMatrix(),l.fog instanceof A.FogExp2&&(l.fog.density=C/(I+5)*1*.25),c.maxPolarAngle=Math.min(Math.pow(1e4/I,4),1.2),m.copy(c.target),c.target.clamp(d,f),m.sub(c.target),r.position.sub(m)}),t(W,()=>c),t(Se,()=>r.fov),t(Lt,(C=!1)=>C?r.position.clone():r.position),t(_t,(C=!1)=>C?c.target.clone():c.target),t(ue,(C=!1)=>C?r.up.clone():r.up),c}),t(Io,()=>{const o=new A.DirectionalLight(16777215,1);return o.position.set(0,2e3,1e3),o}),window.addEventListener("beforeunload",()=>e(nn))}function eA(n){let{root:t="#map",debugMode:e}=n;t?t=typeof t=="string"?document.querySelector(t)||document.querySelector("#map"):t:t=document.querySelector("#map"),t||(t=document.createElement("div"),t.id="map",(document.querySelector("#gsviewer")||document.querySelector("body")).appendChild(t));const s={...n};return s.root=t,s}function nA(){const n=new di.plugin.BingSource,t=new di.TileMap({imgSource:n,lon0:90,minLevel:2,maxLevel:16});return t.scale.set(10,10,10),t.rotateX(-Math.PI/2),t.autoUpdate=!1,t}class sA{constructor(){this.down=0,this.move=!1,this.downTime=0,this.isDbClick=!1,this.x=0,this.y=0,this.lastClickX=0,this.lastClickY=0,this.lastClickPointTime=0,this.lastMovePoint=null,this.lastMovePointTime=0}}function iA(n){const t=(x,k,M)=>n.on(x,k,M),e=(x,...k)=>n.fire(x,...k),s=e(Ae);let i=new Set,a,o=new sA;t(an,()=>{if(!i.size)return;const x=e(F);if(!x.enableKeyboard)return i.clear();const k=x.editMode;if(i.has("KeyH")){const M=e(lt);M&&(M.visible=!M.visible),i.clear()}else if(i.has("Equal"))if(k){const M=e(lt);if(!M||!M.visible)return i.clear();if(i.has("KeyX")){const P=A.MathUtils.degToRad(.1);M.rotateOnAxis(new A.Vector3(1,0,0),P)}else M.scale.set(M.scale.x+.01,M.scale.y+.01,M.scale.z+.01)}else e(Te),i.clear();else if(i.has("Minus"))if(k){const M=e(lt);if(!M||!M.visible)return i.clear();if(i.has("KeyX")){const P=A.MathUtils.degToRad(-.1);M.rotateOnAxis(new A.Vector3(1,0,0),P)}else M.scale.set(M.scale.x-.01,M.scale.y-.01,M.scale.z-.01)}else e(Le),i.clear();else if(i.has("KeyP"))e(qt,!0),i.clear();else if(i.has("ArrowUp")){if(k){const M=e(lt);M&&M.visible&&(M.position.z+=.1)}}else if(i.has("ArrowDown")){if(k){const M=e(lt);M&&M.visible&&(M.position.z-=.1)}}else if(i.has("ArrowRight")){if(k){const M=e(lt);M&&M.visible&&(M.position.x+=.1)}}else if(i.has("ArrowLeft")){if(k){const M=e(lt);M&&M.visible&&(M.position.x-=.1)}}else if(i.has("KeyU")){if(k){const M=e(lt);if(M){const P=M.meta||{};P.transform=M.matrix.toArray()}}}else i.has("KeyQ")?k&&e(ks,.1):i.has("KeyW")?k&&e(Ts,.1):i.has("KeyE")?k&&e(Rs,.1):i.has("KeyA")?k&&e(ks,-.1):i.has("KeyS")?k&&e(Ts,-.1):i.has("KeyD")?k&&e(Rs,-.1):i.has("KeyY")?k?e(wo,i.has("ShiftLeft")||i.has("ShiftRight")?-.1:.1):(e(Re,!0),i.clear()):i.has("KeyC")&&console.info("position=",e(Lt).toArray(),"lookat=",e(_t).toArray())});const r=x=>{x.target.type!=="text"&&(a||x.code==="F5"||(x.preventDefault(),i.add(x.code)))},l=x=>{x.target.type!=="text"&&(a||i.clear())},u=()=>{i.clear()},c=x=>{parent&&setTimeout(()=>window.focus()),x.preventDefault()},d=async x=>{x.preventDefault()};let f,m,C,I;t(ke,()=>{f?.dispose(),m?.dispose(),C?.dispose(),I?.dispose(),f=null,m=null,C=null,I=null,o.lastMovePoint=null,e(jt)});const p=async x=>{parent&&setTimeout(()=>window.focus()),x.preventDefault(),!a&&(o.down=x.button===2?2:1,o.move=!1,o.isDbClick=Date.now()-o.downTime<300,o.downTime=Date.now())},h=async x=>{x.preventDefault(),!a&&o.down&&(o.move=!0)},g=async x=>{x.preventDefault(),!a&&(o.down=0,o.move=!1)};function v(x){x.preventDefault(),!a&&(o.down=x.touches.length,o.down===1&&(o.move=!1,o.x=x.touches[0].clientX,o.y=x.touches[0].clientY))}function B(x){x.touches.length===1&&(o.move=!0)}function R(x){o.down===1&&!o.move&&e(cn,o.x,o.y)}window.addEventListener("keydown",r),window.addEventListener("keyup",l),window.addEventListener("blur",u),window.addEventListener("wheel",c,{passive:!1}),s.addEventListener("contextmenu",d),s.addEventListener("mousedown",p),s.addEventListener("mousemove",h),s.addEventListener("mouseup",g),s.addEventListener("touchstart",v,{passive:!1}),s.addEventListener("touchmove",B,{passive:!1}),s.addEventListener("touchend",R,{passive:!1}),t(qn,()=>{a=!0,window.removeEventListener("keydown",r),window.removeEventListener("keyup",l),window.removeEventListener("blur",u),window.removeEventListener("wheel",c),s.removeEventListener("contextmenu",d),s.removeEventListener("mousedown",p),s.removeEventListener("mousemove",h),s.removeEventListener("mouseup",g),s.removeEventListener("touchstart",v),s.removeEventListener("touchmove",B),s.removeEventListener("touchend",R)}),t(cn,async(x,k)=>await e(rn,x,k))}var be=Object.freeze({Linear:Object.freeze({None:function(n){return n},In:function(n){return n},Out:function(n){return n},InOut:function(n){return n}}),Quadratic:Object.freeze({In:function(n){return n*n},Out:function(n){return n*(2-n)},InOut:function(n){return(n*=2)<1?.5*n*n:-.5*(--n*(n-2)-1)}}),Cubic:Object.freeze({In:function(n){return n*n*n},Out:function(n){return--n*n*n+1},InOut:function(n){return(n*=2)<1?.5*n*n*n:.5*((n-=2)*n*n+2)}}),Quartic:Object.freeze({In:function(n){return n*n*n*n},Out:function(n){return 1- --n*n*n*n},InOut:function(n){return(n*=2)<1?.5*n*n*n*n:-.5*((n-=2)*n*n*n-2)}}),Quintic:Object.freeze({In:function(n){return n*n*n*n*n},Out:function(n){return--n*n*n*n*n+1},InOut:function(n){return(n*=2)<1?.5*n*n*n*n*n:.5*((n-=2)*n*n*n*n+2)}}),Sinusoidal:Object.freeze({In:function(n){return 1-Math.sin((1-n)*Math.PI/2)},Out:function(n){return Math.sin(n*Math.PI/2)},InOut:function(n){return .5*(1-Math.sin(Math.PI*(.5-n)))}}),Exponential:Object.freeze({In:function(n){return n===0?0:Math.pow(1024,n-1)},Out:function(n){return n===1?1:1-Math.pow(2,-10*n)},InOut:function(n){return n===0?0:n===1?1:(n*=2)<1?.5*Math.pow(1024,n-1):.5*(-Math.pow(2,-10*(n-1))+2)}}),Circular:Object.freeze({In:function(n){return 1-Math.sqrt(1-n*n)},Out:function(n){return Math.sqrt(1- --n*n)},InOut:function(n){return(n*=2)<1?-.5*(Math.sqrt(1-n*n)-1):.5*(Math.sqrt(1-(n-=2)*n)+1)}}),Elastic:Object.freeze({In:function(n){return n===0?0:n===1?1:-Math.pow(2,10*(n-1))*Math.sin((n-1.1)*5*Math.PI)},Out:function(n){return n===0?0:n===1?1:Math.pow(2,-10*n)*Math.sin((n-.1)*5*Math.PI)+1},InOut:function(n){return n===0?0:n===1?1:(n*=2,n<1?-.5*Math.pow(2,10*(n-1))*Math.sin((n-1.1)*5*Math.PI):.5*Math.pow(2,-10*(n-1))*Math.sin((n-1.1)*5*Math.PI)+1)}}),Back:Object.freeze({In:function(n){var t=1.70158;return n===1?1:n*n*((t+1)*n-t)},Out:function(n){var t=1.70158;return n===0?0:--n*n*((t+1)*n+t)+1},InOut:function(n){var t=2.5949095;return(n*=2)<1?.5*(n*n*((t+1)*n-t)):.5*((n-=2)*n*((t+1)*n+t)+2)}}),Bounce:Object.freeze({In:function(n){return 1-be.Bounce.Out(1-n)},Out:function(n){return n<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375},InOut:function(n){return n<.5?be.Bounce.In(n*2)*.5:be.Bounce.Out(n*2-1)*.5+.5}}),generatePow:function(n){return n===void 0&&(n=4),n=n<Number.EPSILON?Number.EPSILON:n,n=n>1e4?1e4:n,{In:function(t){return Math.pow(t,n)},Out:function(t){return 1-Math.pow(1-t,n)},InOut:function(t){return t<.5?Math.pow(t*2,n)/2:(1-Math.pow(2-t*2,n))/2+.5}}}}),Xe=function(){return performance.now()},oA=(function(){function n(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._tweens={},this._tweensAddedDuringUpdate={},this.add.apply(this,t)}return n.prototype.getAll=function(){var t=this;return Object.keys(this._tweens).map(function(e){return t._tweens[e]})},n.prototype.removeAll=function(){this._tweens={}},n.prototype.add=function(){for(var t,e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];for(var i=0,a=e;i<a.length;i++){var o=a[i];(t=o._group)===null||t===void 0||t.remove(o),o._group=this,this._tweens[o.getId()]=o,this._tweensAddedDuringUpdate[o.getId()]=o}},n.prototype.remove=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var s=0,i=t;s<i.length;s++){var a=i[s];a._group=void 0,delete this._tweens[a.getId()],delete this._tweensAddedDuringUpdate[a.getId()]}},n.prototype.allStopped=function(){return this.getAll().every(function(t){return!t.isPlaying()})},n.prototype.update=function(t,e){t===void 0&&(t=Xe()),e===void 0&&(e=!0);var s=Object.keys(this._tweens);if(s.length!==0)for(;s.length>0;){this._tweensAddedDuringUpdate={};for(var i=0;i<s.length;i++){var a=this._tweens[s[i]],o=!e;a&&a.update(t,o)===!1&&!e&&this.remove(a)}s=Object.keys(this._tweensAddedDuringUpdate)}},n})(),Ai={Linear:function(n,t){var e=n.length-1,s=e*t,i=Math.floor(s),a=Ai.Utils.Linear;return t<0?a(n[0],n[1],s):t>1?a(n[e],n[e-1],e-s):a(n[i],n[i+1>e?e:i+1],s-i)},Utils:{Linear:function(n,t,e){return(t-n)*e+n}}},Ha=(function(){function n(){}return n.nextId=function(){return n._nextId++},n._nextId=0,n})(),ui=new oA,aA=(function(){function n(t,e){this._isPaused=!1,this._pauseStart=0,this._valuesStart={},this._valuesEnd={},this._valuesStartRepeat={},this._duration=1e3,this._isDynamic=!1,this._initialRepeat=0,this._repeat=0,this._yoyo=!1,this._isPlaying=!1,this._reversed=!1,this._delayTime=0,this._startTime=0,this._easingFunction=be.Linear.None,this._interpolationFunction=Ai.Linear,this._chainedTweens=[],this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._id=Ha.nextId(),this._isChainStopped=!1,this._propertiesAreSetUp=!1,this._goToEnd=!1,this._object=t,typeof e=="object"?(this._group=e,e.add(this)):e===!0&&(this._group=ui,ui.add(this))}return n.prototype.getId=function(){return this._id},n.prototype.isPlaying=function(){return this._isPlaying},n.prototype.isPaused=function(){return this._isPaused},n.prototype.getDuration=function(){return this._duration},n.prototype.to=function(t,e){if(e===void 0&&(e=1e3),this._isPlaying)throw new Error("Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.");return this._valuesEnd=t,this._propertiesAreSetUp=!1,this._duration=e<0?0:e,this},n.prototype.duration=function(t){return t===void 0&&(t=1e3),this._duration=t<0?0:t,this},n.prototype.dynamic=function(t){return t===void 0&&(t=!1),this._isDynamic=t,this},n.prototype.start=function(t,e){if(t===void 0&&(t=Xe()),e===void 0&&(e=!1),this._isPlaying)return this;if(this._repeat=this._initialRepeat,this._reversed){this._reversed=!1;for(var s in this._valuesStartRepeat)this._swapEndStartRepeatValues(s),this._valuesStart[s]=this._valuesStartRepeat[s]}if(this._isPlaying=!0,this._isPaused=!1,this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._isChainStopped=!1,this._startTime=t,this._startTime+=this._delayTime,!this._propertiesAreSetUp||e){if(this._propertiesAreSetUp=!0,!this._isDynamic){var i={};for(var a in this._valuesEnd)i[a]=this._valuesEnd[a];this._valuesEnd=i}this._setupProperties(this._object,this._valuesStart,this._valuesEnd,this._valuesStartRepeat,e)}return this},n.prototype.startFromCurrentValues=function(t){return this.start(t,!0)},n.prototype._setupProperties=function(t,e,s,i,a){for(var o in s){var r=t[o],l=Array.isArray(r),u=l?"array":typeof r,c=!l&&Array.isArray(s[o]);if(!(u==="undefined"||u==="function")){if(c){var d=s[o];if(d.length===0)continue;for(var f=[r],m=0,C=d.length;m<C;m+=1){var I=this._handleRelativeValue(r,d[m]);if(isNaN(I)){c=!1,console.warn("Found invalid interpolation list. Skipping.");break}f.push(I)}c&&(s[o]=f)}if((u==="object"||l)&&r&&!c){e[o]=l?[]:{};var p=r;for(var h in p)e[o][h]=p[h];i[o]=l?[]:{};var d=s[o];if(!this._isDynamic){var g={};for(var h in d)g[h]=d[h];s[o]=d=g}this._setupProperties(p,e[o],d,i[o],a)}else(typeof e[o]>"u"||a)&&(e[o]=r),l||(e[o]*=1),c?i[o]=s[o].slice().reverse():i[o]=e[o]||0}}},n.prototype.stop=function(){return this._isChainStopped||(this._isChainStopped=!0,this.stopChainedTweens()),this._isPlaying?(this._isPlaying=!1,this._isPaused=!1,this._onStopCallback&&this._onStopCallback(this._object),this):this},n.prototype.end=function(){return this._goToEnd=!0,this.update(this._startTime+this._duration),this},n.prototype.pause=function(t){return t===void 0&&(t=Xe()),this._isPaused||!this._isPlaying?this:(this._isPaused=!0,this._pauseStart=t,this)},n.prototype.resume=function(t){return t===void 0&&(t=Xe()),!this._isPaused||!this._isPlaying?this:(this._isPaused=!1,this._startTime+=t-this._pauseStart,this._pauseStart=0,this)},n.prototype.stopChainedTweens=function(){for(var t=0,e=this._chainedTweens.length;t<e;t++)this._chainedTweens[t].stop();return this},n.prototype.group=function(t){return t?(t.add(this),this):(console.warn("tween.group() without args has been removed, use group.add(tween) instead."),this)},n.prototype.remove=function(){var t;return(t=this._group)===null||t===void 0||t.remove(this),this},n.prototype.delay=function(t){return t===void 0&&(t=0),this._delayTime=t,this},n.prototype.repeat=function(t){return t===void 0&&(t=0),this._initialRepeat=t,this._repeat=t,this},n.prototype.repeatDelay=function(t){return this._repeatDelayTime=t,this},n.prototype.yoyo=function(t){return t===void 0&&(t=!1),this._yoyo=t,this},n.prototype.easing=function(t){return t===void 0&&(t=be.Linear.None),this._easingFunction=t,this},n.prototype.interpolation=function(t){return t===void 0&&(t=Ai.Linear),this._interpolationFunction=t,this},n.prototype.chain=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return this._chainedTweens=t,this},n.prototype.onStart=function(t){return this._onStartCallback=t,this},n.prototype.onEveryStart=function(t){return this._onEveryStartCallback=t,this},n.prototype.onUpdate=function(t){return this._onUpdateCallback=t,this},n.prototype.onRepeat=function(t){return this._onRepeatCallback=t,this},n.prototype.onComplete=function(t){return this._onCompleteCallback=t,this},n.prototype.onStop=function(t){return this._onStopCallback=t,this},n.prototype.update=function(t,e){var s=this,i;if(t===void 0&&(t=Xe()),e===void 0&&(e=n.autoStartOnUpdate),this._isPaused)return!0;var a;if(!this._goToEnd&&!this._isPlaying)if(e)this.start(t,!0);else return!1;if(this._goToEnd=!1,t<this._startTime)return!0;this._onStartCallbackFired===!1&&(this._onStartCallback&&this._onStartCallback(this._object),this._onStartCallbackFired=!0),this._onEveryStartCallbackFired===!1&&(this._onEveryStartCallback&&this._onEveryStartCallback(this._object),this._onEveryStartCallbackFired=!0);var o=t-this._startTime,r=this._duration+((i=this._repeatDelayTime)!==null&&i!==void 0?i:this._delayTime),l=this._duration+this._repeat*r,u=function(){if(s._duration===0||o>l)return 1;var I=Math.trunc(o/r),p=o-I*r,h=Math.min(p/s._duration,1);return h===0&&o===s._duration?1:h},c=u(),d=this._easingFunction(c);if(this._updateProperties(this._object,this._valuesStart,this._valuesEnd,d),this._onUpdateCallback&&this._onUpdateCallback(this._object,c),this._duration===0||o>=this._duration)if(this._repeat>0){var f=Math.min(Math.trunc((o-this._duration)/r)+1,this._repeat);isFinite(this._repeat)&&(this._repeat-=f);for(a in this._valuesStartRepeat)!this._yoyo&&typeof this._valuesEnd[a]=="string"&&(this._valuesStartRepeat[a]=this._valuesStartRepeat[a]+parseFloat(this._valuesEnd[a])),this._yoyo&&this._swapEndStartRepeatValues(a),this._valuesStart[a]=this._valuesStartRepeat[a];return this._yoyo&&(this._reversed=!this._reversed),this._startTime+=r*f,this._onRepeatCallback&&this._onRepeatCallback(this._object),this._onEveryStartCallbackFired=!1,!0}else{this._onCompleteCallback&&this._onCompleteCallback(this._object);for(var m=0,C=this._chainedTweens.length;m<C;m++)this._chainedTweens[m].start(this._startTime+this._duration,!1);return this._isPlaying=!1,!1}return!0},n.prototype._updateProperties=function(t,e,s,i){for(var a in s)if(e[a]!==void 0){var o=e[a]||0,r=s[a],l=Array.isArray(t[a]),u=Array.isArray(r),c=!l&&u;c?t[a]=this._interpolationFunction(r,i):typeof r=="object"&&r?this._updateProperties(t[a],o,r,i):(r=this._handleRelativeValue(o,r),typeof r=="number"&&(t[a]=o+(r-o)*i))}},n.prototype._handleRelativeValue=function(t,e){return typeof e!="string"?e:e.charAt(0)==="+"||e.charAt(0)==="-"?t+parseFloat(e):parseFloat(e)},n.prototype._swapEndStartRepeatValues=function(t){var e=this._valuesStartRepeat[t],s=this._valuesEnd[t];typeof s=="string"?this._valuesStartRepeat[t]=this._valuesStartRepeat[t]+parseFloat(s):this._valuesStartRepeat[t]=this._valuesEnd[t],this._valuesEnd[t]=e},n.autoStartOnUpdate=!1,n})();Ha.nextId;var Ot=ui;Ot.getAll.bind(Ot),Ot.removeAll.bind(Ot),Ot.add.bind(Ot),Ot.remove.bind(Ot),Ot.update.bind(Ot);const Ga=navigator.userAgent.includes("Mobi");class rA extends A.Mesh{constructor(t,e){super(),this.isWarpSplatMesh=!0,this.lastActiveTime=Date.now(),this.active=!1,this.disposed=!1;const s=this;s.mapViewer=e,s.addScene(t),s.frustumCulled=!1}async addScene(t){const e=this,{renderer:s,scene:i,controls:a,tileMap:o}=e.mapViewer;fetch(t,{mode:"cors",credentials:"omit",cache:"reload"}).then(r=>r.ok?r.json():{}).then(r=>{const l=new A.Matrix4;if(r.transform)l.fromArray(r.transform);else if(r.WGS84){const C=o.geo2world(new A.Vector3().fromArray(r.WGS84));l.makeTranslation(C.x,C.y,C.z),r.transform=l.toArray()}e.metaMatrix=l,r.autoCut&&(r.autoCut=Math.min(Math.max(r.autoCut,1),50));const u=r.autoCut&&r.autoCut>1,c=!1,d=!1,f=r.showWatermark!==!1,m={renderer:s,scene:i,controls:a,pointcloudMode:c,bigSceneMode:u,showWatermark:f,depthTest:d,mapMode:!0};m.maxRenderCountOfMobile??(m.maxRenderCountOfMobile=m.bigSceneMode?128*10240:400*1e4),m.maxRenderCountOfPc??(m.maxRenderCountOfPc=m.bigSceneMode?320*1e4:400*1e4),m.debugMode=e.mapViewer.events.fire(F).debugMode,e.opts=m,e.meta=r,i.add(e),e.initCSS3DSprite(m),e.applyMatrix4(l)}).catch(r=>{console.error(r.message)})}async initCSS3DSprite(t){const e=this,s=t.controls,i=document.createElement("div");i.innerHTML=`<div title="${e.meta.name}" style='flex-direction: column;align-items: center;display: flex;pointer-events: auto;margin-bottom: 20px;'>
                               <svg height="20" width="20" style="color:#eeee00;opacity:0.9;"><use href="#svgicon-point3" fill="currentColor" /></svg>
                            </div>`,i.classList.add("splatmesh-point"),i.style.position="absolute",i.style.borderRadius="4px",i.style.cursor="pointer";let a=null;i.onclick=()=>{if(a)return;const l=s.target.clone(),u=s.object.position.clone(),c=e.position.clone(),d=Ga?6:2,m=l.clone().sub(u).normalize().clone(),C=c.clone().sub(m.multiplyScalar(d)),I={x:u.x,y:u.y,z:u.z,tx:l.x,ty:l.y,tz:l.z},p={x:C.x,y:C.y,z:C.z,tx:c.x,ty:c.y,tz:c.z};a=new aA(I).to(p,3500),a.easing(be.Sinusoidal.InOut).start().onUpdate(()=>{s.object.position.set(I.x,I.y,I.z),s.target.set(I.tx,I.ty,I.tz)}).onComplete(()=>{a=null})},i.oncontextmenu=l=>l.preventDefault();const o=new yt(i);o.element.style.pointerEvents="none",o.visible=!1,o.applyMatrix4(e.metaMatrix),e.css3dTag=o,t.scene.add(o);const r=l=>e.mapViewer.controls._onMouseWheel(l);i.addEventListener("wheel",r,{passive:!1}),o.dispose=()=>i.removeEventListener("wheel",r),e.onBeforeRender=()=>{a?.update();const l=Ga?60:30,u=100,c=e.position.distanceTo(e.mapViewer.controls.object.position);if(c>l){e.css3dTag.visible=e.opts.controls.object.position.y>2;let d=.002*c;o.scale.set(d,d,d),e.css3dTag.visible=s.object.position.y<10?c<u:!0,e.splatMesh&&(e.splatMesh.visible=!1)}else{if(!e.active){e.splatMesh&&(e.splatMesh.visible=!1);let d=.002*c;o.scale.set(d,d,d),e.css3dTag.visible=!0,e.splatMesh?.boundBox&&(e.splatMesh.boundBox.visible=!1);return}if(e.lastActiveTime=Date.now(),e.css3dTag.visible=!1,e.splatMesh)e.splatMesh.visible=!0;else{const d=e.meta,f={...e.opts};d.autoCut&&(f.bigSceneMode=!0);const m=new Bt(f);e.splatMesh=m,e.opts.scene.add(m),m.meta=d;const C=d.watermark||d.name||"";d.showWatermark=d.showWatermark!==!1,m.fire(Is,C,!0,!1),m.addModel({url:d.url},d)}e.splatMesh.meta.showBoundBox&&(e.splatMesh.boundBox.visible=!0)}},e.onAfterRender=()=>{e.splatMesh&&(!e.active||Date.now()-e.lastActiveTime>60*1e3)&&setTimeout(()=>{e.splatMesh?.dispose(),e.splatMesh=null},5)}}dispose(){const t=this;t.disposed||(t.disposed=!0,t.opts.scene.remove(t.css3dTag),t.splatMesh?.dispose(),t.meta=null,t.splatMesh=null,t.opts=null,t.css3dTag=null,t.mapViewer=null,t.metaMatrix=null)}}class cA extends A.EventDispatcher{constructor(t={}){console.info("Reall3dMapViewer",Tn),super(),this.clock=new A.Clock,this.updateTime=0,this.disposed=!1;const e=this,s=new kn;e.events=s;const i=(r,l,u)=>s.on(r,l,u),a=(r,...l)=>s.fire(r,...l);e.tileMap=nA();const o=eA(t);i(F,()=>o),Gs(s),ti(s),tA(s),Ua(s),Wa(s),e.camera=new A.PerspectiveCamera(60,1,.01,100),i(q,()=>e.camera),e.container=o.root,e.renderer=a(mo),e.scene=a(Co),e.controls=a(yo),e.ambLight=new A.AmbientLight(16777215,1),e.scene.add(e.ambLight),e.dirLight=a(Io),e.scene.add(e.dirLight),e.scene.add(e.tileMap),e.container.appendChild(e.renderer.domElement),Na(s),iA(s),window.addEventListener("resize",e.resize.bind(e)),e.resize(),e.renderer.setAnimationLoop(e.animate.bind(e)),wt&&e.controls._dollyOut&&e.controls._dollyOut(.75),i(nn,()=>e.dispose()),i(fe,()=>{a(tn),e.controls.update(),a(bo),a(an)},!0),i(pe,()=>{e.tileMap.update(e.camera);try{e.renderer.render(e.scene,e.camera)}catch(r){console.warn(r.message)}},!0),i(dn,()=>{e.dispatchEvent({type:"update",delta:e.clock.getDelta()}),e.updateTime=Date.now(),a(Mt)&&a(rt,{fps:a($e),realFps:a(en),fov:a(Se),position:a(se,a(Lt)),lookAt:a(se,a(_t)),lookUp:a(se,a(ue))})},!0)}addScenes(t){const e=(a,o,r)=>this.events.on(a,o,r),s=(a,...o)=>this.events.fire(a,...o),i=this;fetch(t,{mode:"cors",credentials:"omit",cache:"reload"}).then(a=>a.ok?a.json():{}).then(a=>{const o=new A.Vector3().fromArray(a.position||[17e3,3e4,-35e3]),r=new A.Vector3().fromArray(a.lookAt||[17e3,0,-35e3]);i.controls.object.position.copy(o),i.controls.target.copy(r),i.dirLight.target.position.copy(r),e(Es,()=>a),s(mn,a.flyPositions||[]),s(Cn,a.flyTargets||[]);const l=new Set;for(let u of a.scenes)l.has(u)||(new rA(u,i),l.add(u))}).catch(a=>{console.error(a.message)})}fire(t,e,s){const i=this;t===2&&i.events.fire(Te),t===3&&i.events.fire(qt,!0),t===4&&i.events.fire(Le),t===5&&i.events.fire(Re)}resize(){const t=this;if(t.disposed)return;const{width:e,height:s,top:i,left:a}=t.container.getBoundingClientRect();t.renderer.setPixelRatio(Math.min(devicePixelRatio,2)),t.renderer.setSize(e,s),t.camera.aspect=e/s,t.camera.updateProjectionMatrix();const o=t.events.fire(hs);o.setSize(e,s),o.domElement.style.position="absolute",o.domElement.style.left=`${a}px`,o.domElement.style.top=`${i}px`}animate(){const t=this,e=(s,...i)=>t.events.fire(s,...i);e(qe),Date.now()-t.updateTime>17&&(e(fe),e(pe),e(dn))}dispose(){const t=this;if(t.disposed)return;t.disposed=!0;const e=t.renderer.domElement;t.events.fire(ms),t.events.fire(Eo),t.renderer.clear(),t.renderer.dispose(),t.events.clear(),t.scene=null,t.renderer=null,t.camera=null,t.controls=null,t.ambLight=null,t.dirLight=null,t.container.removeChild(e),t.container.classList.add("hidden"),t.container=null,t.clock=null,t.events=null,t.tileMap=null,document.querySelector("#gsviewer .debug.dev-panel")?.classList?.remove("map")}}Ct.Reall3dMapViewer=cA,Ct.Reall3dViewer=$l,Ct.SplatMesh=Bt,Object.defineProperty(Ct,Symbol.toStringTag,{value:"Module"})}));
