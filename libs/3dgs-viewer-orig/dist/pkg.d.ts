import { AmbientLight } from 'three';
import { DirectionalLight } from 'three';
import { EventDispatcher } from 'three';
import { MapControls } from 'three/examples/jsm/controls/MapControls.js';
import { Mesh } from 'three';
import { Object3D } from 'three';
import { OrbitControls } from 'three/examples/jsm/Addons.js';
import { PerspectiveCamera } from 'three';
import { Renderer } from 'three';
import { Scene } from 'three';
import * as tt from '@gotoeasy/three-tile';
import { WebGLRenderer } from 'three';

declare class BoundBox extends Object3D {
    private boxLines;
    constructor(minX?: number, minY?: number, minZ?: number, maxX?: number, maxY?: number, maxZ?: number);
    update(minX: number, minY: number, minZ: number, maxX: number, maxY: number, maxZ: number, show?: boolean): void;
    dispose(): void;
}

/**
 * 相机参数信息
 */
export declare interface CameraInfo {
    /**
     * 相机视场
     */
    fov?: number;
    /**
     * 相机近截面距离
     */
    near?: number;
    /**
     * 相机远截面距离
     */
    far?: number;
    /**
     * 相机宽高比
     */
    aspect?: number;
    /**
     * 相机位置
     */
    position: number[];
    /**
     * 相机注视点
     */
    lookAt?: number[];
    /**
     * 相机上向量
     */
    lookUp?: number[];
}

declare class Events {
    private map;
    constructor();
    on(key: number, fn?: Function, multiFn?: boolean): Function | Function[];
    fire(key: number, ...args: any): any;
    tryFire(key: number, ...args: any): any;
    off(key: number): void;
    clear(): void;
}

/**
 * Metadata
 */
export declare interface MetaData {
    /** Name */
    name?: string;
    /** Version */
    version?: string;
    /** Update date (YYYYMMDD) */
    updateDate?: number;
    /** Enable auto-rotation */
    autoRotate?: boolean;
    /** Enable debug mode */
    debugMode?: boolean;
    /** Enable point-cloud rendering */
    pointcloudMode?: boolean;
    /** Maximum render count for mobile */
    maxRenderCountOfMobile?: number;
    /** Maximum render count for PC */
    maxRenderCountOfPc?: number;
    /** Maximum download count for mobile splats */
    mobileDownloadLimitSplatCount?: number;
    /** Maximum download count for PC splats */
    pcDownloadLimitSplatCount?: number;
    /** Meter scale factor */
    meterScale?: number;
    /** Text watermark */
    watermark?: string;
    /** Display watermark */
    showWatermark?: boolean;
    /** Display bounding box */
    showBoundBox?: boolean;
    /** Camera parameters */
    cameraInfo?: CameraInfo;
    /** Annotations */
    marks?: any[];
    /** Fly-through camera positions */
    flyPositions?: number[];
    /** Fly-through camera look-at points */
    flyTargets?: number[];
    /** render quality level */
    qualityLevel?: number;
    /** sort type */
    sortType?: number;
    /** depth near rate */
    depthNearRate?: number;
    /** depth near value */
    depthNearValue?: number;
    /** 最小可渲染直径像素 */
    minPixelDiameter?: number;
    /** 最大直径像素 */
    maxPixelDiameter?: number;
    /** 最小可渲染透明度 */
    minAlpha?: number;
    /** Enable particle loading effect (small scenes) */
    particleMode?: boolean;
    /** Auto-cut count */
    autoCut?: number;
    /** Transformation matrix */
    transform?: number[];
    /** Geolocation (EPSG:4326 WGS 84) */
    WGS84?: number[];
    /** Model URL */
    url?: string;
}

/**
 * Gaussian Model Options
 */
export declare interface ModelOptions {
    /**
     * Model URL
     */
    url: string;
    /**
     * Model format (ply | splat | spx | spz | sog | obj), auto-detected by default
     */
    format?: 'ply' | 'splat' | 'spx' | 'spz' | 'sog' | 'obj';
    /**
     * Whether to force re-download
     */
    fetchReload?: boolean;
}

/**
 * Built-in Map viewer with Gaussian Splatting model support
 */
export declare class Reall3dMapViewer extends EventDispatcher<tt.plugin.GLViewerEventMap> {
    scene: Scene;
    renderer: WebGLRenderer;
    camera: PerspectiveCamera;
    controls: MapControls;
    ambLight: AmbientLight;
    dirLight: DirectionalLight;
    container: HTMLElement;
    tileMap: tt.TileMap;
    events: Events;
    private clock;
    private updateTime;
    private disposed;
    constructor(options?: Reall3dMapViewerOptions);
    /**
     * 打开地图场景
     * @param 场景索引文件地址
     */
    addScenes(urlScenesJson: string): void;
    fire(n: number, p1?: any, p2?: any): void;
    private resize;
    private animate;
    /**
     * 销毁
     */
    dispose(): void;
}

/**
 * Configuration options for Reall3dMapViewer
 */
export declare interface Reall3dMapViewerOptions {
    /**
     * Container element or its selector (default: '#map').
     * Will be automatically created if not found.
     */
    root?: HTMLElement | string;
    /**
     * Enable keyboard controls (default: false)
     */
    enableKeyboard?: boolean;
    /**
     * Minimum panning boundaries (default: [-20000, 0.1, -60000])
     */
    minPan?: number[];
    /**
     * Maximum panning boundaries (default: [50000, 10000, 0])
     */
    maxPan?: number[];
    /**
     * Background color (default: '#dbf0ff')
     */
    background?: string;
    /**
     * Debug mode flag (default: false in production)
     */
    debugMode?: boolean;
    /**
     * Edit mode flag (default: false)
     */
    editMode?: boolean;
}

/**
 * Built-in Gaussian Splatting model viewer
 */
export declare class Reall3dViewer {
    private disposed;
    private splatMesh;
    private events;
    private metaMatrix;
    needUpdate: boolean;
    constructor(opts?: Reall3dViewerOptions);
    private init;
    /**
     * 允许拖拽本地文件进行渲染
     */
    private enableDropLocalFile;
    /**
     * 刷新
     */
    update(): void;
    fire(n: number, p1?: any, p2?: any): void;
    /**
     * 设定或者获取最新配置项
     * @param opts 配置项
     * @returns 最新配置项
     */
    options(opts?: Reall3dViewerOptions): Reall3dViewerOptions;
    /**
     * 重置
     */
    reset(opts?: Reall3dViewerOptions): void;
    /**
     * 光圈过渡切换显示
     * @returns
     */
    switchDeiplayMode(): void;
    /**
     * 添加场景
     * @param sceneUrl 场景地址
     */
    addScene(sceneUrl: string): Promise<void>;
    /**
     * 添加要渲染的高斯模型（小场景模式）
     * @param urlOpts 高斯模型链接或元数据文件链接或选项
     */
    addModel(urlOpts: string | ModelOptions): Promise<void>;
    /**
     * 根据需要暴露的接口
     */
    private initGsApi;
    /**
     * 销毁渲染器不再使用
     */
    dispose(): void;
    /**
     * 启用或禁用第一人称模式
     *
     * 第一人称模式提供类似FPS游戏的控制体验：
     * - 使用WASD键控制相机前后左右移动
     * - 使用箭头键控制相机上下移动
     * - 使用鼠标左键拖动来旋转视角
     *
     * @example
     * // 启用第一人称模式
     * viewer.enableFirstPersonMode(true);
     *
     * // 启用第一人称模式并设置较快的移动速度
     * viewer.enableFirstPersonMode(true, 0.01);
     *
     * // 禁用第一人称模式，恢复默认控制
     * viewer.enableFirstPersonMode(false);
     *
     * @param enable 是否启用第一人称模式，默认为true
     * @param cameraMoveSpeed 相机移动速度，值越大移动越快，默认为0.005
     * @returns 当前的ViewerOptions
     */
    enableFirstPersonMode(enable?: boolean, cameraMoveSpeed?: number): Reall3dViewerOptions;
}

/**
 * Configuration options for Reall3dViewer
 */
export declare interface Reall3dViewerOptions {
    /**
     * Specify a custom renderer instance. If undefined, one will be created automatically.
     */
    renderer?: Renderer | undefined;
    /**
     * Specify a custom scene instance. If undefined, one will be created automatically.
     */
    scene?: Scene | undefined;
    /**
     * Specify a custom camera instance. If undefined, one will be created automatically.
     */
    camera?: PerspectiveCamera | undefined;
    /**
     * Orbit controls instance
     */
    controls?: OrbitControls;
    /**
     * Renderer event manager
     */
    viewerEvents?: Events | undefined;
    /**
     * Debug mode flag. Defaults to false in production.
     */
    debugMode?: boolean | undefined;
    /**
     * Large scene mode flag. Cannot be modified after initialization.
     */
    bigSceneMode?: boolean;
    /**
     * Point cloud rendering mode. Defaults to true.
     * Can be dynamically updated via viewer.options()
     */
    pointcloudMode?: boolean | undefined;
    /**
     * Maximum renderable Gaussian points count for mobile devices.
     * Can be dynamically updated via viewer.options()
     */
    maxRenderCountOfMobile?: number | undefined;
    /**
     * Maximum renderable Gaussian points count for PC.
     * Can be dynamically updated via viewer.options()
     */
    maxRenderCountOfPc?: number | undefined;
    /**
     * Color brightness factor. Defaults to 1.1.
     */
    lightFactor?: number | undefined;
    /**
     * Container element or its selector. Defaults to '#gsviewer'.
     * If no container is found when creating canvas, one will be automatically created under body.
     */
    root?: HTMLElement | string | undefined;
    /**
     * Camera field of view. Defaults to 45.
     */
    fov?: number | undefined;
    /**
     * Camera near clipping plane. Defaults to 0.1.
     */
    near?: number | undefined;
    /**
     * Camera far clipping plane. Defaults to 1000.
     */
    far?: number | undefined;
    /**
     * Camera position. Defaults to [0, -5, 15].
     */
    position?: number[] | undefined;
    /**
     * Camera look-at target. Defaults to [0, 0, 0].
     */
    lookAt?: number[] | undefined;
    /**
     * Camera up vector. Defaults to [0, -1, 0].
     */
    lookUp?: number[] | undefined;
    /**
     * Auto-rotation flag. Defaults to true.
     * Can be dynamically updated via viewer.options()
     */
    autoRotate?: boolean | undefined;
    /**
     * Auto-rotation speed in degrees per second. Defaults to 2.0.
     * Can be dynamically updated via viewer.options()
     */
    autoRotateSpeed?: number | undefined;
    /**
     * Damping effect flag. Defaults to true.
     */
    enableDamping?: boolean | undefined;
    /**
     * Zoom control flag. Defaults to true.
     */
    enableZoom?: boolean | undefined;
    /**
     * Rotation control flag. Defaults to true.
     */
    enableRotate?: boolean | undefined;
    /**
     * Pan control flag. Defaults to true.
     */
    enablePan?: boolean | undefined;
    /**
     * Minimum panning boundaries [x, y, z]. Defaults to undefined (no limit).
     */
    minPan?: number[] | undefined;
    /**
     * Maximum panning boundaries [x, y, z]. Defaults to undefined (no limit).
     */
    maxPan?: number[] | undefined;
    /**
     * Pan direction restriction. Defaults to 'both'.
     * - 'horizontal': Only allow horizontal (x-axis) panning
     * - 'vertical': Only allow vertical (y-axis) panning
     * - 'both': Allow both horizontal and vertical panning
     */
    panDirection?: 'horizontal' | 'vertical' | 'both' | undefined;
    /**
     * Minimum camera distance
     */
    minDistance?: number | undefined;
    /**
     * Maximum camera distance
     */
    maxDistance?: number | undefined;
    /**
     * Minimum polar angle (vertical rotation limit)
     */
    minPolarAngle?: number | undefined;
    /**
     * Maximum polar angle (vertical rotation limit)
     */
    maxPolarAngle?: number | undefined;
    /**
     * Keyboard controls flag. Defaults to true.
     */
    enableKeyboard?: boolean | undefined;
    /**
     * Annotation mode flag. Defaults to false.
     */
    markMode?: boolean | undefined;
    /**
     * Annotation type (point/lines/plans/distance/area/circle)
     */
    markType?: 'point' | 'lines' | 'plans' | 'distance' | 'area' | 'circle' | undefined;
    /**
     * Annotation visibility flag. Defaults to true.
     */
    markVisible?: boolean | undefined;
    /**
     * Meter scale (how many meters per unit length). Defaults to 1.
     */
    meterScale?: number | undefined;
    /**
     * Disable local file drag-and-drop flag. Defaults to false.
     */
    disableDropLocalFile?: boolean | undefined;
    /**
     * Spherical harmonics rendering level. Defaults to the maximum renderable level of model data.
     */
    shDegree?: number | undefined;
    /**
     * Background color (defaults to '#000000')
     */
    background?: string;
    /**
     * Render quality level (1~9, default to QualityLevels.Default5)
     */
    qualityLevel?: number;
    /**
     * Sort type (default to SortTypes.Default1)
     */
    sortType?: number;
    /**
     * disable transition effect on load; defaults to false
     */
    disableTransitionEffectOnLoad?: boolean;
    /**
     * transition effect type; defaults to ModelCenterCirccle
     */
    transitionEffect?: TransitionEffects;
    /**
     * 过渡动画持续时间，单位毫秒，默认4000
     */
    transitionAnimDuration?: number;
    /**
     * Auto play background audio (defaults to false)
     */
    autoPlayBgAudio?: boolean;
    /**
     * 相机移动速度，默认0.005
     */
    cameraMoveSpeed?: number | undefined;
    /**
     * 是否启用自定义控制（WASD移动等），默认false
     */
    useCustomControl?: boolean | undefined;
    /**
     * 是否禁用渐进式加载（从中心向外扩展），默认false
     */
    disableProgressiveLoading?: boolean | undefined;
    /**
     * 是否禁用流式加载（等待全部数据下载完成后再渲染），默认false
     */
    disableStreamLoading?: boolean | undefined;
    /**
     * 性能监控回调函数
     */
    onPerformanceUpdate?: (metrics: {
        firstFrameTime?: number;
        fullLoadTime?: number;
        currentSplatCount?: number;
        totalSplatCount?: number;
        loadProgress?: number;
        phase?: 'loading' | 'firstFrame' | 'complete';
    }) => void;
}

/**
 * Gaussian splatting mesh
 */
export declare class SplatMesh extends Mesh {
    readonly isSplatMesh: boolean;
    meta: MetaData;
    private disposed;
    private events;
    private opts;
    boundBox: BoundBox;
    /**
     * 构造函数
     * @param options 渲染器、场景、相机都应该传入
     */
    constructor(options: SplatMeshOptions);
    /**
     * 设定或者获取最新配置项
     * @param opts 配置项
     * @returns 最新配置项
     */
    options(opts?: SplatMeshOptions): SplatMeshOptions;
    /**
     * 添加渲染指定高斯模型
     * @param opts 高斯模型选项
     * @param meta 元数据
     */
    addModel(opts: ModelOptions, meta?: MetaData): void;
    fire(key: number, ...args: any): any;
    /**
     * 销毁
     */
    dispose(): void;
}

/**
 * Splat mesh configuration options
 */
export declare interface SplatMeshOptions {
    /**
     * Name
     */
    name?: string;
    /**
     * Renderer instance to be used
     */
    renderer: Renderer;
    /**
     * Scene instance to be used
     */
    scene: Scene;
    /**
     * Camera controls
     */
    controls?: OrbitControls;
    /**
     * In VR mode, when no controls is used, pass the camera instead.
     */
    camera?: PerspectiveCamera;
    /**
     * Renderer event manager
     */
    viewerEvents?: Events;
    /**
     * Debug mode flag; defaults to false in production
     */
    debugMode?: boolean;
    /**
     * Large-scene mode flag; cannot be changed after initialization
     */
    bigSceneMode?: boolean;
    /**
     * Point-cloud rendering mode; defaults to true,
     * Can be updated dynamically via viewer.options()
     */
    pointcloudMode?: boolean;
    /**
     * Maximum number of Gaussians to render on mobile devices,
     * Can be updated dynamically via viewer.options()
     */
    maxRenderCountOfMobile?: number;
    /**
     * Maximum number of Gaussians to render on PC,
     * Can be updated dynamically via viewer.options()
     */
    maxRenderCountOfPc?: number;
    /**
     * Brightness scale factor; defaults to 1.0
     */
    lightFactor?: number;
    /**
     * Whether to display a watermark; defaults to true
     */
    showWatermark?: boolean;
    /**
     * Spherical harmonics rendering degree; defaults to 0
     */
    shDegree?: number;
    /**
     * Enable depth testing; defaults to true
     */
    depthTest?: boolean;
    /**
     * Render quality level (1~9, default to QualityLevels.Default5)
     */
    qualityLevel?: number;
    /**
     * Sort type (default to SortTypes.Default1)
     */
    sortType?: number;
    /**
     * map mode, defaults to false
     */
    mapMode?: boolean;
    /**
     * disable transition effect on load; defaults to false
     */
    disableTransitionEffectOnLoad?: boolean;
    /**
     * transition effect type; defaults to ModelCenterCirccle
     */
    transitionEffect?: TransitionEffects;
    /**
     * 过渡动画持续时间，单位毫秒，默认4000
     */
    transitionAnimDuration?: number;
    /**
     * 最小可渲染直径像素
     */
    minPixelDiameter?: number;
    /**
     * 最大直径像素
     */
    maxPixelDiameter?: number;
    /**
     * 最小可渲染透明度
     */
    minAlpha?: number;
    /**
     * 是否禁用渐进式加载（从中心向外扩展），默认false
     */
    disableProgressiveLoading?: boolean | undefined;
    /**
     * 是否禁用流式加载（等待全部数据下载完成后再渲染），默认false
     */
    disableStreamLoading?: boolean | undefined;
    /**
     * 性能监控回调函数
     */
    onPerformanceUpdate?: (metrics: {
        firstFrameTime?: number;
        fullLoadTime?: number;
        currentSplatCount?: number;
        totalSplatCount?: number;
        loadProgress?: number;
        phase?: 'loading' | 'firstFrame' | 'complete';
    }) => void;
}

/** 过渡效果 */
declare enum TransitionEffects {
    ModelCenterCirccle = 1,
    ScreenCenterCircle = 2,
    ScreenMiddleToLeftRight = 3,
    ScreenMiddleToTopBottom = 4
}

export { }
