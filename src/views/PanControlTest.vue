<template>
  <div class="pan-control-test">
    <div class="controls-panel">
      <h3>Pan Control Settings</h3>
      
      <div class="control-group">
        <label>
          <input 
            v-model="config.enablePan" 
            type="checkbox"
          > Enable Pan
        </label>
      </div>
      
      <div class="control-group">
        <label>Pan Direction:</label>
        <select v-model="config.panDirection">
          <option value="both">Both (Default)</option>
          <option value="horizontal">Horizontal Only</option>
          <option value="vertical">Vertical Only</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>Min Pan (X,Y,Z):</label>
        <input 
          v-model="minPanInput" 
          type="text" 
          placeholder="e.g., -10,-5,-10"
          @blur="updateMinPan"
        >
      </div>
      
      <div class="control-group">
        <label>Max Pan (X,Y,Z):</label>
        <input 
          v-model="maxPanInput" 
          type="text" 
          placeholder="e.g., 10,5,10"
          @blur="updateMaxPan"
        >
      </div>
      
      <div class="control-group">
        <button @click="applySettings" class="apply-btn">Apply Settings</button>
        <button @click="resetSettings" class="reset-btn">Reset</button>
      </div>
      
      <div class="info">
        <strong>Instructions:</strong><br>
        • Use mouse to drag and pan the camera<br>
        • On mobile, use two fingers to pan<br>
        • Try different pan direction settings<br>
        • Set min/max pan values to limit range<br><br>
        <strong>Current Config:</strong><br>
        • enablePan: {{ config.enablePan }}<br>
        • panDirection: {{ config.panDirection }}<br>
        • minPan: {{ config.minPan || 'undefined' }}<br>
        • maxPan: {{ config.maxPan || 'undefined' }}
      </div>
    </div>
    
    <div class="viewer-container">
      <div id="gsviewer" class="viewer"></div>
      
      <div v-if="isLoading" class="loading">
        Loading viewer...
      </div>
      
      <div v-if="error" class="error">
        Error: {{ error }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useViewer } from '@/composables/useViewer'
import type { ViewerConfig } from '@/types/project'

// 响应式配置
const config = reactive<ViewerConfig>({
  root: '#gsviewer',
  debugMode: true,
  useCustomControl: false,
  enablePan: true,
  panDirection: 'both',
  minDistance: 3,
  maxDistance: 15,
  autoRotate: false
})

// 输入框的值
const minPanInput = ref('')
const maxPanInput = ref('')

// 使用 useViewer composable
const { viewer, isLoading, error, initViewer, destroyViewer } = useViewer(config)

// 更新 minPan
const updateMinPan = () => {
  if (minPanInput.value.trim()) {
    try {
      const values = minPanInput.value.split(',').map(v => parseFloat(v.trim()))
      if (values.length === 3 && values.every(v => !isNaN(v))) {
        config.minPan = values
      }
    } catch (e) {
      console.error('Invalid minPan format:', e)
    }
  } else {
    config.minPan = undefined
  }
}

// 更新 maxPan
const updateMaxPan = () => {
  if (maxPanInput.value.trim()) {
    try {
      const values = maxPanInput.value.split(',').map(v => parseFloat(v.trim()))
      if (values.length === 3 && values.every(v => !isNaN(v))) {
        config.maxPan = values
      }
    } catch (e) {
      console.error('Invalid maxPan format:', e)
    }
  } else {
    config.maxPan = undefined
  }
}

// 应用设置
const applySettings = async () => {
  console.log('Applying new config:', config)

  // 重新初始化 viewer
  if (viewer) {
    destroyViewer()
  }

  await initViewer()

  // 可以加载一个测试模型
  // await loadModel('path/to/test/model.ply')
}

// 重置设置
const resetSettings = () => {
  config.enablePan = true
  config.panDirection = 'both'
  config.minPan = undefined
  config.maxPan = undefined
  minPanInput.value = ''
  maxPanInput.value = ''
  
  applySettings()
}

onMounted(() => {
  initViewer()
})

onUnmounted(() => {
  destroyViewer()
})
</script>

<style scoped>
.pan-control-test {
  display: flex;
  height: 100vh;
  font-family: Arial, sans-serif;
}

.controls-panel {
  width: 300px;
  padding: 20px;
  background: #f8f9fa;
  border-right: 1px solid #dee2e6;
  overflow-y: auto;
}

.controls-panel h3 {
  margin-top: 0;
  color: #495057;
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.control-group input[type="checkbox"] {
  margin-right: 8px;
}

.control-group input[type="text"],
.control-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.apply-btn, .reset-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 10px;
}

.apply-btn {
  background: #007bff;
  color: white;
}

.apply-btn:hover {
  background: #0056b3;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.reset-btn:hover {
  background: #545b62;
}

.info {
  background: #e9ecef;
  padding: 15px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
  color: #495057;
}

.viewer-container {
  flex: 1;
  position: relative;
  background: #000;
}

.viewer {
  width: 100%;
  height: 100%;
}

.loading, .error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
}

.error {
  background: rgba(220, 53, 69, 0.9);
}
</style>
