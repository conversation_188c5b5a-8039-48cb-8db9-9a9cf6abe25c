/**
 * 项目配置接口
 */
export interface ProjectConfig {
  /** 项目ID */
  id: string
  /** 项目名称 */
  name: string
  /** 项目描述 */
  description?: string
  /** 默认模型URL */
  defaultModelUrl?: string
  /** 自定义配置 */
  customConfig?: Record<string, any>
}

/**
 * 相机配置接口
 */
export interface CameraConfig {
  /** 相机位置 [x, y, z] */
  position?: [number, number, number]
  /** 相机注视点 [x, y, z] */
  lookAt?: [number, number, number]
  /** 相机上向量 [x, y, z] */
  lookUp?: [number, number, number]
  /** 视场角度 */
  fov?: number
  /** 近裁剪面 */
  near?: number
  /** 远裁剪面 */
  far?: number
  /** 相机按键移动速度 */
  cameraMoveSpeed?: number
  /** 相机最小距离 */
  minDistance?: number
  /** 相机最大距离 */
  maxDistance?: number
  /** 是否启用平移控制 */
  enablePan?: boolean
  /** 平移方向限制 */
  panDirection?: 'horizontal' | 'vertical' | 'both'
  /** 最小平移边界 [x, y, z] */
  minPan?: number[]
  /** 最大平移边界 [x, y, z] */
  maxPan?: number[]
  /** 是否启用旋转控制 */
  enableRotate?: boolean
  /** 是否启用缩放控制 */
  enableZoom?: boolean
}

/**
 * Viewer配置接口
 */
export interface ViewerConfig extends CameraConfig {
  /** 是否启用自定义控制 */
  useCustomControl?: boolean
  /** 是否禁用过渡效果 */
  disableTransitionEffectOnLoad?: boolean
  /** 渐进式加载效果 */
  transitionEffect?: number
  /** 渐进式动画时长 */
  transitionAnimDuration?: number
  /** 调试模式 */
  debugMode?: boolean
  /** 容器选择器 */
  root?: string
  /** 渲染质量(1~9) */
  qualityLevel?: number
  /** 其他配置项 */
  [key: string]: any
}

/**
 * URL参数接口
 */
export interface ViewerParams {
  /** 编码后的模型URL */
  modelUrl?: string
  /** 项目ID */
  projectId?: string
  /** 其他参数 */
  [key: string]: string | undefined
}
