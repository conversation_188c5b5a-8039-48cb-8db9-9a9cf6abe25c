/**
 * 用户信息接口
 */
export interface IUserInfo {
  /** 用户ID */
  userId: string
  /** 昵称 */
  nickName: string
  /** 头像图片 */
  avatarPic: string
  /** 最后活跃时间 */
  lastActivate: string
}

/**
 * 场景详情接口
 */
export interface ISceneDetail {
  /** 项目ID */
  projectId: number
  /** 项目名称 */
  name: string
  /** 项目类型 */
  type: number
  /** 项目状态 */
  state: number
  /** 用户ID */
  userId: string
  /** 快照文件URL */
  snapshotFileUrl: string
  /** 创建时间 */
  createTime: string
  /** 评分 */
  score: number
  /** 点赞数 */
  likes: number
  /** 是否已点赞 */
  isMyLikes: boolean | null
  /** 用户信息 */
  user: IUserInfo
  /** 项目描述 */
  description: string | null
  /** 模型文件列表 */
  models: string[]
  /** VPS状态 */
  vpsState: number
  /** AR标签 */
  arTag: number
  /** 是否访问过 */
  isVisit: number
  /** 模型中心点 */
  modelCenter: string
  /** 模型半径 */
  modelRadius: string
}

/**
 * 获取场景详情请求参数
 */
export interface IGetSceneDetailParams {
  /** 项目ID */
  projectId: string
}

/**
 * 场景模型文件接口
 */
export interface ISceneModels {
  /** Transform配置文件URL */
  transformUrl?: string
  /** 点云文件URL */
  splatUrl?: string
  /** Settings配置文件URL */
  settingsUrl?: string
}

/**
 * Transform配置中的相机数据
 */
export interface ITransformCameraData {
  /** 相机位置 */
  position?: [number, number, number]
  /** 相机朝向 */
  at?: [number, number, number]
  /** 相机上向量 */
  up?: [number, number, number]
  /** 焦距相关 */
  fy?: number
  /** 图像高度 */
  height?: number
  /** 其他属性 */
  [key: string]: any
}

/**
 * 解析后的场景数据
 */
export interface IParsedSceneData {
  /** 场景详情 */
  scene: ISceneDetail
  /** 解析后的模型文件 */
  models: ISceneModels
  /** 相机配置（从transform.json解析） */
  cameraConfig?: import('./project').CameraConfig
  /** 动画轨道（从settings.json解析） */
  animTracks?: any[]
}
