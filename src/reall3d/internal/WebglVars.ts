// ==============================================
// Copyright (c) 2025 reall3d.com, MIT license
// ==============================================
export const VarCurrentVisibleRadius = 'currentVisibleRadius';
export const VarCurrentLightRadius = 'currentLightRadius';
export const VarTransitionEffect = 'transitionEffect';
export const VarSplatShTexture12 = 'splatShTexture12';
export const VarMaxPixelDiameter = 'maxPixelDiameter';
export const VarMinPixelDiameter = 'minPixelDiameter';
export const VarActiveFlagValue = 'activeFlagValue';
export const VarSplatShTexture3 = 'splatShTexture3';
export const VarPerformanceNow = 'performanceNow';
export const VarPerformanceAct = 'performanceAct';
export const VarWaterMarkColor = 'waterMarkColor';
export const VarShowWaterMark = 'showWaterMark';
export const VarUseSimilarExp = 'useSimilarExp';
export const VarSplatTexture0 = 'splatTexture0';
export const VarSplatTexture1 = 'splatTexture1';
export const VarParticleMode = 'particleMode';
export const VarBigSceneMode = 'bigSceneMode';
export const VarLightFactor = 'lightFactor';
export const VarDebugEffect = 'debugEffect';
export const VarUsingIndex = 'usingIndex';
export const VarSplatIndex = 'splatIndex';
export const VarMaxRadius = 'maxRadius';
export const VarFlagValue = 'flagValue';
export const VarVPosition = 'vPosition';
export const VarPointMode = 'pointMode';
export const VarMarkPoint = 'markPoint';
export const VarShDegree = 'shDegree';
export const VarViewport = 'viewport';
export const VarMinAlpha = 'minAlpha';
export const VarVColor = 'vColor';
export const VarFocal = 'focal';
export const VarTopY = 'topY';
export const VarAabbCenter = 'aabbCenter';
export const VarLookAtCenter = 'lookAtCenter';
