// ==============================================
// Copyright (c) 2025 reall3d.com, MIT license
// ==============================================
body {
    overflow: hidden;
    margin: 0;
    height: 100vh;
    width: 100vw;
    font-family: PingFang SC, Microsoft YaHei, sans-serif;
    font-size: 14px;
    background: black;
    color: white;
}

#map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.gsviewer-canvas {
    width: 100%;
    height: 100%;
    touch-action: none;
}

#gsviewer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .logo {
        z-index: 9999;
        width: 40px;
        height: 40px;
        animation-fill-mode: backwards;
    }

    .logo.loading {
        animation: spin infinite 2s linear;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    .hidden {
        display: none !important;
    }

    .info {
        z-index: 9999;
        position: absolute;
        bottom: 20px;
        right: 20px;
        color: #eee;
        background-color: #333;
        padding: 3px 10px;
    }

    .debug {
        z-index: 9999;
        position: absolute;
        bottom: 20px;
        left: 20px;
        color: #eee;
        background-color: #333;
        padding: 5px 10px;
        opacity: 0.85;
        pointer-events: none;

        table {
            width: 100%;
            font-size: 14px;
        }

        &.map .map-hidden {
            display: none;
        }
    }

    .operation {
        z-index: 9999;
        position: absolute;
        top: 30px;
        right: 20px;
        color: #eee;
        background-color: #333;
        padding: 5px 5px;
        opacity: 0.85;

        table {
            width: 100%;
            font-size: 14px;

            tr {
                margin-top: 10px;
                margin-bottom: 10px;

                span {
                    cursor: pointer;
                    padding-left: 2px;
                    padding-right: 2px;
                    user-select: none;

                    &.disable {
                        cursor: not-allowed;
                    }
                }

                span:hover {
                    background-color: #999;
                }
            }

            &.plus tr.tr-data {
                display: none;
            }
        }
    }

    // 画字
    .grid-wrapper-text {
        display: flex !important;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }

    .grid-container {
        display: grid;
        grid-template-columns: repeat(20, 1fr);
        grid-template-rows: repeat(20, 1fr);
        gap: 1px;
        width: 400px;
        height: 400px;
        background-color: #aaa;
        position: relative;
        overflow: visible;
        user-select: none;
    }

    .grid-item {
        background-color: #000;
        transition: background-color 0.3s;
        user-select: none;
    }

    .yellow {
        background-color: #ff0;
    }

    .grid-line {
        background-color: #ddd;
        position: absolute;
        z-index: 5;
    }

    .grid-line.vertical {
        width: 100%;
        height: 1px;
        top: 50%;
    }

    .grid-line.horizontal {
        height: 100%;
        width: 1px;
        left: 50%;
    }

    .grid-line.left {
        height: 100%;
        width: 1px;
        left: 0%;
        background-color: #aaa;
    }

    .grid-line.top {
        width: 100%;
        height: 1px;
        top: 0%;
        background-color: #aaa;
    }

    .overlay-text {
        position: absolute;
        padding-top: 20px;
        top: 0px;
        left: 0;
        width: 400px;
        height: 400px;
        pointer-events: none;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 380px;
        color: rgba(255, 255, 255, 0.3);
        z-index: 10;
    }
}
