// ==============================================
// Copyright (c) 2025 reall3d.com, MIT license
// ==============================================
import { Matrix4, PerspectiveCamera, Renderer, Scene } from 'three';
import { Events } from '../../events/Events';
import { OrbitControls } from 'three/examples/jsm/Addons.js';

/**
 * Splat mesh configuration options
 */
export interface SplatMeshOptions {
    /**
     * Name
     */
    name?: string;

    /**
     * Renderer instance to be used
     */
    renderer: Renderer;

    /**
     * Scene instance to be used
     */
    scene: Scene;

    /**
     * Camera controls
     */
    controls?: OrbitControls;

    /**
     * In VR mode, when no controls is used, pass the camera instead.
     */
    camera?: PerspectiveCamera;

    /**
     * Renderer event manager
     */
    viewerEvents?: Events;

    /**
     * Debug mode flag; defaults to false in production
     */
    debugMode?: boolean;

    /**
     * Large-scene mode flag; cannot be changed after initialization
     */
    bigSceneMode?: boolean;

    /**
     * Point-cloud rendering mode; defaults to true,
     * Can be updated dynamically via viewer.options()
     */
    pointcloudMode?: boolean;

    /**
     * Maximum number of Gaussians to render on mobile devices,
     * Can be updated dynamically via viewer.options()
     */
    maxRenderCountOfMobile?: number;

    /**
     * Maximum number of Gaussians to render on PC,
     * Can be updated dynamically via viewer.options()
     */
    maxRenderCountOfPc?: number;

    /**
     * Brightness scale factor; defaults to 1.0
     */
    lightFactor?: number;

    /**
     * Whether to display a watermark; defaults to true
     */
    showWatermark?: boolean;

    /**
     * Spherical harmonics rendering degree; defaults to 0
     */
    shDegree?: number;

    /**
     * Enable depth testing; defaults to true
     */
    depthTest?: boolean;

    /**
     * Render quality level (1~9, default to QualityLevels.Default5)
     */
    qualityLevel?: number;

    /**
     * Sort type (default to SortTypes.Default1)
     */
    sortType?: number;

    /**
     * map mode, defaults to false
     */
    mapMode?: boolean;

    /**
     * disable transition effect on load; defaults to false
     */
    disableTransitionEffectOnLoad?: boolean;

    /**
     * transition effect type; defaults to ModelCenterCirccle
     */
    transitionEffect?: TransitionEffects;

    /**
     * 过渡动画持续时间，单位毫秒，默认4000
     */
    transitionAnimDuration?: number;

    /**
     * 最小可渲染直径像素
     */
    minPixelDiameter?: number;

    /**
     * 最大直径像素
     */
    maxPixelDiameter?: number;

    /**
     * 最小可渲染透明度
     */
    minAlpha?: number;

    /**
     * 是否禁用渐进式加载（从中心向外扩展），默认false
     */
    disableProgressiveLoading?: boolean | undefined;

    /**
     * 是否禁用流式加载（等待全部数据下载完成后再渲染），默认false
     */
    disableStreamLoading?: boolean | undefined;

    /**
     * 性能监控回调函数
     */
    onPerformanceUpdate?: (metrics: {
        firstFrameTime?: number;
        fullLoadTime?: number;
        currentSplatCount?: number;
        totalSplatCount?: number;
        loadProgress?: number;
        phase?: 'loading' | 'firstFrame' | 'complete';
    }) => void;
}

/** 过渡效果 */
export enum TransitionEffects {
    ModelCenterCirccle = 1,
    ScreenCenterCircle = 2,
    ScreenMiddleToLeftRight = 3,
    ScreenMiddleToTopBottom = 4,
}
