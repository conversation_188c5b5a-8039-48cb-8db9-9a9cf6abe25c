<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <div class="app">
    <header class="app-header">
      <div class="logo-container">
        <img src="@/assets/shuyuan_logo.png" alt="Logo" class="logo" />
      </div>
    </header>
    <RouterView />
  </div>
</template>

<style>
/* 全局重置样式，确保3DGS Viewer占满全屏 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

#app {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.app {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-header {
  position: fixed;
  top: 1rem;
  left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 1000;
}

.logo-container {
  width: 15%;
  flex-shrink: 0;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .logo-container {
    width: 30%;
  }
}
</style>
