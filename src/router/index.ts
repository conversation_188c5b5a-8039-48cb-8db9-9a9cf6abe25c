import { createRouter, createWebHistory } from 'vue-router'
import ProjectView from '../views/ProjectView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 根路径重定向到village-mini项目
    {
      path: '/',
      redirect: '/project/village-mini',
    },
    // 项目页面路由
    {
      path: '/project/:projectId',
      name: 'project',
      component: ProjectView,
      props: true,
    },
    // 兼容性路由：直接访问项目名称
    {
      path: '/village-mini',
      redirect: '/project/village-mini',
    },
    {
      path: '/unicity-mini',
      redirect: '/project/unicity-mini',
    },
    {
      path: '/unicity-web',
      redirect: '/project/unicity-web',
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      redirect: '/project/village-mini',
    },
  ],
})

export default router
