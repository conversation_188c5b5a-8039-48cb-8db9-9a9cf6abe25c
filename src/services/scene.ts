import request from '@/utils/http'
import type { ICommonResponse } from '@/types/common'
import type { ISceneDetail, IGetSceneDetailParams, ITransformCameraData } from '@/types/scene'

const baseUrl = '/app/api/v1/h5/index/scene'

/**
 * 场景服务类 - 只包含纯净的API请求函数
 */
export class SceneService {
  /**
   * 获取场景详情
   * @param params 请求参数
   * @returns 场景详情数据
   */
  static async getSceneDetail(
    params: IGetSceneDetailParams,
  ): Promise<ICommonResponse<ISceneDetail>> {
    const { data } = await request<ICommonResponse<ISceneDetail>>({
      method: 'get',
      url: `${baseUrl}/${params.projectId}`,
      data: {},
    })
    return data
  }

  /**
   * 获取Transform配置文件
   * @param transformUrl transform.json文件URL
   * @returns Transform配置数据
   */
  static async getTransformConfig(transformUrl: string): Promise<ITransformCameraData[]> {
    const { data } = await request<ITransformCameraData[]>({
      method: 'get',
      url: transformUrl,
      data: {},
    })
    return data
  }

  /**
   * 获取其他配置文件（如report.yaml等）
   * @param fileUrl 文件URL
   * @returns 文件内容
   */
  static async getConfigFile(fileUrl: string): Promise<any> {
    const { data } = await request<any>({
      method: 'get',
      url: fileUrl,
      data: {},
    })
    return data
  }
}
