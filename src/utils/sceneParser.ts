import type { ISceneModels, ITransformCameraData } from '@/types/scene'
import type { CameraConfig } from '@/types/project'

/**
 * 场景数据解析工具类
 */
export class SceneParser {
  /**
   * 解析场景模型文件
   * @param models 模型文件URL数组
   * @returns 解析后的模型文件对象
   */
  static parseSceneModels(models: string[]): ISceneModels {
    const modelFiles: ISceneModels = {}

    models.forEach((url) => {
      if (url.includes('transform') && url.endsWith('.json')) {
        modelFiles.transformUrl = url
      } else if (url.includes('point_cloud') && url.endsWith('.splat')) {
        modelFiles.splatUrl = url
      } else if (url.includes('settings') && url.endsWith('.json')) {
        modelFiles.settingsUrl = url
      }
    })

    return modelFiles
  }

  /**
   * 将Transform配置转换为相机配置
   * @param transformData Transform配置数据
   * @returns 相机配置
   */
  static parseTransformToCamera(transformData: ITransformCameraData[]): CameraConfig | null {
    try {
      if (transformData && transformData.length > 0) {
        console.log(`[SceneParser] 解析Transform配置:`, transformData)
        const transform = transformData[0]
        const cameraConfig: CameraConfig = {}

        if (transform.position) {
          cameraConfig.position = transform.position as [number, number, number]
        }
        if (transform.at) {
          cameraConfig.lookAt = transform.at as [number, number, number]
        }
        if (transform.up) {
          cameraConfig.lookUp = transform.up as [number, number, number]
        }

        console.log(`[SceneParser] 解析Transform配置完成:`, cameraConfig)
        return cameraConfig
      }
    } catch (error) {
      console.error('解析Transform配置失败:', error)
    }

    return null
  }

  /**
   * 解析Settings配置文件中的相机配置
   * @param settingsData Settings配置数据
   * @returns 相机配置
   */
  static parseSettingsToCamera(settingsData: any): CameraConfig | null {
    try {
      if (settingsData && settingsData.camera) {
        console.log(`[SceneParser] 解析Settings配置:`, settingsData.camera)
        const camera = settingsData.camera
        const cameraConfig: CameraConfig = {}

        if (camera.position) {
          cameraConfig.position = camera.position as [number, number, number]
        }
        if (camera.target) {
          cameraConfig.lookAt = camera.target as [number, number, number]
        }
        if (camera.fov) {
          cameraConfig.fov = camera.fov
        }

        console.log(`[SceneParser] 解析Settings配置完成:`, cameraConfig)
        return cameraConfig
      }
    } catch (error) {
      console.error('解析Settings配置失败:', error)
    }

    return null
  }

  /**
   * 获取有效的模型URL（从解析后的模型文件中）
   * @param models 解析后的模型文件
   * @returns 有效的splat模型URL
   */
  static getEffectiveModelUrl(models: ISceneModels): string | null {
    if (models.splatUrl) {
      console.log(`[SceneParser] 找到splat模型文件: ${models.splatUrl}`)
      return models.splatUrl
    }

    console.warn(`[SceneParser] 未找到有效的splat模型文件`)
    return null
  }

  static getEffectiveProjectId(projectName: string | null): string | null {
    if (!projectName || projectName === '') return null;
    const effectiveProjects: Record<string, string> = {
      '天坛': '2148',
      '樱花泉谷': '2126'
    }
    if (Object.keys(effectiveProjects).includes(projectName)) {
      return effectiveProjects[projectName]
    }
    return null;
  }
}
