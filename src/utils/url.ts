/**
 * URL编码解码工具函数
 */

/**
 * 对模型URL进行编码
 * @param url 原始URL
 * @returns 编码后的字符串
 */
export function encodeModelUrl(url: string): string {
  try {
    // 使用encodeURIComponent编码
    return encodeURIComponent(url)
  } catch (error) {
    console.error('URL编码失败:', error)
    return ''
  }
}

/**
 * 对编码后的模型URL进行解码
 * @param encodedUrl 编码后的URL
 * @returns 解码后的原始URL
 */
export function decodeModelUrl(encodedUrl: string): string {
  try {
    // 使用decodeURIComponent解码
    return decodeURIComponent(encodedUrl)
  } catch (error) {
    console.error('URL解码失败:', error)
    return ''
  }
}

/**
 * 从URL查询参数中获取参数值
 * @param key 参数键名
 * @param defaultValue 默认值
 * @returns 参数值
 */
export function getUrlParam(key: string, defaultValue = ''): string {
  const url = new URL(window.location.href)
  return url.searchParams.get(key) || defaultValue
}

/**
 * 获取解码后的模型URL
 * @returns 解码后的模型URL，如果获取失败返回空字符串
 */
export function getDecodedModelUrl(): string {
  const encodedUrl = getUrlParam('model')
  if (!encodedUrl) {
    console.warn('URL参数中未找到model')
    return ''
  }
  return decodeModelUrl(encodedUrl)
}
