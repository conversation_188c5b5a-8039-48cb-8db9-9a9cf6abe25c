import axios, {
  type AxiosPromise,
  type AxiosResponse,
  type AxiosInstance,
  type Method,
  type ResponseType,
  type AxiosProgressEvent,
  type CancelToken,
} from 'axios'
// 简单的消息提示函数
const Message = {
  error: (message: string) => {
    console.error('[HTTP Error]:', message)
    // 可选：显示浏览器通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('请求错误', { body: message })
    }
  },
}

// 环境变量配置
const NODE_ENV: string = import.meta.env.MODE || 'development'
const baseUrl = 'https://unicity3dev-api.bdnrc.org.cn'

const instance: AxiosInstance = axios.create({
  baseURL: NODE_ENV === 'production' ? '' : baseUrl,
  timeout: 15000,
  responseType: 'json',
  // withCredentials: true,
  headers: { 'Content-Type': 'application/json' },
})

function errHandler(err: any) {
  // 登陆、网络错误等问题
  if (err && err.status) {
    let errorMessage = ''
    if (err.status >= 500) {
      errorMessage = '服务器开小差了'
    } else if (err.status === 408) {
      errorMessage = '当前网络不可用，请检查网络设置'
    } else if (err.status === 403) {
      errorMessage = '当前用户无权限'
    } else if (err.status === 400) {
      errorMessage = err.data.message || 'error'
    } else if (err.status === 401) {
      errorMessage = err.data.message || 'error'
    }
    if (errorMessage) {
      Message.error(errorMessage)
    }
  }
  return Promise.reject(err)
}

instance.interceptors.request.use(
  (config: any) => {
    return config
  },
  (err) => {
    return Promise.reject(err)
  },
)

instance.interceptors.response.use(
  (res: AxiosResponse) => {
    return res
  },
  (err) => {
    console.error(err)
    if (err.response) {
      return errHandler(err.response)
    } else {
      const message = err.message || '当前网络不可用，请检查网络设置'
      Message.error(message)
    }
  },
)

interface IReqParams {
  url: string
  method: Method
  data: any
  params?: any
  timeout?: number
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
  cancelToken?: CancelToken
  responseType?: ResponseType
}

function request<T>({
  method = 'get',
  url,
  data = {},
  params,
  timeout,
  onUploadProgress,
  cancelToken,
  responseType,
}: IReqParams): AxiosPromise<T> {
  return instance.request<T>({
    method,
    url,
    data,
    params: (method.toUpperCase() === 'GET' && data) || params,
    timeout: timeout || 15000,
    responseType,
    onUploadProgress: function (progressEvent) {
      if (onUploadProgress && typeof onUploadProgress === 'function') {
        onUploadProgress(progressEvent)
      }
    },
    cancelToken,
  })
}

export default request
